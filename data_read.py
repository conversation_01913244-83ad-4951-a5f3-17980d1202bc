from pyspark.sql import SparkSession

spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

# Replace with your actual file path
parquet_file_path = "/Users/<USER>/Downloads/part-00000-c85d5c3f-03bf-4b5b-bf60-c0beaa070ee8-c000.snappy.parquet"
df = spark.read.parquet(parquet_file_path)
# df = df.filter(df["account_id"].isin([804092, 802392]))

df.coalesce(1).write.mode('overwrite').csv('/Users/<USER>/Desktop/playground/resources/txn_batches_26', header=True)
# Show schema or data
df.printSchema()
df.show(truncate=False)
