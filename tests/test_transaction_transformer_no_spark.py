import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestTransactionTransformerNoSpark:
    """Test TransactionTransformer without Spark dependencies."""

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_initialization_with_default_config(self, mock_date_utils, mock_operations, 
                                               mock_io_utils, mock_spark_utils, mock_logger):
        """Test TransactionTransformer initialization with default configuration."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {"all_transaction_file_path": "all_transactions"},
            "trading_competition": {"start_time": "2025-01-01 00:00:00.000"},
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
            "start_asset_position_path": "start_positions",
            "global_stock_splits_path": "stock_splits",
            "pluang_partner_id": 1,
            "usdt_coin_id": 825,
            "prices": {
                "forex": {"price_path": "forex_prices"},
                "gold": {"price_path": "gold_prices"},
                "global_stock": {"price_path": "global_stock_prices"},
                "crypto_currency": {"price_path": "crypto_prices"},
                "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
                "fund": {"price_path": "fund_prices"},
                "global_stock_options": {
                    "price_path": "options_prices",
                    "price_snapshot_folder": "snapshots"
                }
            }
        }
        
        # Initialize TransactionTransformer
        transformer = TransactionTransformer(config)
        
        # Verify initialization
        assert transformer.config == config
        assert transformer.bucket_path == "s3a://test-bucket"
        assert transformer.partner_id == 1
        assert transformer.usdt_coin_id == 825
        
        # Verify paths are constructed correctly
        expected_tier_path = "s3a://test-bucket/tier_snapshots"
        expected_txn_path = "s3a://test-bucket/all_transactions/"
        expected_crypto_rebrand_path = "s3a://test-bucket/crypto_rebrand_transactions/"
        expected_hdfs_path = "hdfs:///all_transactions/"
        expected_snapshot_path = "s3a://test-bucket/snapshots/"
        expected_start_position_path = "s3a://test-bucket/start_positions/"
        
        assert transformer.tier_snapshot_path == expected_tier_path
        assert transformer.transactions_overwrite_path == expected_txn_path
        assert transformer.crypto_rebrand_txn_path == expected_crypto_rebrand_path
        assert transformer.transactions_overwrite_hdfs_path == expected_hdfs_path
        assert transformer.transactions_snapshot_path == expected_snapshot_path
        assert transformer.start_asset_position_path == expected_start_position_path
        
        # Verify utility objects are created
        mock_spark_utils.assert_called_once_with("Transaction Transformer")
        mock_io_utils.assert_called_once()
        mock_operations.assert_called_once()

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_initialization_with_custom_utc_cutoff(self, mock_date_utils, mock_operations, 
                                                  mock_io_utils, mock_spark_utils, mock_logger):
        """Test TransactionTransformer initialization with custom UTC cutoff."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        custom_cutoff = datetime(2025, 1, 20, 10, 30, 0)
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {"all_transaction_file_path": "all_transactions"},
            "trading_competition": {"start_time": "2025-01-01 00:00:00.000"},
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
            "start_asset_position_path": "start_positions",
            "global_stock_splits_path": "stock_splits",
            "pluang_partner_id": 1,
            "usdt_coin_id": 825,
            "utc_cutoff_ts": custom_cutoff,
            "prices": {
                "forex": {"price_path": "forex_prices"},
                "gold": {"price_path": "gold_prices"},
                "global_stock": {"price_path": "global_stock_prices"},
                "crypto_currency": {"price_path": "crypto_prices"},
                "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
                "fund": {"price_path": "fund_prices"},
                "global_stock_options": {
                    "price_path": "options_prices",
                    "price_snapshot_folder": "snapshots"
                }
            }
        }
        
        # Initialize TransactionTransformer
        transformer = TransactionTransformer(config)
        
        # Verify custom UTC cutoff is used
        assert transformer.utc_cutoff_ts == custom_cutoff
        
        # Verify DateUtils.get_utc_timestamp was not called since custom cutoff was provided
        mock_date_utils.get_utc_timestamp.assert_not_called()

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_date_calculations(self, mock_date_utils, mock_operations, 
                              mock_io_utils, mock_spark_utils, mock_logger):
        """Test date calculations during initialization."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {"all_transaction_file_path": "all_transactions"},
            "trading_competition": {"start_time": "2025-01-01 00:00:00.000"},
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
            "start_asset_position_path": "start_positions",
            "global_stock_splits_path": "stock_splits",
            "pluang_partner_id": 1,
            "usdt_coin_id": 825,
            "prices": {
                "forex": {"price_path": "forex_prices"},
                "gold": {"price_path": "gold_prices"},
                "global_stock": {"price_path": "global_stock_prices"},
                "crypto_currency": {"price_path": "crypto_prices"},
                "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
                "fund": {"price_path": "fund_prices"},
                "global_stock_options": {
                    "price_path": "options_prices",
                    "price_snapshot_folder": "snapshots"
                }
            }
        }
        
        # Initialize TransactionTransformer
        transformer = TransactionTransformer(config)
        
        # Verify date utility methods were called correctly
        mock_date_utils.get_utc_timestamp_from_string.assert_called_once_with("2025-01-01 00:00:00.000")
        mock_date_utils.get_tc_dates_and_timestamp.assert_called_once()
        
        # Verify date attributes are set
        assert transformer.trading_competition_start_time == datetime(2025, 1, 1, 0, 0, 0)
        assert transformer.t_1 == datetime(2025, 1, 15).date()
        assert transformer.h_1 == 14
        assert transformer.t_2 == datetime(2025, 1, 14).date()
        assert transformer.h_2 == 14
        assert transformer.dt_1 == "2025-01-15T14:00:00.000Z"
        assert transformer.dt_2 == "2025-01-14T17:00:00.000Z"

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_logger_info_calls(self, mock_date_utils, mock_operations, 
                              mock_io_utils, mock_spark_utils, mock_logger):
        """Test that logger info calls are made during initialization."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {"all_transaction_file_path": "all_transactions"},
            "trading_competition": {"start_time": "2025-01-01 00:00:00.000"},
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
            "start_asset_position_path": "start_positions",
            "global_stock_splits_path": "stock_splits",
            "pluang_partner_id": 1,
            "usdt_coin_id": 825,
            "prices": {
                "forex": {"price_path": "forex_prices"},
                "gold": {"price_path": "gold_prices"},
                "global_stock": {"price_path": "global_stock_prices"},
                "crypto_currency": {"price_path": "crypto_prices"},
                "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
                "fund": {"price_path": "fund_prices"},
                "global_stock_options": {
                    "price_path": "options_prices",
                    "price_snapshot_folder": "snapshots"
                }
            }
        }
        
        # Initialize TransactionTransformer
        transformer = TransactionTransformer(config)
        
        # Verify logger info was called with date information
        mock_logger_instance.info.assert_called()
        
        # Check that the info call contains the expected date information
        info_calls = mock_logger_instance.info.call_args_list
        assert len(info_calls) > 0
        
        # The info call should contain date information
        info_message = info_calls[0][0][0]  # First call, first argument
        assert "utc_cutoff_ts" in info_message
        assert "t_1" in info_message
        assert "h_1" in info_message
        assert "t_2" in info_message
        assert "h_2" in info_message
        assert "dt_1" in info_message
        assert "dt_2" in info_message

    def test_class_attributes_exist(self):
        """Test that TransactionTransformer class has all expected attributes and methods."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Check that class exists
        assert TransactionTransformer is not None
        
        # Check that expected methods exist
        expected_methods = [
            '__init__',
            'get_all_prices',
            'update_executed_quantity_splits',
            'get_user_tiers',
            'get_crypto_currency_transactions',
            'get_global_stock_transactions',
            'get_crypto_future_transactions',
            'get_options_transactions',
            'get_fund_transactions',
            'get_forex_transactions',
            'get_gold_transactions',
            'add_current_unit_price',
            'cast_fields',
            'execute',
            'run'
        ]
        
        for method_name in expected_methods:
            assert hasattr(TransactionTransformer, method_name), f"Method {method_name} not found"
            assert callable(getattr(TransactionTransformer, method_name)), f"Method {method_name} is not callable"
