import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestFlashGamesPnL:
    """Test class for FlashGamesPnL functionality."""

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_init(self, mock_date_utils, mock_operations, mock_io_utils, 
                  mock_spark_utils, mock_logger, mock_config, spark_session):
        """Test FlashGamesPnL initialization."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Initialize FlashGamesPnL
        flash_games_pnl = FlashGamesPnL(mock_config)
        
        # Assertions
        assert flash_games_pnl.config == mock_config
        assert flash_games_pnl.flash_games == mock_config["flash_games"]
        assert flash_games_pnl.current_flash_game is None
        assert flash_games_pnl.current_flash_game_id is None
        assert flash_games_pnl.trading_competition_id == "TC_2025_Q1"
        
        # Verify utility objects are created
        mock_spark_utils.assert_called_once_with("Flash Games PnL")
        mock_io_utils.assert_called_once()
        mock_operations.assert_called_once()

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_get_current_flash_game_active(self, mock_date_utils, mock_operations, 
                                          mock_io_utils, mock_spark_utils, mock_logger, 
                                          mock_config, spark_session):
        """Test get_current_flash_game when there's an active game."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp_from_string.side_effect = [
            datetime(2025, 1, 1, 0, 0, 0),  # trading competition start
            datetime(2025, 1, 15, 10, 0, 0),  # flash game start
            datetime(2025, 1, 15, 18, 0, 0),   # flash game end
            datetime(2025, 1, 15, 19, 0, 0)   # flash game schedule end
        ]
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        flash_games_pnl = FlashGamesPnL(mock_config)
        
        # Test get_current_flash_game
        current_game = flash_games_pnl.get_current_flash_game()
        
        assert current_game is not None
        assert current_game["flash_game_name"] == "game_1"
        assert flash_games_pnl.flash_game_start_ts == datetime(2025, 1, 15, 10, 0, 0)
        assert flash_games_pnl.flash_game_end_ts == datetime(2025, 1, 15, 18, 0, 0)

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_get_current_flash_game_none_active(self, mock_date_utils, mock_operations, 
                                               mock_io_utils, mock_spark_utils, mock_logger, 
                                               spark_session):
        """Test get_current_flash_game when no game is active."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Config with no active flash games
        config_no_games = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {
                "batch_file_path": "batches",
                "flash_games_batch_file_path": "flash_games_batches",
                "all_transaction_file_path": "all_transactions"
            },
            "trading_competition": {
                "id": "TC_2025_Q1",
                "start_time": "2025-01-01 00:00:00.000",
                "frequency": 24
            },
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "flash_games": {},  # No flash games
            "flash_games_assets_path": "flash_games_assets",
            "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
            "offset": 0,
            "execution_time": "jkt_day_end"
        }
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        flash_games_pnl = FlashGamesPnL(config_no_games)
        
        # Test get_current_flash_game
        current_game = flash_games_pnl.get_current_flash_game()
        
        assert current_game is None

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_get_live_options_contracts_data(self, mock_date_utils, mock_operations, 
                                            mock_io_utils, mock_spark_utils, mock_logger, 
                                            mock_config, spark_session, sample_options_data):
        """Test get_live_options_contracts_data method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_io_utils.return_value.read_parquet_data.return_value = sample_options_data
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        flash_games_pnl = FlashGamesPnL(mock_config)
        
        # Test with global_stock_ids
        result = flash_games_pnl.get_live_options_contracts_data([1, 2])
        
        assert "global_stock_options" in result
        assert isinstance(result["global_stock_options"], list)
        
        # Test with empty global_stock_ids
        result_empty = flash_games_pnl.get_live_options_contracts_data([])
        assert "global_stock_options" in result_empty

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_get_global_stocks_leverage_data(self, mock_date_utils, mock_operations, 
                                           mock_io_utils, mock_spark_utils, mock_logger, 
                                           mock_config, spark_session, sample_leverage_stocks_data):
        """Test get_global_stocks_leverage_data method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_io_utils.return_value.read_from_kafka_in_memory.return_value = sample_leverage_stocks_data
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        flash_games_pnl = FlashGamesPnL(mock_config)
        
        # Test get_global_stocks_leverage_data
        result = flash_games_pnl.get_global_stocks_leverage_data()
        
        assert "global_stocks" in result
        assert isinstance(result["global_stocks"], list)

    def test_get_all_eligible_transactions(self, spark_session, sample_asset_data, sample_transactions_data):
        """Test get_all_eligible_transactions method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Create a minimal FlashGamesPnL instance for testing this method
        with patch('src.jobs.trading_competition.flash_games_pnl.get_logger'), \
             patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.IOUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.Operations'), \
             patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:
            
            mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
            mock_date_utils.get_tc_dates_and_timestamp.return_value = (
                datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
                "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
            )
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "snapshot_path": "snapshots",
                "batches": {
                    "batch_file_path": "batches",
                    "flash_games_batch_file_path": "flash_games_batches",
                    "all_transaction_file_path": "all_transactions"
                },
                "trading_competition": {
                    "id": "TC_2025_Q1",
                    "start_time": "2025-01-01 00:00:00.000",
                    "frequency": 24
                },
                "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
                "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
                "offset": 0,
                "execution_time": "jkt_day_end"
            }
            flash_games_pnl = FlashGamesPnL(config)
            flash_games_pnl.spark = spark_session
            flash_games_pnl.logger = Mock()
            
            # Test the method
            result = flash_games_pnl.get_all_eligible_transactions(sample_asset_data, sample_transactions_data)
            
            # Verify result structure
            assert result is not None
            assert "account_id" in result.columns
            assert "asset_id" in result.columns
            assert "asset_type" in result.columns
            
            # Check that we get some results
            count = result.count()
            assert count > 0

    def test_create_batches_static_method(self):
        """Test the static create_batches method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Create mock transaction data that behaves like PySpark Row objects
        class MockRow:
            def __init__(self, data):
                self._data = data
                # Make the object subscriptable for the sorting key
                for key, value in data.items():
                    setattr(self, key, value)

            def __getitem__(self, key):
                return self._data[key]

            def asDict(self):
                return self._data.copy()

        txn_data_1 = {
            "row_number": 1,
            "currency_to_idr": 15000.0,
            "updated_executed_unit_price": 50.0,
            "updated_executed_quantity": 100.0,
            "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 52.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }

        txn_data_2 = {
            "row_number": 2,
            "currency_to_idr": 15200.0,
            "updated_executed_unit_price": 55.0,
            "updated_executed_quantity": 50.0,
            "transaction_time": datetime(2025, 1, 15, 12, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "SELL",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 5.0,
            "current_unit_price": 55.0,
            "current_currency_to_idr": 15200,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }

        all_txn = [MockRow(txn_data_1), MockRow(txn_data_2)]
        buy_types = ["BUY"]
        sell_types = ["SELL"]

        # Test the static method
        result = FlashGamesPnL.create_batches(all_txn, buy_types, sell_types)

        assert isinstance(result, list)
        assert len(result) == 2

        # Check that PnL calculations are performed
        for txn in result:
            assert "realized_pnl" in txn
            assert "unrealized_pnl" in txn
            assert "remaining_quantity" in txn
            assert "is_pnl_eligible" in txn

    def test_cast_fields(self, spark_session):
        """Test cast_fields method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Create test data
        data = [
            (1001, 50.*********, 100.*********, 49.*********, 45.*********, 15000, 15100, 1000, 500)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("current_unit_price", DoubleType(), True),
            StructField("remaining_quantity", DoubleType(), True),
            StructField("updated_executed_quantity", DoubleType(), True),
            StructField("updated_executed_unit_price", DoubleType(), True),
            StructField("current_currency_to_idr", LongType(), True),
            StructField("currency_to_idr", LongType(), True),
            StructField("realized_pnl", LongType(), True),
            StructField("unrealized_pnl", LongType(), True)
        ])
        
        df = spark_session.createDataFrame(data, schema)
        
        # Create a minimal FlashGamesPnL instance
        with patch('src.jobs.trading_competition.flash_games_pnl.get_logger'), \
             patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.IOUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.Operations'), \
             patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:
            
            mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
            mock_date_utils.get_tc_dates_and_timestamp.return_value = (
                datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
                "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
            )
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "snapshot_path": "snapshots",
                "batches": {
                    "batch_file_path": "batches",
                    "flash_games_batch_file_path": "flash_games_batches",
                    "all_transaction_file_path": "all_transactions"
                },
                "trading_competition": {
                    "id": "TC_2025_Q1",
                    "start_time": "2025-01-01 00:00:00.000",
                    "frequency": 24
                },
                "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
                "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
                "offset": 0,
                "execution_time": "jkt_day_end"
            }
            flash_games_pnl = FlashGamesPnL(config)

            # Test cast_fields
            result = flash_games_pnl.cast_fields(df)
            
            # Verify the result has the expected columns and types
            assert "current_unit_price" in result.columns
            assert "remaining_quantity" in result.columns
            
            # Check that rounding was applied (values should be rounded to 12 decimal places)
            row = result.collect()[0]
            assert abs(row["current_unit_price"] - 50.*********) < 1e-10

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_process_flash_game_pnl(self, mock_date_utils, mock_operations,
                                   mock_io_utils, mock_spark_utils, mock_logger,
                                   mock_config, spark_session, sample_niv_data,
                                   sample_gtv_data):
        """Test process_flash_game_pnl method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_io_utils.return_value.read_csv_file.side_effect = [sample_niv_data, sample_gtv_data]
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        flash_games_pnl = FlashGamesPnL(mock_config)
        flash_games_pnl.current_flash_game_id = "game_1"

        # Create sample batches data
        batches_data = [
            (1001, 1000, 500),
            (1002, -200, 100)
        ]

        batches_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("realized_pnl", LongType(), True),
            StructField("unrealized_pnl", LongType(), True)
        ])

        batches = spark_session.createDataFrame(batches_data, batches_schema)

        # Test process_flash_game_pnl
        result = flash_games_pnl.process_flash_game_pnl(batches)

        # Verify result structure
        assert "account_id" in result.columns
        assert "pnl" in result.columns
        assert "pnl_rank" in result.columns
        assert "trading_competition_id" in result.columns
        assert "flash_game_id" in result.columns

        # Check that ranking is applied
        result_list = result.orderBy("pnl_rank").collect()
        assert len(result_list) > 0

        # Highest PnL should have rank 1
        assert result_list[0]["pnl_rank"] == 1

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_write_flash_game_pnl_to_mongo(self, mock_date_utils, mock_operations,
                                          mock_io_utils, mock_spark_utils, mock_logger,
                                          mock_config, spark_session, sample_user_details_data):
        """Test write_flash_game_pnl_to_mongo method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_io_utils.return_value.read_csv_file.return_value = sample_user_details_data
        mock_io_utils.return_value.get_mongo_connection_string.return_value = "*******************************************"
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        flash_games_pnl = FlashGamesPnL(mock_config)
        flash_games_pnl.current_flash_game_id = "game_1"
        flash_games_pnl.flash_game_start_ts = datetime(2025, 1, 15, 10, 0, 0)

        # Create sample PnL data
        pnl_data = [
            (1001, 1500, 1200000.0, 1, "game_1"),
            (1002, -100, 600000.0, 2, "game_1")
        ]

        pnl_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("pnl", LongType(), True),
            StructField("total_gtv", DoubleType(), True),
            StructField("pnl_rank", IntegerType(), True),
            StructField("flash_game_id", StringType(), True)
        ])

        pnl_df = spark_session.createDataFrame(pnl_data, pnl_schema)

        # Test write_flash_game_pnl_to_mongo
        flash_games_pnl.write_flash_game_pnl_to_mongo(pnl_df)

        # Verify that write_dataset_to_mongo was called
        mock_io_utils.return_value.write_dataset_to_mongo.assert_called_once()

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_execute_no_active_game(self, mock_date_utils, mock_operations,
                                   mock_io_utils, mock_spark_utils, mock_logger,
                                   spark_session):
        """Test execute method when no active flash game."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Config with no active flash games
        config_no_games = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {
                "batch_file_path": "batches",
                "flash_games_batch_file_path": "flash_games_batches",
                "all_transaction_file_path": "all_transactions"
            },
            "trading_competition": {
                "id": "TC_2025_Q1",
                "start_time": "2025-01-01 00:00:00.000",
                "frequency": 24
            },
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "flash_games": {},  # No flash games
            "flash_games_assets_path": "flash_games_assets",
            "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
            "offset": 0,
            "execution_time": "jkt_day_end"
        }

        # Setup mocks
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        flash_games_pnl = FlashGamesPnL(config_no_games)

        # Test execute
        flash_games_pnl.execute()

        # Verify that it logs and exits gracefully
        mock_logger_instance.info.assert_called_with(
            "No active flash game found for the current UTC timestamp: {}. Exiting gracefully.".format(
                datetime(2025, 1, 15, 14, 0, 0)
            )
        )

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_run_method(self, mock_date_utils, mock_operations,
                       mock_io_utils, mock_spark_utils, mock_logger,
                       mock_config, spark_session):
        """Test run method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        flash_games_pnl = FlashGamesPnL(mock_config)

        # Mock the execute method
        flash_games_pnl.execute = Mock()

        # Test run
        flash_games_pnl.run()

        # Verify execute was called and spark was stopped
        flash_games_pnl.execute.assert_called_once()
        mock_spark_utils_instance.stop_spark.assert_called_once_with(spark_session)

    def test_create_initial_position(self, spark_session):
        """Test create_initial_position method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Create test transaction data
        data = [
            (1001, 101, 1, 1, "global_stocks", 100.0, "BUY", datetime(2025, 1, 14, 10, 0, 0), "global_stock_transactions"),
            (1001, 101, 1, 1, "global_stocks", 50.0, "SELL", datetime(2025, 1, 14, 11, 0, 0), "global_stock_transactions"),
            (1002, 102, 2, 1, "crypto_currency", 1000.0, "BUY", datetime(2025, 1, 14, 12, 0, 0), "crypto_currency_transactions"),
            # This should be filtered out (crypto_future_funding_transactions)
            (1003, 103, 3, 1, "crypto_futures", 500.0, "BUY", datetime(2025, 1, 14, 13, 0, 0), "crypto_future_funding_transactions")
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("asset_id", LongType(), True),
            StructField("leverage", LongType(), True),
            StructField("asset_type", StringType(), True),
            StructField("updated_executed_quantity", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("asset_sub_type", StringType(), True)
        ])

        df = spark_session.createDataFrame(data, schema)

        # Create a minimal FlashGamesPnL instance
        with patch('src.jobs.trading_competition.flash_games_pnl.get_logger'), \
             patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.IOUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.Operations'), \
             patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:

            mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
            mock_date_utils.get_tc_dates_and_timestamp.return_value = (
                datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
                "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
            )

            config = {
                "bucket_path": "s3a://test-bucket",
                "snapshot_path": "snapshots",
                "batches": {
                    "batch_file_path": "batches",
                    "flash_games_batch_file_path": "flash_games_batches",
                    "all_transaction_file_path": "all_transactions"
                },
                "trading_competition": {
                    "id": "TC_2025_Q1",
                    "start_time": "2025-01-01 00:00:00.000",
                    "frequency": 24
                },
                "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
                "buy_types": ["BUY", "AIRDROP_BUY"],
                "sell_types": ["SELL", "AIRDROP_SELL"],
                "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
                "offset": 0,
                "execution_time": "jkt_day_end"
            }
            flash_games_pnl = FlashGamesPnL(config)
            flash_games_pnl.spark = spark_session
            flash_games_pnl.logger = Mock()
            flash_games_pnl.flash_game_start_ts = datetime(2025, 1, 15, 10, 0, 0)

            # Test create_initial_position
            result = flash_games_pnl.create_initial_position(df)

            # Verify result structure
            assert "account_id" in result.columns
            assert "updated_executed_quantity" in result.columns
            assert "transaction_type" in result.columns

            # Check that INITIAL_ASSET_BALANCE transactions are created
            result_list = result.collect()
            for row in result_list:
                assert row["transaction_type"] == "INITIAL_ASSET_BALANCE"
                assert row["transaction_time"] == datetime(2025, 1, 15, 10, 0, 0)

            # Check that crypto_future_funding_transactions are filtered out
            account_ids = [row["account_id"] for row in result_list]
            assert 1003 not in account_ids  # Should be filtered out

    def test_get_current_flash_game_asset_id_with_options_and_leverage(self, spark_session):
        """Test get_current_flash_game_asset_id with options and leverage enabled."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Mock data for options and leverage
        options_data = spark_session.createDataFrame(
            [(1001, 1), (1002, 2)],
            StructType([StructField("id", LongType(), True), StructField("global_stock_id", LongType(), True)])
        )

        leverage_data = spark_session.createDataFrame(
            [({"id": "1", "status": "ACTIVE", "stock_type": "CFD_LEVERAGE"},),
             ({"id": "2", "status": "ACTIVE", "stock_type": "CFD_LEVERAGE"},)],
            StructType([StructField("value", MapType(StringType(), StringType()), True)])
        )

        with patch('src.jobs.trading_competition.flash_games_pnl.get_logger'), \
             patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.IOUtils') as mock_io_utils, \
             patch('src.jobs.trading_competition.flash_games_pnl.Operations'), \
             patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:

            mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
            mock_date_utils.get_tc_dates_and_timestamp.return_value = (
                datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
                "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
            )

            # Mock IO operations
            mock_io_utils.return_value.read_parquet_data.return_value = options_data
            mock_io_utils.return_value.read_from_kafka_in_memory.return_value = leverage_data

            config = {
                "bucket_path": "s3a://test-bucket",
                "snapshot_path": "snapshots",
                "batches": {
                    "batch_file_path": "batches",
                    "flash_games_batch_file_path": "flash_games_batches",
                    "all_transaction_file_path": "all_transactions"
                },
                "trading_competition": {
                    "id": "TC_2025_Q1",
                    "start_time": "2025-01-01 00:00:00.000",
                    "frequency": 24
                },
                "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
                "bootstrap_servers": "localhost:9092",
                "kafka_topics": {"global_stock_topic": "global_stocks"},
                "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
                "offset": 0,
                "execution_time": "jkt_day_end"
            }

            flash_games_pnl = FlashGamesPnL(config)
            flash_games_pnl.spark = spark_session
            flash_games_pnl.logger = Mock()
            flash_games_pnl.current_flash_game = {
                "assets": {
                    "global_stocks": [1, 2],
                    "global_stock_options": [],  # Empty list means enabled
                    "global_stock_with_leverage": []  # Empty list means enabled
                }
            }

            # Test get_current_flash_game_asset_id
            result = flash_games_pnl.get_current_flash_game_asset_id()

            # Verify result structure
            assert "asset_type" in result.columns
            assert "asset_id" in result.columns

            # Check that we get results for different asset types
            result_list = result.collect()
            asset_types = [row["asset_type"] for row in result_list]

            # Should include original global_stocks, options, and leverage stocks
            assert "global_stocks" in asset_types
            assert len([at for at in asset_types if at == "global_stocks"]) >= 2  # Original + leverage

    def test_edge_case_empty_transactions(self, spark_session):
        """Test handling of empty transaction data."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Create empty DataFrame with correct schema for assets
        asset_schema = StructType([
            StructField("asset_type", StringType(), True),
            StructField("asset_id", LongType(), True)
        ])

        # Create empty DataFrame with correct schema for transactions
        transaction_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("asset_id", LongType(), True),
            StructField("leverage", LongType(), True),
            StructField("fees", DoubleType(), True),
            StructField("asset_type", StringType(), True),
            StructField("transaction_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("updated_executed_quantity", DoubleType(), True),
            StructField("updated_executed_unit_price", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("currency_to_idr", DoubleType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("asset_sub_type", StringType(), True),
            StructField("current_unit_price", DoubleType(), True),
            StructField("current_currency_to_idr", LongType(), True)
        ])

        empty_asset_df = spark_session.createDataFrame([], asset_schema)
        empty_transaction_df = spark_session.createDataFrame([], transaction_schema)

        with patch('src.jobs.trading_competition.flash_games_pnl.get_logger'), \
             patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.IOUtils'), \
             patch('src.jobs.trading_competition.flash_games_pnl.Operations'), \
             patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:

            mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
            mock_date_utils.get_tc_dates_and_timestamp.return_value = (
                datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
                "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
            )

            config = {
                "bucket_path": "s3a://test-bucket",
                "snapshot_path": "snapshots",
                "batches": {
                    "batch_file_path": "batches",
                    "flash_games_batch_file_path": "flash_games_batches",
                    "all_transaction_file_path": "all_transactions"
                },
                "trading_competition": {
                    "id": "TC_2025_Q1",
                    "start_time": "2025-01-01 00:00:00.000",
                    "frequency": 24
                },
                "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
                "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
                "offset": 0,
                "execution_time": "jkt_day_end"
            }
            flash_games_pnl = FlashGamesPnL(config)
            flash_games_pnl.spark = spark_session
            flash_games_pnl.logger = Mock()

            # Test get_all_eligible_transactions with empty data
            result = flash_games_pnl.get_all_eligible_transactions(empty_asset_df, empty_transaction_df)

            # Should handle empty data gracefully
            assert result is not None
            assert result.count() == 0
