import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

@pytest.mark.integration
class TestFlashGamesPnLIntegration:
    """Integration tests for FlashGamesPnL class."""

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_full_execution_flow(self, mock_date_utils, mock_operations, 
                                mock_io_utils, mock_spark_utils, mock_logger, 
                                spark_session):
        """Test the complete execution flow with realistic data."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Setup comprehensive mock config
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {
                "batch_file_path": "batches",
                "flash_games_batch_file_path": "flash_games_batches",
                "all_transaction_file_path": "all_transactions"
            },
            "trading_competition": {
                "id": "TC_2025_Q1",
                "start_time": "2025-01-01 00:00:00.000",
                "frequency": 24
            },
            "aum_tier_upgrade": {
                "tier_snapshot_path": "tier_snapshots"
            },
            "flash_games": {
                "crypto_flash_game": {
                    "start_ts": "2025-01-15 10:00:00.000",
                    "end_ts": "2025-01-15 18:00:00.000",
                    "schedule_end_ts": "2025-01-15 19:00:00.000",
                    "assets": {
                        "crypto_currency": [101, 102],
                        "global_stocks": [1, 2]
                    }
                }
            },
            "buy_types": ["BUY", "AIRDROP_BUY"],
            "sell_types": ["SELL", "AIRDROP_SELL"],
            "bootstrap_servers": "localhost:9092",
            "kafka_topics": {"global_stock_topic": "global_stocks"},
            "niv_path": "niv",
            "gtv_path": "gtv",
            "flash_games_assets_path": "flash_games_assets",
            "flash_games_pnl_path": "flash_games_pnl",
            "data_store": {
                "reporting_mongo": {
                    "host": "localhost",
                    "port": 27017,
                    "database": "test_db",
                    "username": "test_user",
                    "password": "test_pass"
                },
                "flash_game": {
                    "collection": "flash_game_leaderboard"
                }
            },
            "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
            "offset": 0,
            "execution_time": "jkt_day_end"
        }
        
        # Create comprehensive test data
        transactions_data = [
            # User 1001 - Profitable trader
            (1001, 101, 101, 1, 0.0, "crypto_currency", 1001, 
             datetime(2025, 1, 15, 11, 0, 0), datetime(2025, 1, 15, 11, 0, 0),
             1000.0, 0.001, "BUY", 1.0, datetime(2025, 1, 15, 11, 0, 0),
             "crypto_currency_transactions", 0.0012, 1),
            (1001, 101, 101, 1, 5.0, "crypto_currency", 1002,
             datetime(2025, 1, 15, 12, 0, 0), datetime(2025, 1, 15, 12, 0, 0),
             500.0, 0.0012, "SELL", 1.0, datetime(2025, 1, 15, 12, 0, 0),
             "crypto_currency_transactions", 0.0012, 1),
            
            # User 1002 - Loss-making trader
            (1002, 102, 1, 1, 0.0, "global_stocks", 1003,
             datetime(2025, 1, 15, 13, 0, 0), datetime(2025, 1, 15, 13, 0, 0),
             100.0, 50.0, "BUY", 15000.0, datetime(2025, 1, 15, 13, 0, 0),
             "global_stock_transactions", 45.0, 15000),
            
            # Pre-flash game transactions for initial positions
            (1001, 101, 101, 1, 0.0, "crypto_currency", 1004,
             datetime(2025, 1, 14, 15, 0, 0), datetime(2025, 1, 14, 15, 0, 0),
             2000.0, 0.0009, "BUY", 1.0, datetime(2025, 1, 14, 15, 0, 0),
             "crypto_currency_transactions", 0.0012, 1),
        ]
        
        transactions_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("asset_id", LongType(), True),
            StructField("leverage", LongType(), True),
            StructField("fees", DoubleType(), True),
            StructField("asset_type", StringType(), True),
            StructField("transaction_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("updated_executed_quantity", DoubleType(), True),
            StructField("updated_executed_unit_price", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("currency_to_idr", DoubleType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("asset_sub_type", StringType(), True),
            StructField("current_unit_price", DoubleType(), True),
            StructField("current_currency_to_idr", LongType(), True)
        ])
        
        transactions_df = spark_session.createDataFrame(transactions_data, transactions_schema)
        
        # Create NIV data
        niv_data = spark_session.createDataFrame(
            [(1001, 1000000.0), (1002, 500000.0)],
            StructType([
                StructField("account_id", LongType(), True),
                StructField("invested_value", DoubleType(), True)
            ])
        )
        
        # Create GTV data
        gtv_data = spark_session.createDataFrame(
            [(1001, 1200000.0), (1002, 600000.0)],
            StructType([
                StructField("account_id", LongType(), True),
                StructField("total_gtv", DoubleType(), True)
            ])
        )
        
        # Create user details data
        user_details_data = spark_session.createDataFrame(
            [(1001, 101, "Alice Crypto", "<EMAIL>", "TC_2025_Q1"),
             (1002, 102, "Bob Stocks", "<EMAIL>", "TC_2025_Q1")],
            StructType([
                StructField("account_id", LongType(), True),
                StructField("user_id", LongType(), True),
                StructField("name", StringType(), True),
                StructField("email", StringType(), True),
                StructField("trading_competition_id", StringType(), True)
            ])
        )
        
        # Setup mocks
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        
        mock_io_utils_instance = Mock()
        mock_io_utils.return_value = mock_io_utils_instance
        mock_io_utils_instance.read_parquet_data.return_value = transactions_df
        mock_io_utils_instance.read_csv_file.side_effect = [niv_data, gtv_data, user_details_data]
        mock_io_utils_instance.write_parquet_file = Mock()
        mock_io_utils_instance.write_csv_file = Mock()
        mock_io_utils_instance.write_dataset_to_mongo = Mock()
        mock_io_utils_instance.get_mongo_connection_string.return_value = "*******************************************"
        
        mock_date_utils.get_utc_timestamp_from_string.side_effect = [
            datetime(2025, 1, 1, 0, 0, 0),    # trading competition start
            datetime(2025, 1, 15, 10, 0, 0),  # flash game start
            datetime(2025, 1, 15, 18, 0, 0),  # flash game end
            datetime(2025, 1, 15, 19, 0, 0)   # flash game schedule end
        ]
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Initialize and run FlashGamesPnL
        flash_games_pnl = FlashGamesPnL(config)
        
        # Execute the full flow
        flash_games_pnl.execute()
        
        # Verify the execution flow
        # 1. Check that current flash game was identified
        assert flash_games_pnl.current_flash_game is not None
        assert flash_games_pnl.current_flash_game_id == "crypto_flash_game"
        
        # 2. Verify file operations were called
        assert mock_io_utils_instance.read_parquet_data.called
        assert mock_io_utils_instance.write_parquet_file.called
        assert mock_io_utils_instance.write_csv_file.called
        assert mock_io_utils_instance.write_dataset_to_mongo.called
        
        # 3. Check that NIV and GTV data were read
        csv_calls = mock_io_utils_instance.read_csv_file.call_args_list
        assert len(csv_calls) >= 2  # At least NIV and GTV calls
        
        # 4. Verify MongoDB write was called with correct parameters
        mongo_call = mock_io_utils_instance.write_dataset_to_mongo.call_args
        assert mongo_call is not None
        
        # 5. Check logging
        assert mock_logger_instance.info.called
        
        # 6. Verify Spark session management
        mock_spark_utils_instance.create_spark_session.assert_called_once()

    @patch('src.jobs.trading_competition.flash_games_pnl.get_logger')
    @patch('src.jobs.trading_competition.flash_games_pnl.SparkUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.IOUtils')
    @patch('src.jobs.trading_competition.flash_games_pnl.Operations')
    @patch('src.jobs.trading_competition.flash_games_pnl.DateUtils')
    def test_run_method_integration(self, mock_date_utils, mock_operations, 
                                   mock_io_utils, mock_spark_utils, mock_logger, 
                                   spark_session):
        """Test the run method which calls execute and stops spark."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {
                "batch_file_path": "batches",
                "flash_games_batch_file_path": "flash_games_batches",
                "all_transaction_file_path": "all_transactions"
            },
            "trading_competition": {
                "id": "TC_2025_Q1",
                "start_time": "2025-01-01 00:00:00.000",
                "frequency": 24
            },
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "flash_games": {},
            "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),
            "offset": 0,
            "execution_time": "jkt_day_end"
        }
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Initialize FlashGamesPnL
        flash_games_pnl = FlashGamesPnL(config)
        
        # Run the complete flow
        flash_games_pnl.run()
        
        # Verify that Spark session was stopped
        mock_spark_utils_instance.stop_spark.assert_called_once_with(spark_session)

    def test_complex_pnl_calculation_scenarios(self, spark_session):
        """Test complex PnL calculation scenarios using the static create_batches method."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL

        # Create mock transaction data that behaves like PySpark Row objects
        class MockRow:
            def __init__(self, data):
                self._data = data
                # Make the object subscriptable for the sorting key
                for key, value in data.items():
                    setattr(self, key, value)

            def __getitem__(self, key):
                return self._data[key]

            def asDict(self):
                return self._data.copy()

        # Test scenario: Multiple buy/sell transactions with different prices
        txn_data_1 = {
            "row_number": 1,
            "currency_to_idr": 15000.0,
            "updated_executed_unit_price": 50.0,
            "updated_executed_quantity": 100.0,
            "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 55.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields that the method expects
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }

        txn_data_2 = {
            "row_number": 2,
            "currency_to_idr": 15100.0,
            "updated_executed_unit_price": 60.0,
            "updated_executed_quantity": 50.0,
            "transaction_time": datetime(2025, 1, 15, 12, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 55.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }

        txn_data_3 = {
            "row_number": 3,
            "currency_to_idr": 15200.0,
            "updated_executed_unit_price": 65.0,
            "updated_executed_quantity": 75.0,
            "transaction_time": datetime(2025, 1, 15, 13, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "SELL",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 5.0,
            "current_unit_price": 55.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }

        all_txn = [MockRow(txn_data_1), MockRow(txn_data_2), MockRow(txn_data_3)]
        buy_types = ["BUY"]
        sell_types = ["SELL"]

        # Test the complex PnL calculation
        result = FlashGamesPnL.create_batches(all_txn, buy_types, sell_types)

        assert len(result) == 3

        # Verify PnL calculations
        # First BUY should have unrealized PnL
        buy_1 = next(r for r in result if r["row_number"] == 1)
        assert buy_1["transaction_type"] == "BUY"
        assert "unrealized_pnl" in buy_1

        # Second BUY should also have unrealized PnL
        buy_2 = next(r for r in result if r["row_number"] == 2)
        assert buy_2["transaction_type"] == "BUY"
        assert "unrealized_pnl" in buy_2

        # SELL should have realized PnL calculated
        sell_1 = next(r for r in result if r["row_number"] == 3)
        assert sell_1["transaction_type"] == "SELL"
        assert "realized_pnl" in sell_1
