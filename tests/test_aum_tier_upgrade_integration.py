import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestAUMTierUpgradeProcessorIntegration:
    """Integration tests for AUMTierUpgradeProcessor."""

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_full_execution_flow(self, mock_date_utils, mock_operations, mock_io_utils, 
                                mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test complete execution flow of AUMTierUpgradeProcessor."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Create comprehensive test data
        
        # AUM data
        aum_data = [
            {"account_id": 1001, "portfolioValue": ********.0},   # CHALLENGER tier AUM
            {"account_id": 1002, "portfolioValue": *********.0},  # LEGEND tier AUM
            {"account_id": 1003, "portfolioValue": ********.0},   # GOLD tier AUM
            {"account_id": 1004, "portfolioValue": 2000000.0}     # SILVER tier AUM
        ]
        aum_df = spark_session.createDataFrame(aum_data)
        
        # Tier event data
        tier_event_data = [
            {
                "accountId": 1001, "userId": 101, "userAction": "OPT_IN",
                "currentTier": "CHALLENGER", "eligibleUpgradeTier": "LEGEND",
                "eventTime": datetime(2025, 1, 15, 10, 0, 0),
                "name": "John Doe", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1002, "userId": 102, "userAction": "ACCEPTED",
                "currentTier": "CHALLENGER", "eligibleUpgradeTier": "LEGEND",
                "eventTime": datetime(2025, 1, 15, 11, 0, 0),
                "name": "Jane Smith", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1003, "userId": 103, "userAction": "OPT_IN",
                "currentTier": "GOLD", "eligibleUpgradeTier": "CHALLENGER",
                "eventTime": datetime(2025, 1, 15, 12, 0, 0),
                "name": "Bob Johnson", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1003, "userId": 103, "userAction": "DECLINED",
                "currentTier": "GOLD", "eligibleUpgradeTier": "CHALLENGER",
                "eventTime": datetime(2025, 1, 15, 13, 0, 0),
                "name": "Bob Johnson", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1004, "userId": 104, "userAction": "OPT_IN",
                "currentTier": "SILVER", "eligibleUpgradeTier": "GOLD",
                "eventTime": datetime(2025, 1, 15, 14, 0, 0),
                "name": "Alice Brown", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1004, "userId": 104, "userAction": "IGNORED",
                "currentTier": "SILVER", "eligibleUpgradeTier": "GOLD",
                "eventTime": datetime(2025, 1, 15, 15, 0, 0),
                "name": "Alice Brown", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            }
        ]
        tier_event_df = spark_session.createDataFrame(tier_event_data)
        
        # Previous tier data (for CleverTap events)
        prev_tier_data = [
            {"account_id": 1001, "user_id": 101, "tier": "CHALLENGER"},
            {"account_id": 1002, "user_id": 102, "tier": "CHALLENGER"},
            {"account_id": 1003, "user_id": 103, "tier": "GOLD"},
            {"account_id": 1004, "user_id": 104, "tier": "SILVER"}
        ]
        prev_tier_df = spark_session.createDataFrame(prev_tier_data)
        
        # Setup IO utils mocks
        def mock_read_csv_file(path, schema=None, return_empty=False):
            if "aum_data/dt=" in path:
                return aum_df
            elif "tier_snapshots/dt=" in path:
                return prev_tier_df
            else:
                return spark_session.createDataFrame([])
        
        mock_io_utils.return_value.read_csv_file.side_effect = mock_read_csv_file
        mock_io_utils.return_value.read_parquet_data.return_value = tier_event_df
        mock_io_utils.return_value.get_mongo_connection_string.return_value = "*******************************************"
        
        # Mock operations de_dupe_dataframe
        def mock_de_dupe(df, keys, time_col, type="desc"):
            if type == "asc":
                return df.orderBy(F.col(time_col).asc()).dropDuplicates(keys)
            else:
                return df.orderBy(F.col(time_col).desc()).dropDuplicates(keys)
        
        mock_operations.return_value.de_dupe_dataframe.side_effect = mock_de_dupe
        
        # Initialize and execute
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        processor.execute()
        
        # Verify all IO operations were called
        assert mock_io_utils.return_value.read_csv_file.call_count >= 2  # AUM data and previous tier data
        mock_io_utils.return_value.read_parquet_data.assert_called_once()  # Tier events
        mock_io_utils.return_value.write_csv_file.assert_called_once()  # Tier snapshot
        mock_io_utils.return_value.write_dataset_to_mongo.assert_called_once()  # Mongo write
        mock_io_utils.return_value.write_data_in_kafka.assert_called_once()  # CleverTap events

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_run_method_integration(self, mock_date_utils, mock_operations, mock_io_utils,
                                   mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test run method with Spark session management."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock minimal data for execution with proper schemas
        empty_aum_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("portfolioValue", DoubleType(), True)
        ])
        empty_prev_tier_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("tier", StringType(), True)
        ])
        empty_tier_schema = StructType([
            StructField("accountId", LongType(), True),
            StructField("userId", LongType(), True),
            StructField("userAction", StringType(), True),
            StructField("currentTier", StringType(), True),
            StructField("eligibleUpgradeTier", StringType(), True),
            StructField("eventTime", TimestampType(), True),
            StructField("name", StringType(), True),
            StructField("email", StringType(), True),
            StructField("tradingCompetitionId", StringType(), True)
        ])

        empty_aum_df = spark_session.createDataFrame([], empty_aum_schema)
        empty_tier_df = spark_session.createDataFrame([], empty_tier_schema)
        empty_prev_tier_df = spark_session.createDataFrame([], empty_prev_tier_schema)

        def mock_read_csv_file(path, schema=None, return_empty=False):
            if "aum_data/dt=" in path:
                return empty_aum_df
            elif "tier_snapshots/dt=" in path:
                return empty_prev_tier_df
            else:
                return empty_aum_df

        mock_io_utils.return_value.read_csv_file.side_effect = mock_read_csv_file
        mock_io_utils.return_value.read_parquet_data.return_value = empty_tier_df
        mock_io_utils.return_value.get_mongo_connection_string.return_value = "*******************************************"
        mock_operations.return_value.de_dupe_dataframe.return_value = empty_tier_df
        
        # Initialize and run
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        processor.run()
        
        # Verify Spark session was properly managed
        mock_spark_utils_instance.create_spark_session.assert_called_once()
        mock_spark_utils_instance.stop_spark.assert_called_once_with(spark_session)

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_complex_tier_upgrade_scenarios(self, mock_date_utils, mock_operations, mock_io_utils,
                                           mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test complex tier upgrade scenarios with various user actions."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Create test scenario: User with CHALLENGER tier and LEGEND-level AUM
        aum_data = [{"account_id": 1001, "portfolioValue": *********.0}]  # LEGEND tier AUM
        aum_df = spark_session.createDataFrame(aum_data)
        
        tier_event_data = [
            {
                "accountId": 1001, "userId": 101, "userAction": "OPT_IN",
                "currentTier": "CHALLENGER", "eligibleUpgradeTier": "LEGEND",
                "eventTime": datetime(2025, 1, 15, 10, 0, 0),
                "name": "John Doe", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            }
        ]
        tier_event_df = spark_session.createDataFrame(tier_event_data)
        
        # Setup IO utils mocks
        def mock_read_csv_file(path, schema=None, return_empty=False):
            if "aum_data/dt=" in path:
                return aum_df
            else:
                # Return empty DataFrame with proper schema
                empty_schema = StructType([
                    StructField("account_id", LongType(), True),
                    StructField("user_id", LongType(), True),
                    StructField("tier", StringType(), True)
                ])
                return spark_session.createDataFrame([], empty_schema)

        mock_io_utils.return_value.read_csv_file.side_effect = mock_read_csv_file
        mock_io_utils.return_value.read_parquet_data.return_value = tier_event_df
        mock_io_utils.return_value.get_mongo_connection_string.return_value = "*******************************************"

        # Mock operations de_dupe_dataframe
        def mock_de_dupe(df, keys, time_col, type="desc"):
            return df.dropDuplicates(keys)

        mock_operations.return_value.de_dupe_dataframe.side_effect = mock_de_dupe

        # Initialize processor
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)

        # Test individual methods with the scenario
        aum_result = processor.load_and_process_aum_data()
        tier_result, tier_events_result = processor.get_user_tier()

        # Verify AUM processing - the join might create multiple rows due to tier ranges
        assert aum_result.count() >= 1
        aum_with_legend = aum_result.filter(F.col("eligible_upgrade_tier") == "LEGEND")
        assert aum_with_legend.count() >= 1
        
        # Verify tier processing
        assert tier_result.count() == 1
        tier_row = tier_result.collect()[0]
        assert tier_row["tier"] == "CHALLENGER"  # Current tier from OPT_IN action
        
        # Test tier eligibility processing
        # Filter for LEGEND tier from AUM result since join creates multiple rows
        aum_legend = aum_result.filter(F.col("eligible_upgrade_tier") == "LEGEND")
        combined_df = tier_result.join(aum_legend, on=["account_id"], how="left").fillna({'aum': 0})

        if combined_df.count() > 0:
            eligibility_result = processor.process_tier_eligibility(combined_df)

            if eligibility_result.count() > 0:
                eligibility_row = eligibility_result.collect()[0]
                # CHALLENGER->LEGEND should be upgradable according to the business logic
                assert eligibility_row["eligible_upgrade_tier"] == "LEGEND"

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_error_handling_scenarios(self, mock_date_utils, mock_operations, mock_io_utils,
                                     mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test error handling scenarios."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock IO utils to raise exception for previous tier file
        def mock_read_csv_file(path, schema=None, return_empty=False):
            if "tier_snapshots/dt=" in path:
                raise Exception("File not found")
            else:
                # Return empty DataFrame with proper schema
                empty_schema = StructType([
                    StructField("account_id", LongType(), True),
                    StructField("portfolioValue", DoubleType(), True)
                ])
                return spark_session.createDataFrame([], empty_schema)

        # Create empty tier events DataFrame with proper schema
        empty_tier_schema = StructType([
            StructField("accountId", LongType(), True),
            StructField("userId", LongType(), True),
            StructField("userAction", StringType(), True),
            StructField("currentTier", StringType(), True),
            StructField("eligibleUpgradeTier", StringType(), True),
            StructField("eventTime", TimestampType(), True),
            StructField("name", StringType(), True),
            StructField("email", StringType(), True),
            StructField("tradingCompetitionId", StringType(), True)
        ])
        empty_tier_df = spark_session.createDataFrame([], empty_tier_schema)

        mock_io_utils.return_value.read_csv_file.side_effect = mock_read_csv_file
        mock_io_utils.return_value.read_parquet_data.return_value = empty_tier_df
        mock_io_utils.return_value.get_mongo_connection_string.return_value = "*******************************************"
        mock_operations.return_value.de_dupe_dataframe.return_value = empty_tier_df
        
        # Initialize processor
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        
        # Test publish_clevertap_event with missing previous tier file
        current_tier_df = spark_session.createDataFrame([{"account_id": 1001, "user_id": 101, "tier": "GOLD"}])
        processor.publish_clevertap_event(current_tier_df)
        
        # Verify error was logged
        mock_logger_instance.error.assert_called_with("Previous Tier File is not found")
