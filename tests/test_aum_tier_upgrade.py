import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestAUMTierUpgradeProcessor:
    """Test class for AUMTierUpgradeProcessor functionality."""

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_init(self, mock_date_utils, mock_operations, mock_io_utils, 
                  mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test AUMTierUpgradeProcessor initialization."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Initialize AUMTierUpgradeProcessor
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        
        # Assertions
        assert processor.config == mock_aum_tier_upgrade_config
        assert processor.bucket_path == "s3a://test-bucket"
        assert processor.aum_file_path == "s3a://test-bucket/aum_data"
        assert processor.tier_event_snapshot_path == "s3a://test-bucket/tier_events"
        assert len(processor.tier_upgrade_aum_range) == 5
        
        # Verify utility objects are created
        mock_spark_utils.assert_called_once_with("aum_tier_upgrade")
        mock_io_utils.assert_called_once()
        mock_operations.assert_called_once()

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_get_user_tier(self, mock_date_utils, mock_operations, mock_io_utils, 
                          mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test get_user_tier method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock tier event data
        tier_event_data = [
            {
                "accountId": 1001, "userId": 101, "userAction": "OPT_IN",
                "currentTier": "BRONZE", "eligibleUpgradeTier": "SILVER",
                "eventTime": datetime(2025, 1, 15, 10, 0, 0),
                "name": "John Doe", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1001, "userId": 101, "userAction": "ACCEPTED",
                "currentTier": "BRONZE", "eligibleUpgradeTier": "SILVER",
                "eventTime": datetime(2025, 1, 15, 11, 0, 0),
                "name": "John Doe", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            },
            {
                "accountId": 1002, "userId": 102, "userAction": "OPT_IN",
                "currentTier": "SILVER", "eligibleUpgradeTier": "GOLD",
                "eventTime": datetime(2025, 1, 15, 12, 0, 0),
                "name": "Jane Smith", "email": "<EMAIL>",
                "tradingCompetitionId": "TC_2025_Q1"
            }
        ]
        
        tier_event_df = spark_session.createDataFrame(tier_event_data)
        mock_io_utils.return_value.read_parquet_data.return_value = tier_event_df
        
        # Mock operations de_dupe_dataframe
        def mock_de_dupe(df, keys, time_col, type="desc"):
            if type == "asc":
                return df.orderBy(F.col(time_col).asc()).dropDuplicates(keys)
            else:
                return df.orderBy(F.col(time_col).desc()).dropDuplicates(keys)
        
        mock_operations.return_value.de_dupe_dataframe.side_effect = mock_de_dupe
        
        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        tier_result, tier_events_result = processor.get_user_tier()
        
        # Verify results
        assert tier_result.count() == 2
        assert tier_events_result.count() == 3
        
        # Check columns
        tier_columns = tier_result.columns
        assert "account_id" in tier_columns
        assert "user_id" in tier_columns
        assert "tier" in tier_columns
        assert "opt_in_time" in tier_columns
        
        # Check tier assignment logic
        tier_data = tier_result.collect()
        user_1001 = next(row for row in tier_data if row["account_id"] == 1001)
        assert user_1001["tier"] == "SILVER"  # ACCEPTED action should use eligibleUpgradeTier
        
        user_1002 = next(row for row in tier_data if row["account_id"] == 1002)
        assert user_1002["tier"] == "SILVER"  # OPT_IN action should use currentTier

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_load_and_process_aum_data(self, mock_date_utils, mock_operations, mock_io_utils, 
                                      mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test load_and_process_aum_data method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock AUM data
        aum_data = [
            {"account_id": 1001, "portfolioValue": 2500000.0},  # Should get SILVER tier
            {"account_id": 1002, "portfolioValue": ********.0}, # Should get GOLD tier
            {"account_id": 1003, "portfolioValue": -500000.0},  # Negative AUM, should get BRONZE
            {"account_id": 1004, "portfolioValue": 1********.0} # Should get LEGEND tier
        ]
        
        aum_df = spark_session.createDataFrame(aum_data)
        mock_io_utils.return_value.read_csv_file.return_value = aum_df
        
        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.load_and_process_aum_data()
        
        # Verify results - the join might create multiple rows due to tier ranges
        assert result.count() >= 4
        columns = result.columns
        assert "account_id" in columns
        assert "aum" in columns
        assert "eligible_upgrade_tier" in columns

        # Check tier assignment based on AUM - filter for non-null eligible tiers
        result_with_tiers = result.filter(F.col("eligible_upgrade_tier").isNotNull())
        result_data = result_with_tiers.collect()

        # Account 1001: 2.5M AUM should get SILVER tier
        account_1001_tiers = [row for row in result_data if row["account_id"] == 1001]
        assert len(account_1001_tiers) > 0
        assert "SILVER" in [row["eligible_upgrade_tier"] for row in account_1001_tiers]

        # Account 1002: 10M AUM should get GOLD tier
        account_1002_tiers = [row for row in result_data if row["account_id"] == 1002]
        assert len(account_1002_tiers) > 0
        assert "GOLD" in [row["eligible_upgrade_tier"] for row in account_1002_tiers]

        # Account 1003: Negative AUM should get BRONZE tier
        account_1003_tiers = [row for row in result_data if row["account_id"] == 1003]
        assert len(account_1003_tiers) > 0
        assert "BRONZE" in [row["eligible_upgrade_tier"] for row in account_1003_tiers]

        # Account 1004: 150M AUM should get LEGEND tier
        account_1004_tiers = [row for row in result_data if row["account_id"] == 1004]
        assert len(account_1004_tiers) > 0
        assert "LEGEND" in [row["eligible_upgrade_tier"] for row in account_1004_tiers]

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_process_tier_eligibility(self, mock_date_utils, mock_operations, mock_io_utils, 
                                     mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test process_tier_eligibility method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Create test tier data
        tier_data = [
            {"account_id": 1001, "tier": "CHALLENGER", "eligible_upgrade_tier": "LEGEND"},  # Should be upgradable
            {"account_id": 1002, "tier": "GOLD", "eligible_upgrade_tier": "CHALLENGER"},   # Should not be upgradable (not CHALLENGER->LEGEND)
            {"account_id": 1003, "tier": "SILVER", "eligible_upgrade_tier": "SILVER"},     # Same tier, not upgradable
            {"account_id": 1004, "tier": "BRONZE", "eligible_upgrade_tier": None}          # No eligible tier, not upgradable
        ]
        
        tier_df = spark_session.createDataFrame(tier_data)
        
        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.process_tier_eligibility(tier_df)
        
        # Verify results
        assert result.count() == 4
        columns = result.columns
        assert "is_upgradable" in columns
        assert "user_action_for_tier_upgrade" in columns
        
        result_data = result.collect()
        
        # Account 1001: CHALLENGER->LEGEND should be upgradable
        account_1001 = next(row for row in result_data if row["account_id"] == 1001)
        assert account_1001["is_upgradable"] == True
        assert account_1001["eligible_upgrade_tier"] == "LEGEND"
        
        # Account 1002: GOLD->CHALLENGER should not be upgradable (only CHALLENGER->LEGEND allowed)
        account_1002 = next(row for row in result_data if row["account_id"] == 1002)
        assert account_1002["is_upgradable"] == False
        assert account_1002["eligible_upgrade_tier"] is None
        
        # Account 1003: Same tier should not be upgradable
        account_1003 = next(row for row in result_data if row["account_id"] == 1003)
        assert account_1003["is_upgradable"] == False
        
        # Account 1004: No eligible tier should not be upgradable
        account_1004 = next(row for row in result_data if row["account_id"] == 1004)
        assert account_1004["is_upgradable"] == False

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_process_declined_actions(self, mock_date_utils, mock_operations, mock_io_utils,
                                     mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test process_declined_actions method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create test tier data with explicit schema
        tier_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("tier", StringType(), True),
            StructField("eligible_upgrade_tier", StringType(), True),
            StructField("is_upgradable", BooleanType(), True),
            StructField("user_action_for_tier_upgrade", StringType(), True)
        ])

        tier_data = [
            (1001, "CHALLENGER", "LEGEND", True, None),
            (1002, "GOLD", "CHALLENGER", True, None)
        ]
        tier_df = spark_session.createDataFrame(tier_data, tier_schema)

        # Create tier events data with declined actions
        tier_events_data = [
            {
                "accountId": 1001, "currentTier": "CHALLENGER", "eligibleUpgradeTier": "LEGEND",
                "userAction": "DECLINED", "eventTime": datetime(2025, 1, 15, 10, 0, 0)
            },
            {
                "accountId": 1003, "currentTier": "SILVER", "eligibleUpgradeTier": "GOLD",
                "userAction": "DECLINED", "eventTime": datetime(2025, 1, 15, 11, 0, 0)
            }
        ]
        tier_events_df = spark_session.createDataFrame(tier_events_data)

        # Mock operations de_dupe_dataframe
        mock_operations.return_value.de_dupe_dataframe.return_value = tier_events_df

        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.process_declined_actions(tier_df, tier_events_df)

        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "is_upgradable" in columns
        assert "user_action_for_tier_upgrade" in columns

        result_data = result.collect()

        # Account 1001: Should have declined action and not be upgradable
        account_1001 = next(row for row in result_data if row["account_id"] == 1001)
        assert account_1001["is_upgradable"] == False
        assert account_1001["user_action_for_tier_upgrade"] == "DECLINED"

        # Account 1002: Should remain upgradable (no declined action for this combination)
        account_1002 = next(row for row in result_data if row["account_id"] == 1002)
        assert account_1002["is_upgradable"] == True
        assert account_1002["user_action_for_tier_upgrade"] is None

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_process_ignored_actions(self, mock_date_utils, mock_operations, mock_io_utils,
                                    mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test process_ignored_actions method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create test tier data
        tier_data = [
            {"account_id": 1001, "tier": "CHALLENGER", "eligible_upgrade_tier": "LEGEND", "user_action_for_tier_upgrade": None},
            {"account_id": 1002, "tier": "GOLD", "eligible_upgrade_tier": "CHALLENGER", "user_action_for_tier_upgrade": "DECLINED"}
        ]
        tier_df = spark_session.createDataFrame(tier_data)

        # Create tier events data with ignored actions
        tier_events_data = [
            {
                "accountId": 1001, "currentTier": "CHALLENGER", "eligibleUpgradeTier": "LEGEND",
                "userAction": "IGNORED", "eventTime": datetime(2025, 1, 15, 10, 0, 0)
            },
            {
                "accountId": 1003, "currentTier": "SILVER", "eligibleUpgradeTier": "GOLD",
                "userAction": "IGNORED", "eventTime": datetime(2025, 1, 15, 11, 0, 0)
            }
        ]
        tier_events_df = spark_session.createDataFrame(tier_events_data)

        # Mock operations de_dupe_dataframe
        mock_operations.return_value.de_dupe_dataframe.return_value = tier_events_df

        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.process_ignored_actions(tier_df, tier_events_df)

        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "user_action_for_tier_upgrade" in columns

        result_data = result.collect()

        # Account 1001: Should have ignored action
        account_1001 = next(row for row in result_data if row["account_id"] == 1001)
        assert account_1001["user_action_for_tier_upgrade"] == "IGNORED"

        # Account 1002: Should keep existing declined action (not overwritten by ignored)
        account_1002 = next(row for row in result_data if row["account_id"] == 1002)
        assert account_1002["user_action_for_tier_upgrade"] == "DECLINED"

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_add_metadata_columns(self, mock_date_utils, mock_operations, mock_io_utils,
                                 mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test add_metadata_columns method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create test tier data
        tier_data = [
            {"account_id": 1001, "tier": "CHALLENGER"},
            {"account_id": 1002, "tier": "GOLD"}
        ]
        tier_df = spark_session.createDataFrame(tier_data)

        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.add_metadata_columns(tier_df)

        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "updated_at" in columns
        assert "execution_time" in columns

        # Check that metadata columns are added
        first_row = result.collect()[0]
        assert first_row["execution_time"] == datetime(2025, 1, 15, 14, 0, 0)
        assert first_row["updated_at"] is not None

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_get_snapshotted_tier(self, mock_date_utils, mock_operations, mock_io_utils,
                                 mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test get_snapshotted_tier method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create test tier data
        tier_data = [
            {"account_id": 1001, "tier": "CHALLENGER", "user_action_for_tier_upgrade": "DECLINED"},
            {"account_id": 1002, "tier": "GOLD", "user_action_for_tier_upgrade": None}
        ]
        tier_df = spark_session.createDataFrame(tier_data)

        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.get_snapshotted_tier(tier_df)

        # Verify results
        assert result.count() == 2

        # Verify that write_csv_file was called
        expected_path = "s3a://test-bucket/tier_snapshots/dt=2025-01-15/hour=14/"
        mock_io_utils.return_value.write_csv_file.assert_called_once()

        # Check that null user actions are handled properly
        result_data = result.collect()
        account_1002 = next(row for row in result_data if row["account_id"] == 1002)
        assert account_1002["user_action_for_tier_upgrade"] is None

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_write_tier_data_to_mongo(self, mock_date_utils, mock_operations, mock_io_utils,
                                     mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test write_tier_data_to_mongo method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create test tier data
        tier_data = [
            {
                "account_id": 1001, "user_id": 101, "tier": "CHALLENGER",
                "is_upgradable": True, "user_action_for_tier_upgrade": None,
                "eligible_upgrade_tier": "LEGEND", "updated_at": datetime(2025, 1, 15, 14, 0, 0),
                "aum": ********.0, "trading_competition_id": "TC_2025_Q1"
            },
            {
                "account_id": 1002, "user_id": 102, "tier": "GOLD",
                "is_upgradable": False, "user_action_for_tier_upgrade": "DECLINED",
                "eligible_upgrade_tier": None, "updated_at": datetime(2025, 1, 15, 14, 0, 0),
                "aum": ********.0, "trading_competition_id": "TC_2025_Q1"
            }
        ]
        tier_df = spark_session.createDataFrame(tier_data)

        # Mock mongo connection
        mock_io_utils.return_value.get_mongo_connection_string.return_value = "*******************************************"

        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        result = processor.write_tier_data_to_mongo(tier_df)

        # Verify results
        assert result.count() == 2

        # Verify that mongo write was called
        mock_io_utils.return_value.write_dataset_to_mongo.assert_called_once()

        # Check that null values are converted to empty strings
        result_data = result.collect()
        account_1001 = next(row for row in result_data if row["account_id"] == 1001)
        assert account_1001["user_action_for_tier_upgrade"] == ""
        assert account_1001["eligible_upgrade_tier"] == "LEGEND"

        account_1002 = next(row for row in result_data if row["account_id"] == 1002)
        assert account_1002["user_action_for_tier_upgrade"] == "DECLINED"
        assert account_1002["eligible_upgrade_tier"] == ""

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_publish_clevertap_event(self, mock_date_utils, mock_operations, mock_io_utils,
                                    mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test publish_clevertap_event method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create current tier data
        current_tier_data = [
            {"account_id": 1001, "user_id": 101, "tier": "GOLD"},
            {"account_id": 1002, "user_id": 102, "tier": "CHALLENGER"}
        ]
        current_tier_df = spark_session.createDataFrame(current_tier_data)

        # Create previous tier data
        prev_tier_data = [
            {"account_id": 1001, "user_id": 101, "tier": "SILVER"},  # Upgraded from SILVER to GOLD
            {"account_id": 1002, "user_id": 102, "tier": "CHALLENGER"}  # No change
        ]
        prev_tier_df = spark_session.createDataFrame(prev_tier_data)

        # Mock IO utils to return previous tier data
        mock_io_utils.return_value.read_csv_file.return_value = prev_tier_df

        # Initialize and test
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)
        processor.publish_clevertap_event(current_tier_df)

        # Verify that kafka write was called (only for users with tier changes)
        mock_io_utils.return_value.write_data_in_kafka.assert_called_once()

    @patch('src.jobs.trading_competition.aum_tier_upgrade.get_logger')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.SparkUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.IOUtils')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.Operations')
    @patch('src.jobs.trading_competition.aum_tier_upgrade.DateUtils')
    def test_run(self, mock_date_utils, mock_operations, mock_io_utils,
                 mock_spark_utils, mock_logger, mock_aum_tier_upgrade_config, spark_session):
        """Test run method."""
        from src.jobs.trading_competition.aum_tier_upgrade import AUMTierUpgradeProcessor

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Initialize processor
        processor = AUMTierUpgradeProcessor(mock_aum_tier_upgrade_config)

        # Mock execute method
        processor.execute = Mock()

        # Test run
        processor.run()

        # Verify execute was called
        processor.execute.assert_called_once()

        # Verify spark session was stopped
        mock_spark_utils_instance.stop_spark.assert_called_once_with(spark_session)
