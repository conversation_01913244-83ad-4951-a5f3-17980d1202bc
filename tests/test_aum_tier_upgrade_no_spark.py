import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestAUMTierUpgradeProcessorNoSpark:
    """Test class for AUMTierUpgradeProcessor functionality without Spark dependencies."""

    def test_tier_upgrade_range_validation(self, mock_aum_tier_upgrade_config):
        """Test tier upgrade range configuration validation."""
        # Test that tier upgrade ranges are properly configured
        tier_ranges = mock_aum_tier_upgrade_config["aum_tier_upgrade"]["tier_upgrade_range"]
        
        assert len(tier_ranges) == 5
        
        # Verify tier structure
        tiers = [tier["tier"] for tier in tier_ranges]
        expected_tiers = ["BRONZE", "SILVER", "GOLD", "CHALLENGER", "LEGEND"]
        assert tiers == expected_tiers
        
        # Verify AUM ranges are logical
        bronze_tier = next(tier for tier in tier_ranges if tier["tier"] == "BRONZE")
        assert bronze_tier["min_aum"] == 0
        assert bronze_tier["max_aum"] == 1000000
        
        silver_tier = next(tier for tier in tier_ranges if tier["tier"] == "SILVER")
        assert silver_tier["min_aum"] == 1000000
        assert silver_tier["max_aum"] == 5000000
        
        legend_tier = next(tier for tier in tier_ranges if tier["tier"] == "LEGEND")
        assert legend_tier["min_aum"] == 100000000
        assert legend_tier["max_aum"] is None  # No upper limit

    def test_config_validation(self, mock_aum_tier_upgrade_config):
        """Test configuration validation."""
        config = mock_aum_tier_upgrade_config
        
        # Test required configuration keys
        assert "bucket_path" in config
        assert "aum_tier_upgrade" in config
        assert "kafka_topics" in config
        assert "data_store" in config
        
        # Test AUM tier upgrade specific config
        aum_config = config["aum_tier_upgrade"]
        assert "aum_file_path" in aum_config
        assert "tier_event_snapshot_path" in aum_config
        assert "tier_snapshot_path" in aum_config
        assert "tier_upgrade_range" in aum_config
        assert "mongo_collection" in aum_config
        
        # Test Kafka configuration
        assert "clevertap_events_topic" in config["kafka_topics"]
        
        # Test MongoDB configuration
        mongo_config = config["data_store"]["reporting_mongo"]
        assert "host" in mongo_config
        assert "port" in mongo_config
        assert "database" in mongo_config

    def test_tier_eligibility_logic(self):
        """Test tier eligibility logic without Spark."""
        # Test the core logic for tier eligibility
        
        # Test case 1: CHALLENGER -> LEGEND (should be eligible)
        current_tier = "CHALLENGER"
        eligible_tier = "LEGEND"
        
        # This mimics the logic in process_tier_eligibility
        is_eligible = (current_tier == "CHALLENGER" and eligible_tier == "LEGEND")
        assert is_eligible == True
        
        # Test case 2: GOLD -> CHALLENGER (should not be eligible)
        current_tier = "GOLD"
        eligible_tier = "CHALLENGER"
        
        is_eligible = (current_tier == "CHALLENGER" and eligible_tier == "LEGEND")
        assert is_eligible == False
        
        # Test case 3: Same tier (should not be eligible)
        current_tier = "SILVER"
        eligible_tier = "SILVER"
        
        is_eligible = (current_tier == "CHALLENGER" and eligible_tier == "LEGEND")
        assert is_eligible == False

    def test_aum_tier_mapping_logic(self):
        """Test AUM to tier mapping logic."""
        tier_ranges = [
            {"tier": "BRONZE", "min_aum": 0, "max_aum": 1000000},
            {"tier": "SILVER", "min_aum": 1000000, "max_aum": 5000000},
            {"tier": "GOLD", "min_aum": 5000000, "max_aum": 25000000},
            {"tier": "CHALLENGER", "min_aum": 25000000, "max_aum": None},
            {"tier": "LEGEND", "min_aum": 100000000, "max_aum": None}
        ]
        
        def get_tier_for_aum(aum_value):
            """Helper function to determine tier based on AUM."""
            aum_to_compare = max(0, aum_value)  # Handle negative AUM

            # Find the highest tier that matches (iterate in reverse order)
            for tier_range in reversed(tier_ranges):
                min_aum = tier_range["min_aum"]
                max_aum = tier_range["max_aum"]

                if aum_to_compare >= min_aum:
                    if max_aum is None or aum_to_compare < max_aum:
                        return tier_range["tier"]

            return "BRONZE"  # Default tier
        
        # Test various AUM values
        assert get_tier_for_aum(500000) == "BRONZE"
        assert get_tier_for_aum(2500000) == "SILVER"
        assert get_tier_for_aum(10000000) == "GOLD"
        assert get_tier_for_aum(50000000) == "CHALLENGER"
        assert get_tier_for_aum(150000000) == "LEGEND"
        assert get_tier_for_aum(-100000) == "BRONZE"  # Negative AUM

    def test_user_action_priority_logic(self):
        """Test user action priority logic."""
        # Test that DECLINED actions override is_upgradable
        is_upgradable = True
        user_action = "DECLINED"
        
        # Logic from process_declined_actions
        final_upgradable = False if user_action == "DECLINED" else is_upgradable
        final_action = "DECLINED" if user_action == "DECLINED" else None
        
        assert final_upgradable == False
        assert final_action == "DECLINED"
        
        # Test that IGNORED actions don't affect is_upgradable but set action
        is_upgradable = True
        user_action = "IGNORED"
        
        # Logic from process_ignored_actions
        final_upgradable = is_upgradable  # IGNORED doesn't change upgradability
        final_action = "IGNORED" if user_action == "IGNORED" else None
        
        assert final_upgradable == True
        assert final_action == "IGNORED"

    def test_date_calculation_logic(self):
        """Test date calculation logic."""
        # Test the date calculation for AUM file path
        from datetime import date, timedelta
        
        # Simulate t_1 date
        t_1 = date(2025, 1, 15)
        t_2_aum = t_1 - timedelta(1)  # Previous day for AUM data
        
        assert t_2_aum == date(2025, 1, 14)
        
        # Test path construction
        bucket_path = "s3a://test-bucket"
        aum_file_path = "aum_data"
        expected_path = f"{bucket_path}/{aum_file_path}/dt={t_2_aum}/"
        
        assert expected_path == "s3a://test-bucket/aum_data/dt=2025-01-14/"

    def test_mongo_config_construction(self, mock_aum_tier_upgrade_config):
        """Test MongoDB configuration construction."""
        config = mock_aum_tier_upgrade_config
        
        # Simulate mongo config construction from write_tier_data_to_mongo
        mongo_config = config["data_store"]["reporting_mongo"].copy()
        mongo_config['collection'] = config["aum_tier_upgrade"]["mongo_collection"]
        
        expected_uri = "*****************************************************"
        mongo_write_config = {
            "uri": expected_uri,
            "collection": config["trading_competition_mongo_collection"],
            "batch_size": "500",
            "mode": "append"
        }
        
        assert mongo_config["collection"] == "aum_tier_upgrade"
        assert mongo_write_config["collection"] == "trading_competition"
        assert mongo_write_config["batch_size"] == "500"

    def test_clevertap_event_structure(self):
        """Test CleverTap event structure logic."""
        # Test the event structure that would be created
        user_id = 101
        account_id = 1001
        prev_tier = "CHALLENGER"
        current_tier = "LEGEND"
        upgrade_time = datetime(2025, 1, 15, 14, 0, 0)
        
        # Simulate the event data structure from publish_clevertap_event
        event_data = {
            "userId": user_id,
            "accountId": account_id,
            "eventData": {
                "upgrade_tier_time": upgrade_time,
                "previous_tier": prev_tier,
                "upgrade_tier": current_tier
            },
            "eventName": "tc_tier_upgrade_success"
        }
        
        assert event_data["userId"] == 101
        assert event_data["accountId"] == 1001
        assert event_data["eventData"]["previous_tier"] == "CHALLENGER"
        assert event_data["eventData"]["upgrade_tier"] == "LEGEND"
        assert event_data["eventName"] == "tc_tier_upgrade_success"

    def test_null_value_handling(self):
        """Test null value handling logic."""
        # Test logic from write_tier_data_to_mongo for null handling
        
        # Test user_action_for_tier_upgrade null handling
        user_action = None
        processed_action = "" if user_action is None else user_action
        assert processed_action == ""
        
        user_action = "DECLINED"
        processed_action = "" if user_action is None else user_action
        assert processed_action == "DECLINED"
        
        # Test eligible_upgrade_tier null handling
        eligible_tier = None
        processed_tier = "" if eligible_tier is None else eligible_tier
        assert processed_tier == ""
        
        eligible_tier = "LEGEND"
        processed_tier = "" if eligible_tier is None else eligible_tier
        assert processed_tier == "LEGEND"

    def test_path_construction_logic(self, mock_aum_tier_upgrade_config):
        """Test path construction logic."""
        config = mock_aum_tier_upgrade_config
        bucket_path = config["bucket_path"]
        
        # Test various path constructions
        aum_file_path = f"{bucket_path}/{config['aum_tier_upgrade']['aum_file_path']}"
        tier_event_path = f"{bucket_path}/{config['aum_tier_upgrade']['tier_event_snapshot_path']}"
        
        assert aum_file_path == "s3a://test-bucket/aum_data"
        assert tier_event_path == "s3a://test-bucket/tier_events"
        
        # Test snapshot path construction
        t_1 = "2025-01-15"
        h_1 = 14
        snapshot_path = f"{bucket_path}/{config['aum_tier_upgrade']['tier_snapshot_path']}/dt={t_1}/hour={h_1}/"
        
        assert snapshot_path == "s3a://test-bucket/tier_snapshots/dt=2025-01-15/hour=14/"

    def test_tier_comparison_logic(self):
        """Test tier comparison and filtering logic."""
        # Test logic for identifying tier changes (from publish_clevertap_event)
        
        # Test case 1: Tier changed
        prev_tier = "CHALLENGER"
        current_tier = "LEGEND"
        has_changed = prev_tier != current_tier
        assert has_changed == True
        
        # Test case 2: Tier unchanged
        prev_tier = "GOLD"
        current_tier = "GOLD"
        has_changed = prev_tier != current_tier
        assert has_changed == False
        
        # Test case 3: Previous tier is None
        prev_tier = None
        current_tier = "SILVER"
        has_changed = prev_tier is not None and prev_tier != current_tier
        assert has_changed == False  # Should not trigger event if prev_tier is None

    def test_configuration_defaults(self):
        """Test configuration default values."""
        # Test default values that would be used in initialization
        
        # Test offset default
        config = {"trading_competition": {}}
        offset = config.get("trading_competition", {}).get("offset", 0)
        assert offset == 0
        
        # Test frequency default
        frequency = config.get("trading_competition", {}).get("frequency", 24)
        assert frequency == 24
        
        # Test utc_cutoff_ts default handling
        utc_cutoff_ts = config.get("utc_cutoff_ts")
        if utc_cutoff_ts is None:
            # Would call DateUtils.get_utc_timestamp() in actual code
            utc_cutoff_ts = datetime.now()
        
        assert utc_cutoff_ts is not None
