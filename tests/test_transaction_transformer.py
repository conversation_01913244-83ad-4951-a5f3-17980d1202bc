import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestTransactionTransformer:
    """Test class for TransactionTransformer functionality."""

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_init(self, mock_date_utils, mock_operations, mock_io_utils, 
                  mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test TransactionTransformer initialization."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Initialize TransactionTransformer
        transformer = TransactionTransformer(mock_transaction_config)
        
        # Assertions
        assert transformer.config == mock_transaction_config
        assert transformer.bucket_path == "s3a://test-bucket"
        assert transformer.partner_id == 1
        assert transformer.usdt_coin_id == 825
        
        # Verify utility objects are created
        mock_spark_utils.assert_called_once_with("Transaction Transformer")
        mock_io_utils.assert_called_once()
        mock_operations.assert_called_once()

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_get_all_prices(self, mock_date_utils, mock_operations, mock_io_utils, 
                           mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test get_all_prices method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock price data
        forex_data = [{"forex_id": 10000, "partner_id": 1, "mid_price": 15000}]
        gold_data = [{"partnerId": 1, "closeBuyBack": "1000000", "closeSell": "1010000"}]
        
        forex_df = spark_session.createDataFrame(forex_data)
        gold_df = spark_session.createDataFrame(gold_data)
        
        # Mock global stock price data
        global_stock_data = [{"_id": 1, "mid_price": 100.50}, {"_id": 2, "mid_price": 200.75}]
        global_stock_df = spark_session.createDataFrame(global_stock_data)
        
        # Mock crypto price data
        crypto_data = [{"_id": 101, "mid_price": 50000.0}, {"_id": 825, "mid_price": 15000.0}]
        crypto_df = spark_session.createDataFrame(crypto_data)
        
        # Mock other price data
        crypto_futures_data = [{"_id": 301, "close_price": 45000.0}]
        fund_data = [{"fund_id": 401, "net_asset_value": 1000.0}]
        options_data = [{"optionsContractId": 501, "price": 5.0}]
        
        crypto_futures_df = spark_session.createDataFrame(crypto_futures_data)
        fund_df = spark_session.createDataFrame(fund_data)
        options_df = spark_session.createDataFrame(options_data)
        
        # Setup IO utils mock
        mock_io_utils.return_value.read_json_data.side_effect = [forex_df, gold_df, fund_df]
        mock_io_utils.return_value.read_csv_file.side_effect = [
            global_stock_df, crypto_df, crypto_futures_df, options_df
        ]
        
        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.get_all_prices()
        
        # Verify results
        usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price = result
        
        assert usdt_price == 15000
        assert forex_price == 15000
        assert gold_price == 1005000  # floor((1000000 + 1010000) / 2)
        assert global_stock_price.count() == 2
        assert crypto_currency_price.count() == 2
        assert fund_price.count() == 1
        assert options_price.count() == 1
        assert crypto_futures_price.count() == 1

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_get_user_tiers(self, mock_date_utils, mock_operations, mock_io_utils, 
                           mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test get_user_tiers method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock tier data
        tier_data = [
            {"account_id": 1001, "tier": "GOLD"},
            {"account_id": 1002, "tier": "SILVER"}
        ]
        tier_df = spark_session.createDataFrame(tier_data)
        
        mock_io_utils.return_value.read_csv_file.return_value = tier_df
        
        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.get_user_tiers()
        
        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "account_id" in columns
        assert "tier" in columns
        assert "trading_competition_start_time" in columns
        
        # Check that trading_competition_start_time is added
        first_row = result.collect()[0]
        assert first_row["trading_competition_start_time"] == datetime(2025, 1, 1, 0, 0, 0)

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_get_crypto_currency_transactions(self, mock_date_utils, mock_operations, mock_io_utils, 
                                            mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test get_crypto_currency_transactions method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Mock crypto transaction data
        crypto_txn_data = [
            {
                "crypto_currency_id": 101, "account_id": 1001, "user_id": 101, "id": 1,
                "created": datetime(2025, 1, 15, 10, 0, 0), "updated": datetime(2025, 1, 15, 10, 0, 0),
                "executed_quantity": 1.0, "executed_unit_price": 50000.0, "executed_total_price": 50000.0,
                "fee": 100.0, "transaction_type": "BUY", "status": "SUCCESS", "partner_id": 1,
                "transaction_time": datetime(2025, 1, 15, 10, 0, 0)
            }
        ]
        
        crypto_txn_df = spark_session.createDataFrame(crypto_txn_data)
        
        # Mock price data
        price_data = [{"asset_id": 101, "current_unit_price": 52000.0}]
        price_df = spark_session.createDataFrame(price_data)
        
        # Create empty DataFrames with proper schemas
        empty_crypto_schema = StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_rebrand_schema = StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        # Setup mocks
        mock_io_utils.return_value.read_parquet_data.side_effect = [
            crypto_txn_df,  # crypto_currency_transactions
            spark_session.createDataFrame([], empty_crypto_schema),  # crypto_currency_wallet_transfers
            spark_session.createDataFrame([], empty_crypto_schema)   # crypto_currency_pocket_transactions
        ]
        mock_io_utils.return_value.read_csv_file.return_value = spark_session.createDataFrame([], empty_rebrand_schema)  # crypto_rebranding_txn
        
        mock_operations.return_value.get_union.side_effect = lambda df1, df2: df1.union(df2)
        
        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.get_crypto_currency_transactions(price_df)
        
        # Verify results
        assert result.count() == 1
        columns = result.columns
        assert "asset_type" in columns
        assert "asset_sub_type" in columns
        assert "current_unit_price" in columns
        
        first_row = result.collect()[0]
        assert first_row["asset_type"] == "crypto_currency"
        assert first_row["asset_sub_type"] == "crypto_currency_transactions"

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_cast_fields(self, mock_date_utils, mock_operations, mock_io_utils, 
                        mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test cast_fields method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Create test data with string timestamps
        test_data = [
            {
                "created": "2025-01-15 10:00:00",
                "updated": "2025-01-15 10:05:00",
                "transaction_time": "2025-01-15 10:00:00",
                "other_field": "test"
            }
        ]
        
        test_df = spark_session.createDataFrame(test_data)
        
        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.cast_fields(test_df)
        
        # Verify field types
        schema = result.schema
        created_field = next(field for field in schema.fields if field.name == "created")
        updated_field = next(field for field in schema.fields if field.name == "updated")
        transaction_time_field = next(field for field in schema.fields if field.name == "transaction_time")
        
        assert isinstance(created_field.dataType, TimestampType)
        assert isinstance(updated_field.dataType, TimestampType)
        assert isinstance(transaction_time_field.dataType, TimestampType)

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_update_executed_quantity_splits(self, mock_date_utils, mock_operations, mock_io_utils,
                                           mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test update_executed_quantity_splits method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create test transaction data
        txn_data = [
            {
                "asset_sub_type": "global_stock_transactions",
                "asset_id": 1,
                "executed_quantity": 100.0,
                "executed_unit_price": 50.0,
                "transaction_time": datetime(2025, 1, 10, 10, 0, 0)
            },
            {
                "asset_sub_type": "global_stock_transactions",
                "asset_id": 2,
                "executed_quantity": 200.0,
                "executed_unit_price": 75.0,
                "transaction_time": datetime(2025, 1, 20, 10, 0, 0)
            }
        ]

        txn_df = spark_session.createDataFrame(txn_data)

        # Create splits data
        splits_data = [
            {
                "asset_sub_type": "global_stock_transactions",
                "asset_id": 1,
                "ratio": 2.0,
                "split_updated": datetime(2025, 1, 15, 0, 0, 0)
            }
        ]

        splits_df = spark_session.createDataFrame(splits_data)
        mock_io_utils.return_value.read_csv_file.return_value = splits_df

        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.update_executed_quantity_splits(txn_df)

        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "updated_executed_quantity" in columns
        assert "updated_executed_unit_price" in columns

        # Check split adjustment for asset_id 1 (transaction before split)
        asset_1_row = result.filter(F.col("asset_id") == 1).collect()[0]
        assert asset_1_row["updated_executed_quantity"] == 200.0  # 100 * 2
        assert asset_1_row["updated_executed_unit_price"] == 25.0  # 50 / 2

        # Check no adjustment for asset_id 2 (transaction after split)
        asset_2_row = result.filter(F.col("asset_id") == 2).collect()[0]
        assert asset_2_row["updated_executed_quantity"] == 200.0  # unchanged
        assert asset_2_row["updated_executed_unit_price"] == 75.0  # unchanged

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_get_global_stock_transactions(self, mock_date_utils, mock_operations, mock_io_utils,
                                         mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test get_global_stock_transactions method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Mock global stock transaction data
        gss_txn_data = [
            {
                "global_stock_id": 1, "account_id": 1001, "user_id": 101, "id": 1,
                "created": datetime(2025, 1, 15, 10, 0, 0), "updated": datetime(2025, 1, 15, 10, 0, 0),
                "executed_quantity": 100.0, "executed_unit_price": 50.0, "executed_total_price": 5000.0,
                "transaction_fee": 10.0, "transaction_type": "BUY", "status": "SUCCESS", "partner_id": 1,
                "usd_to_idr": 15000.0, "transaction_time": datetime(2025, 1, 15, 10, 0, 0),
                "stock_type": "REGULAR", "trading_hours": "REGULAR"
            },
            {
                "global_stock_id": 2, "account_id": 1002, "user_id": 102, "id": 2,
                "created": datetime(2025, 1, 15, 11, 0, 0), "updated": datetime(2025, 1, 15, 11, 0, 0),
                "executed_quantity": 50.0, "executed_unit_price": 100.0, "executed_total_price": 5000.0,
                "transaction_fee": 15.0, "transaction_type": "SELL", "status": "SUCCESS", "partner_id": 1,
                "usd_to_idr": 15100.0, "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
                "stock_type": "CFD_LEVERAGE", "trading_hours": "INTRADAY"
            }
        ]

        gss_txn_df = spark_session.createDataFrame(gss_txn_data)

        # Mock price data
        price_data = [
            {"asset_id": 1, "current_unit_price": 52.0},
            {"asset_id": 2, "current_unit_price": 98.0}
        ]
        price_df = spark_session.createDataFrame(price_data)

        mock_io_utils.return_value.read_parquet_data.return_value = gss_txn_df

        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.get_global_stock_transactions(price_df, 15000)

        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "asset_type" in columns
        assert "asset_sub_type" in columns
        assert "leverage" in columns
        assert "current_currency_to_idr" in columns

        # Check leverage calculation
        regular_stock = result.filter(F.col("asset_id") == 1).collect()[0]
        assert regular_stock["leverage"] == 0

        leverage_stock = result.filter(F.col("asset_id") == 2).collect()[0]
        assert leverage_stock["leverage"] == 4  # CFD_LEVERAGE + INTRADAY = 4

        # Check asset type
        first_row = result.collect()[0]
        assert first_row["asset_type"] == "global_stocks"
        assert first_row["asset_sub_type"] == "global_stock_transactions"
        assert first_row["current_currency_to_idr"] == 15000

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_get_forex_transactions(self, mock_date_utils, mock_operations, mock_io_utils,
                                   mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test get_forex_transactions method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Mock forex transaction data
        forex_txn_data = [
            {
                "forex_id": 10000, "account_id": 1001, "user_id": 101, "id": 1,
                "created": datetime(2025, 1, 15, 10, 0, 0), "updated": datetime(2025, 1, 15, 10, 0, 0),
                "quantity": 1000000.0, "unit_price": 1.0, "total_price": 1000000.0,
                "fee": 0.0, "transaction_type": "BUY", "status": "SUCCESS", "partner_id": 1,
                "transaction_time": datetime(2025, 1, 15, 10, 0, 0)
            }
        ]

        # Mock forex top-ups data
        forex_topup_data = [
            {
                "forex_id": 10000, "account_id": 1002, "user_id": 102, "id": 2,
                "created": datetime(2025, 1, 15, 11, 0, 0), "updated": datetime(2025, 1, 15, 11, 0, 0),
                "final_amount": 500000.0, "unit_price": 1.0, "status": "COMPLETED", "partner_id": 1,
                "transaction_time": datetime(2025, 1, 15, 11, 0, 0)
            }
        ]

        # Mock forex cash-outs data
        forex_cashout_data = [
            {
                "forex_id": 10000, "account_id": 1003, "user_id": 103, "id": 3,
                "created": datetime(2025, 1, 15, 12, 0, 0), "updated": datetime(2025, 1, 15, 12, 0, 0),
                "withdrawal_amount": 200000.0, "unit_price": 1.0, "status": "COMPLETED", "partner_id": 1,
                "transaction_time": datetime(2025, 1, 15, 12, 0, 0)
            }
        ]

        forex_txn_df = spark_session.createDataFrame(forex_txn_data)
        forex_topup_df = spark_session.createDataFrame(forex_topup_data)
        forex_cashout_df = spark_session.createDataFrame(forex_cashout_data)

        mock_io_utils.return_value.read_parquet_data.side_effect = [
            forex_txn_df, forex_topup_df, forex_cashout_df
        ]

        mock_operations.return_value.get_union.side_effect = lambda df1, df2: df1.union(df2)

        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.get_forex_transactions(15000)

        # Verify results
        assert result.count() == 3
        columns = result.columns
        assert "asset_type" in columns
        assert "asset_sub_type" in columns
        assert "current_unit_price" in columns

        # Check asset types
        asset_types = [row["asset_type"] for row in result.collect()]
        assert all(asset_type == "forex" for asset_type in asset_types)

        # Check transaction types
        transaction_types = [row["transaction_type"] for row in result.collect()]
        assert "BUY" in transaction_types
        assert "SELL" in transaction_types

        # Check current unit price
        first_row = result.collect()[0]
        assert first_row["current_unit_price"] == 15000

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_get_gold_transactions(self, mock_date_utils, mock_operations, mock_io_utils,
                                  mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test get_gold_transactions method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Mock gold transaction data
        gold_txn_data = [
            {
                "account_id": 1001, "user_id": 101, "id": 1,
                "created": datetime(2025, 1, 15, 10, 0, 0), "updated": datetime(2025, 1, 15, 10, 0, 0),
                "quantity": 0.1, "unit_price": 1000000.0, "final_amount": 100000.0,
                "fees": 1000.0, "transaction_type": "BUY", "status": "SUCCESS", "partner_id": 1,
                "transaction_time": datetime(2025, 1, 15, 10, 0, 0)
            }
        ]

        # Mock gold withdrawal data
        gold_withdrawal_data = [
            {
                "account_id": 1002, "user_id": 102, "id": 2,
                "created": datetime(2025, 1, 15, 11, 0, 0), "updated": datetime(2025, 1, 15, 11, 0, 0),
                "net_amount": 0.05, "sell_price": 1010000.0, "fee": 500.0,
                "status": "DELIVERED", "transaction_time": datetime(2025, 1, 15, 11, 0, 0)
            }
        ]

        gold_txn_df = spark_session.createDataFrame(gold_txn_data)
        gold_withdrawal_df = spark_session.createDataFrame(gold_withdrawal_data)

        mock_io_utils.return_value.read_parquet_data.side_effect = [gold_txn_df, gold_withdrawal_df]
        mock_operations.return_value.get_union.side_effect = lambda df1, df2: df1.union(df2)

        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.get_gold_transactions(1005000)

        # Verify results
        assert result.count() == 2
        columns = result.columns
        assert "asset_type" in columns
        assert "asset_sub_type" in columns
        assert "current_unit_price" in columns

        # Check asset types
        asset_types = [row["asset_type"] for row in result.collect()]
        assert all(asset_type == "gold" for asset_type in asset_types)

        # Check asset_id (should be 1 for all gold transactions)
        asset_ids = [row["asset_id"] for row in result.collect()]
        assert all(asset_id == 1 for asset_id in asset_ids)

        # Check transaction types
        transaction_types = [row["transaction_type"] for row in result.collect()]
        assert "BUY" in transaction_types
        assert "GOLD_WITHDRAWAL" in transaction_types

        # Check current unit price
        first_row = result.collect()[0]
        assert first_row["current_unit_price"] == 1005000

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_add_current_unit_price(self, mock_date_utils, mock_operations, mock_io_utils,
                                   mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test add_current_unit_price method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Create initial positions data
        initials_data = [
            {
                "asset_type": "wallet", "asset_id": 10000, "account_id": 1001,
                "current_unit_price": 1.0, "current_currency_to_idr": 1
            },
            {
                "asset_type": "gold", "asset_id": 1, "account_id": 1001,
                "current_unit_price": 0.0, "current_currency_to_idr": 1
            },
            {
                "asset_type": "crypto_currency", "asset_id": 101, "account_id": 1001,
                "current_unit_price": 0.0, "current_currency_to_idr": 1
            },
            {
                "asset_type": "global_stocks", "asset_id": 1, "account_id": 1001,
                "current_unit_price": 0.0, "current_currency_to_idr": 1
            }
        ]

        initials_df = spark_session.createDataFrame(initials_data)

        # Create price dataframes
        crypto_price_data = [{"asset_id": 101, "current_unit_price": 50000.0}]
        global_stock_price_data = [{"asset_id": 1, "current_unit_price": 100.0}]
        fund_price_data = [{"asset_id": 401, "current_unit_price": 1000.0}]
        options_price_data = [{"asset_id": 501, "current_unit_price": 5.0}]
        crypto_futures_price_data = [{"asset_id": 301, "current_unit_price": 45000.0}]

        crypto_price_df = spark_session.createDataFrame(crypto_price_data)
        global_stock_price_df = spark_session.createDataFrame(global_stock_price_data)
        fund_price_df = spark_session.createDataFrame(fund_price_data)
        options_price_df = spark_session.createDataFrame(options_price_data)
        crypto_futures_price_df = spark_session.createDataFrame(crypto_futures_price_data)

        # Initialize and test
        transformer = TransactionTransformer(mock_transaction_config)
        result = transformer.add_current_unit_price(
            initials_df, 15000, 15000, 1005000,
            global_stock_price_df, crypto_price_df, fund_price_df,
            options_price_df, crypto_futures_price_df
        )

        # Verify results
        assert result.count() == 4

        # Check wallet remains unchanged
        wallet_row = result.filter(F.col("asset_type") == "wallet").collect()[0]
        assert wallet_row["current_unit_price"] == 1.0

        # Check gold price is updated
        gold_row = result.filter(F.col("asset_type") == "gold").collect()[0]
        assert gold_row["current_unit_price"] == 1005000

        # Check crypto price is joined
        crypto_row = result.filter(F.col("asset_type") == "crypto_currency").collect()[0]
        assert crypto_row["current_unit_price"] == 50000.0

        # Check global stock price is joined
        stock_row = result.filter(F.col("asset_type") == "global_stocks").collect()[0]
        assert stock_row["current_unit_price"] == 100.0

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_run(self, mock_date_utils, mock_operations, mock_io_utils,
                 mock_spark_utils, mock_logger, mock_transaction_config, spark_session):
        """Test run method."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer

        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )

        # Initialize transformer
        transformer = TransactionTransformer(mock_transaction_config)

        # Mock execute method
        transformer.execute = Mock()

        # Test run
        transformer.run()

        # Verify execute was called
        transformer.execute.assert_called_once()

        # Verify spark session was stopped
        mock_spark_utils_instance.stop_spark.assert_called_once_with(spark_session)
