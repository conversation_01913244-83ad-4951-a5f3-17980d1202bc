import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mo<PERSON>, MagicMock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestFlashGamesPnLNoSpark:
    """Test class for FlashGamesPnL functionality that doesn't require Spark."""

    def test_create_batches_static_method_basic(self):
        """Test the static create_batches method with basic scenario."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        # Create mock transaction data that behaves like PySpark Row objects
        class MockRow:
            def __init__(self, data):
                self._data = data
                # Make the object subscriptable for the sorting key
                for key, value in data.items():
                    setattr(self, key, value)
            
            def __getitem__(self, key):
                return self._data[key]
            
            def asDict(self):
                return self._data.copy()
        
        # Simple BUY transaction
        txn_data_1 = {
            "row_number": 1,
            "currency_to_idr": 15000.0,
            "updated_executed_unit_price": 50.0,
            "updated_executed_quantity": 100.0,
            "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 52.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields that the method expects
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }
        
        all_txn = [MockRow(txn_data_1)]
        buy_types = ["BUY"]
        sell_types = ["SELL"]
        
        # Test the static method
        result = FlashGamesPnL.create_batches(all_txn, buy_types, sell_types)
        
        assert isinstance(result, list)
        assert len(result) == 1
        
        # Check that PnL calculations are performed
        txn = result[0]
        assert "realized_pnl" in txn
        assert "unrealized_pnl" in txn
        assert "remaining_quantity" in txn
        assert "is_pnl_eligible" in txn
        
        # For a BUY transaction, should have unrealized PnL
        assert txn["transaction_type"] == "BUY"
        assert txn["remaining_quantity"] == 100.0
        assert txn["is_pnl_eligible"] == True
        
        # Calculate expected unrealized PnL: quantity * (current_price * current_rate - executed_price * executed_rate)
        expected_unrealized = int(100.0 * (52.0 * 15100 - 50.0 * 15000))
        assert txn["unrealized_pnl"] == expected_unrealized

    def test_create_batches_buy_sell_scenario(self):
        """Test create_batches with buy and sell transactions."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        class MockRow:
            def __init__(self, data):
                self._data = data
                for key, value in data.items():
                    setattr(self, key, value)
            
            def __getitem__(self, key):
                return self._data[key]
            
            def asDict(self):
                return self._data.copy()
        
        # BUY transaction
        buy_txn = {
            "row_number": 1,
            "currency_to_idr": 15000.0,
            "updated_executed_unit_price": 50.0,
            "updated_executed_quantity": 100.0,
            "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 55.0,
            "current_currency_to_idr": 15200,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }

        # SELL transaction
        sell_txn = {
            "row_number": 2,
            "currency_to_idr": 15200.0,
            "updated_executed_unit_price": 55.0,
            "updated_executed_quantity": 60.0,
            "transaction_time": datetime(2025, 1, 15, 12, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "SELL",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 5.0,
            "current_unit_price": 55.0,
            "current_currency_to_idr": 15200,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }
        
        all_txn = [MockRow(buy_txn), MockRow(sell_txn)]
        buy_types = ["BUY"]
        sell_types = ["SELL"]
        
        result = FlashGamesPnL.create_batches(all_txn, buy_types, sell_types)
        
        assert len(result) == 2
        
        # Find BUY and SELL transactions in result
        buy_result = next(r for r in result if r["row_number"] == 1)
        sell_result = next(r for r in result if r["row_number"] == 2)
        
        # BUY transaction should have remaining quantity after partial sell
        assert buy_result["transaction_type"] == "BUY"
        assert buy_result["remaining_quantity"] == 40.0  # 100 - 60 sold
        
        # BUY should have realized PnL from the sold portion
        expected_realized = int(60.0 * (55.0 * 15200 - 50.0 * 15000))
        assert buy_result["realized_pnl"] == expected_realized
        
        # SELL transaction
        assert sell_result["transaction_type"] == "SELL"
        assert sell_result["remaining_quantity"] == 0.0  # All quantity matched with BUY
        assert sell_result["is_pnl_eligible"] == False

    def test_create_batches_initial_balance(self):
        """Test create_batches with initial asset balance."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        class MockRow:
            def __init__(self, data):
                self._data = data
                for key, value in data.items():
                    setattr(self, key, value)
            
            def __getitem__(self, key):
                return self._data[key]
            
            def asDict(self):
                return self._data.copy()
        
        # Initial balance transaction
        initial_txn = {
            "row_number": 1,
            "currency_to_idr": 0,
            "updated_executed_unit_price": 0.0,
            "updated_executed_quantity": 50.0,
            "transaction_time": datetime(2025, 1, 15, 10, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "INITIAL_ASSET_BALANCE",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0,
            "current_unit_price": 0.0,
            "current_currency_to_idr": 0,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }
        
        all_txn = [MockRow(initial_txn)]
        buy_types = ["BUY"]
        sell_types = ["SELL"]
        
        result = FlashGamesPnL.create_batches(all_txn, buy_types, sell_types)
        
        assert len(result) == 1
        
        txn = result[0]
        assert txn["transaction_type"] == "INITIAL_ASSET_BALANCE"
        assert txn["remaining_quantity"] == 50.0
        assert txn["is_pnl_eligible"] == False
        assert txn["realized_pnl"] == 0

    def test_create_batches_crypto_futures(self):
        """Test create_batches with crypto futures transactions."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        class MockRow:
            def __init__(self, data):
                self._data = data
                for key, value in data.items():
                    setattr(self, key, value)
            
            def __getitem__(self, key):
                return self._data[key]
            
            def asDict(self):
                return self._data.copy()
        
        # Crypto futures BUY transaction
        futures_txn = {
            "row_number": 1,
            "currency_to_idr": 1.0,
            "updated_executed_unit_price": 0.001,
            "updated_executed_quantity": 1000.0,
            "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
            "leverage": 10,
            "asset_type": "crypto_futures",
            "transaction_type": "BUY",
            "asset_id": 101,
            "asset_sub_type": "crypto_future_trades",
            "fees": 2.0,
            "current_unit_price": 0.0012,
            "current_currency_to_idr": 1,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        }
        
        all_txn = [MockRow(futures_txn)]
        buy_types = ["BUY"]
        sell_types = ["SELL"]
        
        result = FlashGamesPnL.create_batches(all_txn, buy_types, sell_types)
        
        assert len(result) == 1
        
        txn = result[0]
        assert txn["asset_type"] == "crypto_futures"
        assert txn["transaction_type"] == "BUY"
        
        # For crypto futures, realized_pnl should include negative fees
        assert txn["realized_pnl"] == -2  # -1 * fees
        
        # Should have unrealized PnL calculated
        expected_unrealized = int(1000.0 * (0.0012 * 1 - 0.001 * 1.0))
        assert txn["unrealized_pnl"] == expected_unrealized

    def test_create_batches_empty_list(self):
        """Test create_batches with empty transaction list."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        result = FlashGamesPnL.create_batches([], ["BUY"], ["SELL"])
        
        assert isinstance(result, list)
        assert len(result) == 0

    def test_create_batches_sorting(self):
        """Test that transactions are sorted by row_number."""
        from src.jobs.trading_competition.flash_games_pnl import FlashGamesPnL
        
        class MockRow:
            def __init__(self, data):
                self._data = data
                for key, value in data.items():
                    setattr(self, key, value)
            
            def __getitem__(self, key):
                return self._data[key]
            
            def asDict(self):
                return self._data.copy()
        
        # Create transactions in reverse order
        txn_3 = MockRow({
            "row_number": 3,
            "currency_to_idr": 15000.0,
            "updated_executed_unit_price": 50.0,
            "updated_executed_quantity": 100.0,
            "transaction_time": datetime(2025, 1, 15, 13, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 52.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        })

        txn_1 = MockRow({
            "row_number": 1,
            "currency_to_idr": 15000.0,
            "updated_executed_unit_price": 50.0,
            "updated_executed_quantity": 100.0,
            "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
            "leverage": 1,
            "asset_type": "global_stocks",
            "transaction_type": "BUY",
            "asset_id": 1,
            "asset_sub_type": "global_stock_transactions",
            "fees": 0.0,
            "current_unit_price": 52.0,
            "current_currency_to_idr": 15100,
            # Initialize PnL fields
            "realized_pnl": 0,
            "unrealized_pnl": 0,
            "remaining_quantity": 0.0,
            "is_pnl_eligible": True
        })
        
        # Pass in reverse order
        all_txn = [txn_3, txn_1]
        
        result = FlashGamesPnL.create_batches(all_txn, ["BUY"], ["SELL"])
        
        # Should be sorted by row_number
        assert result[0]["row_number"] == 1
        assert result[1]["row_number"] == 3
