apiVersion: v1
kind: Pod
metadata:
  name: jenkins-slave
  labels:
    app: jenkins-slave
spec:
  containers:
    - name: "aws"
      image: "public.ecr.aws/c8p0n4f3/aws-jnlp-slave:12-jdk-alpine-aws-kctl-helm-v3"
      imagePullPolicy: "IfNotPresent"
      command:
        - "sleep"
      args:
        - "9999999"
      volumeMounts:
        - mountPath: "/home/<USER>/agent"
          name: "workspace-volume"
          readOnly: false
        - mountPath: "/home/<USER>/.aws/"
          name: aws-secrets
          readOnly: true
    - name: kaniko
      image: gcr.io/kaniko-project/executor:debug
      imagePullPolicy: IfNotPresent
      command:
        - sleep
      args:
        - 9999999
      volumeMounts:
        - mountPath: "/root/.aws/"
          name: aws-secrets
          readOnly: true
        # - name: jenkins-docker-cfg
        #   mountPath: /kaniko/.docker
    - name: node
      image: public.ecr.aws/c8p0n4f3/node:10-alpine
      imagePullPolicy: IfNotPresent
      command:
        - sleep
      args:
        - 9999999
  volumes:
    - name: jenkins-docker-cfg
      projected:
        sources:
          - secret:
              name: ecr-pull-secret
              items:
                - key: .dockercfg
                  path: config.json
    - name: aws-secrets
      projected:
        sources:
          - secret:
              name: aws-secret
          - secret:
              name: aws-config