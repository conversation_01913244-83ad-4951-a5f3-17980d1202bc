from datetime import date, timedelta, datetime, timezone
import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, LongType
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException
import builtins
python_round = builtins.round

schema_for_batch_flash_games = ArrayType(StructType([
        StructField("user_id", LongType(), True),
        StructField("transaction_id", LongType(), True),
        Struct<PERSON>ield("asset_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_sub_type", StringType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("transaction_time", TimestampType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("leverage", LongType(), True),
        StructField("fees", DoubleType(), True),
        StructField("updated_executed_quantity", DoubleType(), True),
        StructField("updated_executed_unit_price", DoubleType(), True),
        StructField("currency_to_idr", DoubleType(), True),
        StructField("current_currency_to_idr", LongType(), True),
        StructField("current_unit_price", DoubleType(), True),
        StructField("remaining_quantity", DoubleType(), True),
        StructField("realized_pnl", LongType(), True),
        StructField("unrealized_pnl", LongType(), True),
        StructField("execution_time", TimestampType(), True),
        StructField("trading_competition_start_time", TimestampType(), True),
        StructField("trading_competition_id", StringType(), True),
        StructField("flash_game_id", StringType(), True),
        StructField("row_number", IntegerType(), True),
        StructField("is_pnl_eligible", BooleanType(), True),

])
)
spark = SparkSession.builder.getOrCreate()
config = {
"flash_games":{
      "GSS_NFLX":{
          "assets":{
              "crypto_futures":[10006]
          },
          "start_ts": "2025-05-12 22:30:00.000000",
          "end_ts": "2025-07-30 17:00:00.000000"
      }
  },
"trading_competition":{
    "offset": 1,
    "frequency": 24,
    "id" : "2025-002",
    "start_time": "2025-05-01 17:00:00.000000",
    "end_time": "2025-09-30 16:59:59.999999"
  },
"buy_types": ["BUY", "LONG_OPEN", "DEPOSIT", "RECEIVE", "AIRDROP"],
  "sell_types": ["SELL", "LONG_CLOSE", "WITHDRAWAL", "SEND", "GOLD_WITHDRAWAL", "AIRDROP_SELL"]
}

class SparkUtils:
    def __init__(self, app_nane):
        self.app_name = app_nane

    def create_spark_session(self):
        spark = SparkSession.builder \
            .appName(self.app_name) \
            .getOrCreate()
        return spark

    def stop_spark(self, spark: SparkSession):
        spark.stop()

def get_utc_timestamp_from_string(ts):
    format_str = "%Y-%m-%d %H:%M:%S.%f"
    utc_ts = datetime.strptime(ts, format_str)
    return utc_ts

utc_cutoff_ts = "2025-07-01 17:00:00.000000"
# utc_cutoff_ts = get_utc_timestamp_from_string(utc_cutoff_ts)
flash_games = config["flash_games"]
account_id = 542632

class FlashGamesPnL:
    def __init__(self):
        self.current_flash_game_id = None
        self.config = config
        self.spark_utils = SparkUtils("Flash Games PnL")
        self.spark = self.spark_utils.create_spark_session()
        self.flash_games = config.get("flash_games", {})
        self.utc_cutoff_ts = get_utc_timestamp_from_string(utc_cutoff_ts)
        self.current_flash_game = None
        self.flash_game_end_ts = None
        self.flash_game_start_ts = None
        self.trading_competition_id = self.config["trading_competition"]["id"]
        self.trading_competition_start_time = get_utc_timestamp_from_string(self.config["trading_competition"]["start_time"])
        self.key_cols = ["account_id"]
        self.value_cols = ["asset_type", "user_id", "leverage", "asset_id", "asset_sub_type", "transaction_id",
                           "created", "updated", "fees",
                           "updated_executed_quantity", "updated_executed_unit_price",
                           "transaction_type", "currency_to_idr", "transaction_time",
                           "current_unit_price", "current_currency_to_idr", "remaining_quantity", "is_pnl_eligible", "realized_pnl",
                           "unrealized_pnl", "execution_time", "row_number",
                           "trading_competition_start_time", "trading_competition_id"]

    def get_current_flash_game(self):
        for game_name, game_data in self.flash_games.items():
            start_dt = get_utc_timestamp_from_string(game_data["start_ts"])
            end_dt = get_utc_timestamp_from_string(game_data["end_ts"])

            if start_dt < self.utc_cutoff_ts <= end_dt:
                self.flash_game_start_ts = start_dt
                self.flash_game_end_ts = end_dt
                return {
                    "flash_game_name": game_name,
                    **game_data
                }
        return None

    def get_current_flash_game_asset_id(self):
        asset_data = []
        assets = self.current_flash_game.get("assets", {})
        for asset_type, asset_ids in assets.items():
            if not asset_ids:
                asset_data.append({"asset_type": asset_type, "asset_id": 0})
            else:
                for asset_id in asset_ids:
                    asset_data.append({"asset_type": asset_type, "asset_id": asset_id})

        schema = StructType([
            StructField("asset_type", StringType(), True),
            StructField("asset_id", LongType(), True)
        ])
        return self.spark.createDataFrame(asset_data, schema=schema)

    def get_all_eligible_transactions(self, game_df, all_transactions):
        game = game_df.alias("g").select("asset_type", "asset_id")
        txn = all_transactions.alias("txn")

        join_condition = F.expr("""
            (g.asset_id = 0 AND g.asset_type = txn.asset_type)
            OR 
            (g.asset_id != 0 AND g.asset_type = txn.asset_type AND g.asset_id = txn.asset_id)
        """)
        eligible_txn = txn.join(game, on=join_condition, how="inner")
        print(f"eligible_txn df count is {eligible_txn.count()}")
        eligible_txn = eligible_txn.select(
            col("account_id"),
            col("user_id").cast("long"),
            col("txn.asset_id").cast("long"),
            col("leverage"),
            col("fees"),
            col("txn.asset_type"),
            col("transaction_id").cast("long"),
            col("created"),
            col("updated"),
            col("updated_executed_quantity").cast("double"),
            col("updated_executed_unit_price").cast("double"),
            col("transaction_type"),
            col("currency_to_idr").cast("double"),
            col("transaction_time"),
            col("asset_sub_type"),
            col("current_unit_price"),
            col("current_currency_to_idr")
        )
        eligible_txn = eligible_txn.withColumn("created", col("created").cast(TimestampType())) \
            .withColumn("updated", col("updated").cast(TimestampType())) \
            .withColumn("transaction_time", col("transaction_time").cast(TimestampType()))

        eligible_txn.select(
            F.max("transaction_time").alias("max_transaction_time"),
            F.min("transaction_time").alias("min_transaction_time")
        ).show()
        return eligible_txn

    def create_initial_position(self, all_transactions):
        all_transactions = all_transactions.filter(
            (col("transaction_time") < self.flash_game_start_ts) &
            (col("asset_sub_type") != "crypto_future_funding_transactions")
        )
        agg_df = all_transactions.groupBy("account_id", "user_id", "asset_id", "leverage", "asset_type").agg(
            F.coalesce(sum(when(col("transaction_type") == "BUY", col("updated_executed_quantity"))),
                       lit(0.0)).alias(
                "buy_qty"),
            F.coalesce(sum(when(col("transaction_type") == "SELL", col("updated_executed_quantity"))),
                       lit(0.0)).alias(
                "sell_qty")
        )
        agg_df.show(20, truncate=False)
        initials_df = agg_df.withColumn(
            "updated_executed_quantity",
            (col("buy_qty") - col("sell_qty")).cast("double")
        )
        initials_df = initials_df.withColumn("transaction_id", lit(0)) \
            .withColumn("transaction_time", lit(self.flash_game_start_ts)) \
            .withColumn("created", lit(self.flash_game_start_ts)) \
            .withColumn("updated", lit(self.flash_game_start_ts)) \
            .withColumn("updated_executed_unit_price", lit(0.0)) \
            .withColumn("fees", lit(0)) \
            .withColumn("currency_to_idr", lit(0)) \
            .withColumn("current_unit_price", lit(0.0)) \
            .withColumn("current_currency_to_idr", lit(0)) \
            .withColumn("transaction_type", lit("INITIAL_WALLET_BALANCE")) \
            .withColumn("asset_sub_type", when(col("asset_type") == "gold", "gold_transactions")
                        .when(col("asset_type") == "crypto_currency", "crypto_currency_transactions")
                        .when(col("asset_type") == "global_stocks", "global_stock_transactions")
                        .when(col("asset_type") == "global_stock_options", "options_contract_transactions")
                        .when(col("asset_type") == "crypto_futures", "crypto_future_trades")
                        .otherwise(None)
                        )
        initials_df = initials_df.select(
            col("account_id"),
            col("user_id"),
            col("asset_id"),
            col("leverage"),
            col("fees"),
            col("asset_type"),
            col("transaction_id").cast("long"),  # integer -> string
            col("created"),
            col("updated"),
            col("updated_executed_quantity"),  # double -> string
            col("updated_executed_unit_price"),  # double -> string
            col("transaction_type"),
            col("currency_to_idr"),  # integer -> string
            col("transaction_time"),
            col("asset_sub_type"),
            col("current_unit_price"),
            col("current_currency_to_idr")
        )
        initials_df.select(
            F.max("transaction_time").alias("max_transaction_time"),
            F.min("transaction_time").alias("min_transaction_time")
        ).show()
        return initials_df

    @staticmethod
    def create_batches(all_txn, buy_types, sell_types):
        all_sorted_txn = sorted(all_txn, key=lambda txn: txn["row_number"])

        result = []

        for index, transaction in enumerate(all_sorted_txn):
            row = transaction.asDict()
            currency_to_idr = row["currency_to_idr"]
            executed_unit_price = row["updated_executed_unit_price"]
            executed_quantity = row["updated_executed_quantity"]
            transaction_time = row["transaction_time"]
            leverage = row["leverage"] if row["leverage"] > 0 else 1

            if row["asset_type"] == "crypto_futures":
                row["realized_pnl"] = int(-1 * row["fees"])
                row["remaining_quantity"] = executed_quantity
                running_quantity = 0.0
                for result_row in result:
                    if (result_row["asset_id"] == row["asset_id"]) and (
                            result_row["asset_sub_type"] == row["asset_sub_type"]):
                        if result_row["transaction_type"] in buy_types:
                            running_quantity = running_quantity + result_row["updated_executed_quantity"]
                        else:
                            running_quantity = running_quantity - result_row["updated_executed_quantity"]
                if row["transaction_type"] in buy_types:
                    new_running_quantity = running_quantity + row["updated_executed_quantity"]
                else:
                    new_running_quantity = running_quantity - row["updated_executed_quantity"]
                new_running_quantity = python_round(new_running_quantity, 4)
                running_quantity = python_round(running_quantity, 4)
                if (new_running_quantity >= 0 and running_quantity < 0) or (
                        new_running_quantity <= 0 and running_quantity > 0):
                    for result_row in result:
                        if (result_row["asset_id"] == row["asset_id"]) and (
                                result_row["asset_sub_type"] == row["asset_sub_type"]):
                            remaining_quantity = result_row["remaining_quantity"] if result_row[
                                                                                         "transaction_type"] in buy_types else -1 * \
                                                                                                                               result_row[
                                                                                                                                   "remaining_quantity"]
                            result_row["realized_pnl"] += int(remaining_quantity * (
                                    executed_unit_price * currency_to_idr -
                                    result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                            ))
                            result_row["remaining_quantity"] = 0.0
                    row["remaining_quantity"] = abs(new_running_quantity)

            elif row["transaction_type"] == "INITIAL_WALLET_BALANCE":
                row["remaining_quantity"] = executed_quantity
                row["is_pnl_eligible"] = False
            elif row["transaction_type"] in buy_types:
                row["remaining_quantity"] = executed_quantity
                row["is_pnl_eligible"] = True
            elif row["transaction_type"] in sell_types:
                remaining_sell_quantity = executed_quantity

                for result_row in result:
                    if (result_row["transaction_type"] == "INITIAL_WALLET_BALANCE") and (
                            result_row["asset_type"] == row["asset_type"]) and (
                            result_row["leverage"] == row["leverage"]) and (
                            result_row["asset_id"] == row["asset_id"]) and (
                            result_row["remaining_quantity"] > 0) and (
                            remaining_sell_quantity > 0):
                        quantity_settled = min(result_row["remaining_quantity"], remaining_sell_quantity)
                        remaining_sell_quantity -= quantity_settled
                        result_row["remaining_quantity"] -= quantity_settled
                        result_row["realized_pnl"] += 0

                for result_row in result:
                    if (result_row["transaction_type"] in buy_types) and (
                            result_row["asset_type"] == row["asset_type"]) and (
                            result_row["leverage"] == row["leverage"]) and (
                            result_row["asset_id"] == row["asset_id"]) and (
                            result_row["remaining_quantity"] > 0) and (
                            remaining_sell_quantity > 0):
                        quantity_sold = min(result_row["remaining_quantity"], remaining_sell_quantity)
                        remaining_sell_quantity -= quantity_sold
                        result_row["remaining_quantity"] -= quantity_sold

                        if (row["asset_sub_type"] != "crypto_currency_wallet_transfers") and (
                                row["asset_sub_type"] != "gold_withdrawals") and (
                                row["transaction_type"] != "AIRDROP_SELL"):
                            result_row["realized_pnl"] += int(quantity_sold * (
                                    executed_unit_price * currency_to_idr -
                                    result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                            ))
                row["remaining_quantity"] = remaining_sell_quantity
                row["is_pnl_eligible"] = False

            result.append(row)
        print(f" Result is ")
        df = pd.DataFrame(result)
        df.to_csv('/Users/<USER>/Desktop/playground/resources/udf_res.csv', index=False)
        print("Inside unrealized pnl calculation")
        for index, result_row in enumerate(result):
            print(f"row_number is {result_row['row_number']}")
            remaining_quantity = result_row["remaining_quantity"]
            if (result_row["transaction_type"] in buy_types) and (
                    result_row["remaining_quantity"] > 0) and result_row.get("is_pnl_eligible", True):
                print(f"calculating unrealized pnl for row - {result_row['row_number']}")
                result_row["unrealized_pnl"] = int(remaining_quantity * (
                        result_row["current_unit_price"] * result_row["current_currency_to_idr"]
                        - result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                ))

        return result

    def get_batches(self, transactions):
        transactions = transactions.withColumn("remaining_quantity", lit(0.0)) \
            .withColumn("realized_pnl", lit(0)) \
            .withColumn("unrealized_pnl", lit(0)) \
            .withColumn("is_pnl_eligible", lit(True))

        transactions = transactions.withColumn("execution_time", lit(self.utc_cutoff_ts)) \
            .withColumn("trading_competition_start_time", lit(self.trading_competition_start_time)) \
            .withColumn("trading_competition_id", lit(self.trading_competition_id)) \
            .withColumn("flash_game_id", lit(self.current_flash_game_id)) \
            .withColumn("priority", when(col("transaction_type") == "INITIAL_WALLET_BALANCE", 1).otherwise(2))
        window_spec = Window.partitionBy(self.key_cols).orderBy(col("transaction_time").asc(),
                                                                col("priority").asc())
        df_with_row_number = transactions.withColumn("row_number", row_number().over(window_spec)).drop("priority")

        grouped_df = (
            df_with_row_number
            .groupBy(self.key_cols)
            .agg(F.collect_list(F.struct(*[col(c) for c in self.value_cols])).alias("transactions"))
        )
        buy_types_list = self.config["buy_types"]
        sell_types_list = self.config["sell_types"]
        batch_udf = F.udf(lambda txns: FlashGamesPnL.create_batches(txns, buy_types_list, sell_types_list),
                          schema_for_batch_flash_games)

        batches = (
            grouped_df
            .withColumn("batch_result", batch_udf(col("transactions")))
            .select("account_id", "batch_result")
        )

        exploded_batch = batches.withColumn("batch_data", F.explode(col("batch_result"))).select(
            "account_id", "batch_data.*"
        )
        exploded_batch = exploded_batch.withColumn("remaining_quantity", F.round(col("remaining_quantity"), 12))
        return exploded_batch

    def execute(self):
        self.current_flash_game = self.get_current_flash_game()
        self.current_flash_game_id = self.current_flash_game['flash_game_name']
        print("current_flash_game_id", self.current_flash_game_id)
        current_flash_game_assets_df = self.get_current_flash_game_asset_id()
        print("current_flash_game_assets_df")
        print(current_flash_game_assets_df)

        path = '/Users/<USER>/Downloads/crypto_futures_testing.csv'
        all_transactions = self.spark.read.option("recursiveFileLookup", "true").csv(path, header=True, inferSchema=True,quote='"', escape='"', multiLine=True)
        all_transactions = all_transactions.select("account_id", "user_id", "asset_id", "leverage", "fees", "asset_type", "transaction_id","created", "updated", "updated_executed_quantity", "updated_executed_unit_price","transaction_type", "currency_to_idr", "transaction_time", "asset_sub_type","current_unit_price", "current_currency_to_idr")

        # all_transactions = all_transactions.filter(col("asset_id") == 10020)
        eligible_txn = self.get_all_eligible_transactions(current_flash_game_assets_df, all_transactions)
        flash_game_txn = eligible_txn.filter(col("transaction_time") >= self.flash_game_start_ts)
        initial_asset_balance = self.create_initial_position(eligible_txn)
        transformed_flash_game_txn = flash_game_txn.union(initial_asset_balance)

        transformed_flash_game_txn = transformed_flash_game_txn.withColumn(
            "updated_executed_quantity",
            when(col("transaction_id") == 227548, col("updated_executed_quantity") / 2)
            .otherwise(col("updated_executed_quantity"))
        )

        batches = self.get_batches(transformed_flash_game_txn)
        # batches = self.cast_fields(batches)
        # batches = batches.select("asset_id", "asset_type", "account_id", "user_id", "transaction_id",
        #                          "asset_sub_type", "created", "updated", "transaction_time", "transaction_type",
        #                          "leverage", "fees",
        #                          "currency_to_idr", "current_unit_price", "current_currency_to_idr",
        #                          "remaining_quantity", "realized_pnl", "unrealized_pnl", "execution_time",
        #                          "trading_competition_start_time", "trading_competition_id", "flash_game_id",
        #                          "row_number", "updated_executed_quantity",
        #                          "updated_executed_unit_price")
        batches = batches.select("asset_id", "asset_type","account_id",
                                 "asset_sub_type", "transaction_time", "transaction_type",
                                 "leverage", "fees",
                                 "currency_to_idr", "current_unit_price", "current_currency_to_idr",
                                 "remaining_quantity", "realized_pnl", "unrealized_pnl",
                                 "row_number", "updated_executed_quantity",
                                 "updated_executed_unit_price")
        batches.coalesce(1).write.mode("overwrite").csv('/Users/<USER>/Desktop/playground/resources/crypto_futures_old_logic', header=True)

        # flash_game_pnl = self.process_flash_game_pnl(batches)

if __name__ == "__main__":
    pnl = FlashGamesPnL()
    pnl.execute()
