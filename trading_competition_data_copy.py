from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException
from py4j.java_gateway import java_import


spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

# Replace with your actual file path
csv_file_path = "s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/flash_game_transaction_batches/current/"
df = spark.read.option("recursiveFileLookup", "true").csv(csv_file_path, header=True, inferSchema=True,
                                     quote='"', escape='"', multiLine=True)


# Count SELL transactions per account_id
sell_counts = (
    df.filter(F.col("transaction_type") == "SELL")
      .groupBy("account_id")
      .count()
      .withColumnRenamed("count", "sell_count")
)

# Existing logic for asset_type coverage
all_asset_types = [row.asset_type for row in df.select("asset_type").distinct().collect()]
account_asset_counts = (
    df.select("account_id", "asset_type")
      .distinct()
      .groupBy("account_id")
      .agg(F.countDistinct("asset_type").alias("asset_type_count"))
      .filter(F.col("asset_type_count") == len(all_asset_types))
)

# Join with sell_counts and filter for sell_count >= 20
account_asset_counts = (
    account_asset_counts.join(sell_counts, on="account_id")
                       .filter(F.col("sell_count") >= 20)
)

# Top 5 account_ids with most records
top_accounts = (
    df.join(account_asset_counts.select("account_id"), on="account_id")
      .groupBy("account_id")
      .count()
      .orderBy(F.desc("count"))
      .limit(5)
)

top_accounts.show()
