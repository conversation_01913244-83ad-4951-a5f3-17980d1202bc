#!/bin/bash

# Pre-deployment test script for PluangSparkBatchProcessingJobs
# This script runs the test suite before deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VENV_PATH="${PROJECT_ROOT}/venv"
TEST_RESULTS_DIR="${PROJECT_ROOT}/test-results"
COVERAGE_DIR="${PROJECT_ROOT}/coverage"

print_status "Starting pre-deployment tests..."
print_status "Project root: $PROJECT_ROOT"

# Check if we're on master branch (optional)
if command -v git &> /dev/null; then
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    print_status "Current branch: $CURRENT_BRANCH"
    
    if [ "$CURRENT_BRANCH" != "master" ] && [ "$CURRENT_BRANCH" != "main" ]; then
        print_warning "Not on master/main branch. Tests will still run but this is typically for master branch deployments."
    fi
fi

# Create directories
print_status "Creating test result directories..."
mkdir -p "$TEST_RESULTS_DIR"
mkdir -p "$COVERAGE_DIR"

# Setup virtual environment
print_status "Setting up Python virtual environment..."
if [ ! -d "$VENV_PATH" ]; then
    python3 -m venv "$VENV_PATH"
fi

# Activate virtual environment
source "$VENV_PATH/bin/activate"

# Upgrade pip and install dependencies
print_status "Installing dependencies..."
pip install --upgrade pip
pip install -r "$PROJECT_ROOT/requirements-test.txt"

# Set environment variables
export JAVA_HOME=${JAVA_HOME:-"/usr/lib/jvm/java-11-openjdk-amd64"}
export SPARK_LOCAL_IP="127.0.0.1"
export PYTHONPATH="$PROJECT_ROOT/src"
export ENABLE_JSON_LOGGING="false"

print_status "Environment variables set:"
print_status "  JAVA_HOME: $JAVA_HOME"
print_status "  SPARK_LOCAL_IP: $SPARK_LOCAL_IP"
print_status "  PYTHONPATH: $PYTHONPATH"

# Change to project root
cd "$PROJECT_ROOT"

# Run the tests
print_status "Running pre-deployment test suite..."

# Define the test command
TEST_CMD="python -m pytest \
    tests/test_flash_games_pnl_no_spark.py \
    tests/test_transaction_transformer_no_spark.py \
    tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_init \
    tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_run_method \
    tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_execute_no_active_game \
    tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_get_current_flash_game_none_active \
    tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_create_batches_static_method \
    tests/test_transaction_transformer.py::TestTransactionTransformer::test_init \
    tests/test_transaction_transformer.py::TestTransactionTransformer::test_run \
    tests/test_flash_games_pnl_integration.py::TestFlashGamesPnLIntegration::test_run_method_integration \
    tests/test_flash_games_pnl_integration.py::TestFlashGamesPnLIntegration::test_complex_pnl_calculation_scenarios \
    -v \
    --tb=short \
    --junitxml=$TEST_RESULTS_DIR/test-results.xml \
    --cov=src \
    --cov-report=xml:$COVERAGE_DIR/coverage.xml \
    --cov-report=html:$COVERAGE_DIR/htmlcov \
    --cov-report=term-missing \
    --cov-fail-under=70"

# Execute tests
if eval $TEST_CMD; then
    print_success "All pre-deployment tests passed! ✅"
    print_success "Test results saved to: $TEST_RESULTS_DIR/test-results.xml"
    print_success "Coverage report saved to: $COVERAGE_DIR/coverage.xml"
    print_success "HTML coverage report: $COVERAGE_DIR/htmlcov/index.html"
    
    # Display test summary
    echo ""
    echo "=== TEST SUMMARY ==="
    python -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$TEST_RESULTS_DIR/test-results.xml')
    root = tree.getroot()
    tests = root.get('tests', '0')
    failures = root.get('failures', '0')
    errors = root.get('errors', '0')
    time = root.get('time', '0')
    print(f'Tests run: {tests}')
    print(f'Failures: {failures}')
    print(f'Errors: {errors}')
    print(f'Time: {time}s')
    if int(failures) == 0 and int(errors) == 0:
        print('✅ All tests passed!')
    else:
        print('❌ Some tests failed!')
except:
    print('Could not parse test results')
"
    
    exit 0
else
    print_error "Pre-deployment tests failed! ❌"
    print_error "Deployment should be aborted."
    print_error "Check test results in: $TEST_RESULTS_DIR/test-results.xml"
    
    # Show failed tests if available
    if [ -f "$TEST_RESULTS_DIR/test-results.xml" ]; then
        echo ""
        echo "=== FAILED TESTS ==="
        python -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$TEST_RESULTS_DIR/test-results.xml')
    root = tree.getroot()
    for testcase in root.findall('.//testcase'):
        failure = testcase.find('failure')
        error = testcase.find('error')
        if failure is not None or error is not None:
            classname = testcase.get('classname', '')
            name = testcase.get('name', '')
            print(f'❌ {classname}::{name}')
            if failure is not None:
                print(f'   Failure: {failure.get(\"message\", \"\")}')
            if error is not None:
                print(f'   Error: {error.get(\"message\", \"\")}')
except:
    print('Could not parse failed tests')
"
    fi
    
    exit 1
fi
