#!/bin/bash

# FlashGamesPnL CI Test Runner
# This script runs all tests in the correct order for CI/CD environments

set -e  # Exit on any error

echo "🚀 Starting FlashGamesPnL CI Test Suite"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Set environment variables
export SPARK_LOCAL_IP=127.0.0.1
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

print_status "Environment variables set"

# Check Python version
python_version=$(python --version 2>&1)
print_status "Using $python_version"

# Check Java version
if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -n 1)
    print_status "Using $java_version"
else
    print_error "Java not found! Please install Java 11+"
    exit 1
fi

# Install dependencies
print_status "Installing test dependencies..."
pip install -r requirements-test.txt

# Run tests in order of complexity and speed

echo ""
echo "📋 Test Execution Plan:"
echo "1. Core Logic Tests (No Spark) - ~10 seconds"
echo "2. Unit Tests (With Spark) - ~2-3 minutes"
echo "3. Integration Tests - ~1-2 minutes"
echo "4. Coverage Report"
echo ""

# 1. Run simple tests first (fastest feedback)
echo "🧪 Running Core Logic Tests (No Spark Required)..."
if python run_simple_tests.py; then
    print_status "Core logic tests passed"
else
    print_error "Core logic tests failed"
    exit 1
fi

echo ""

# 2. Run unit tests with Spark
echo "🧪 Running Unit Tests (With Spark)..."
if SPARK_LOCAL_IP=127.0.0.1 python -m pytest tests/test_flash_games_pnl.py -v --tb=short; then
    print_status "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

echo ""

# 3. Run integration tests
echo "🧪 Running Integration Tests..."
if SPARK_LOCAL_IP=127.0.0.1 python -m pytest tests/test_flash_games_pnl_integration.py -v --tb=short; then
    print_status "Integration tests passed"
else
    print_error "Integration tests failed"
    exit 1
fi

echo ""

# 4. Run all tests with coverage
echo "📊 Running Full Test Suite with Coverage..."
if SPARK_LOCAL_IP=127.0.0.1 python -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing --cov-fail-under=70; then
    print_status "Full test suite with coverage passed"
else
    print_error "Coverage requirements not met"
    exit 1
fi

echo ""
echo "🎉 All tests passed successfully!"
echo ""
echo "📊 Test Summary:"
echo "- Core Logic Tests: ✅ Passed"
echo "- Unit Tests: ✅ Passed" 
echo "- Integration Tests: ✅ Passed"
echo "- Coverage: ✅ Above 70%"
echo ""
echo "📁 Coverage report generated in: htmlcov/index.html"
echo ""
print_status "CI Test Suite completed successfully!"
