import pendulum
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from airflow.models import XCom
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.utils.db import provide_session
from airflow.utils.helpers import chain
from airflow_kubernetes_job_operator.kubernetes_job_operator import (
    KubernetesJobOperator,
)
from datetime import timedelta

from alerting.dag_alert import DagAlert
from base_dags.base_dag import BASE_DAG

USERNAME = "airflow"
config = Variable.get("crypto_futures_env_variables", deserialize_json=True)
IMAGE = config.get("image")
SLACK_CONN_ID = "slack_default"
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
year, month, day = 2025, 3, 2
slack_alert = DagAlert(USERNAME, SLACK_CONN_ID,None)


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 0,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert.slack_alert_failure
}

dag_id = "DE-CRYPTO-FUTURES-FUNDING-TIME-VALIDATOR"
app_store_sync_base_dag = BASE_DAG (
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    catchup=False,
    schedule_interval="0 */1 * * *",
    tags=["data-eng", "crypto"],
    team="data-eng"
)

DAG = app_store_sync_base_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=1),
    max_active_runs=1
)

globals()[dag_id] = DAG

########################################################################################################################

crypto_futures_funding_validation_task = KubernetesJobOperator(
    task_id="crypto-futures-funding-time-validation",
    namespace=NAME,
    image=IMAGE,
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy="Always",
    get_logs=True,
    dag=globals()[dag_id]
)

chain(crypto_futures_funding_validation_task)
