# from datetime import datetime
# import json
# import json_logging
#
#
# class CustomJSONLog(json_logging.formatters.JSONLogFormatter):
#     """
#     Customized logger
#     """
#
#     def format(self, record):
#         utcnow = datetime.utcnow()
#         json_customized_log_object = ({
#             "timestamp": json_logging.util.iso_time_format(utcnow),
#             "msg": record.getMessage(),
#             "type": "log",
#             "logger_name": record.name,
#             "thread_name": record.threadName,
#             "level": record.levelname,
#             "module": record.module,
#             "line_no": record.lineno})
#
#         return json.dumps(json_customized_log_object)
