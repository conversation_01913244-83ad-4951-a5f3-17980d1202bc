import logging as root_logging
import sys

from slack_sdk import WebClient


class Slack(object):
    def __init__(self, slack_bot_token: str):
        self.logger = root_logging.getLogger("Slack")
        self.logger.setLevel(root_logging.DEBUG)
        self.logger.addHandler(root_logging.StreamHandler(sys.stdout))
        self.client = WebClient(slack_bot_token)
        self.logger.info(f"{Slack.__name__} Init!")

    def post_message(self, channel, text: str, **kwargs):
        try:
            self.client.chat_postMessage(
                channel=channel,
                text=text,
                **kwargs,
            )
            self.logger.info(f"{Slack.post_message.__name__}: Message Posted!")
        except Exception as ex:
            self.logger.exception(f"{Slack.post_message.__name__}: {ex}")
            raise Exception(f"{Slack.post_message.__name__}: {ex}")

    def upload_as_text_file(
            self, channel: str, file_title: str, file_name, content: str
    ):
        try:
            file_meta = self.client.files_upload(
                title=file_title, filename=file_name, content=content
            )
            file_url = file_meta.get("file").get("permalink")
            self.client.chat_postMessage(
                channel=f"#{channel}",
                text=f"{file_url}",
            )
            self.logger.info(
                f"{Slack.upload_as_text_file.__name__}: Uploaded as Text File"
            )
        except Exception as ex:
            self.logger.exception(f"{Slack.upload_as_text_file.__name__}: {ex}")
            raise Exception(f"{Slack.upload_as_text_file.__name__}: {ex}")

    def upload_file(
            self, channels: str, msg: str, filename: list, content, **kwargs: dict
    ):
        try:
            self.client.files_upload(
                channels=channels,
                initial_comment=msg,
                filename=filename,
                content = content,
                **kwargs,
            )
            self.logger.info(
                f"{Slack.upload_file.__name__}: File Uploaded: {filename}"
            )
        except Exception as ex:
            self.logger.exception(f"{Slack.upload_file.__name__}: {ex}")
            raise Exception(f"{Slack.upload_file.__name__}: {ex}")

    def upload_files(self, channel: str, message: str, file_list: list):
        try:
            for file in file_list:
                upload = self.client.files_upload(file=file)
                message = f"{message}<{upload['file']['permalink']}| >"
            self.client.chat_postMessage(channel=channel, text=message)
        except Exception as ex:
            self.logger.exception(f"{Slack.upload_file.__name__}: {ex}")
            raise Exception(f"{Slack.upload_file.__name__}: {ex}")
