import logging
import boto3
import json
from botocore.exceptions import ClientError
import os
import pytz
from datetime import datetime

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

def process_bq_records(records):
    import datetime

    processed_records = list()
    for record in records:
        temp_row = list()
        for column, row in record.items():
            temp_row.append(row)
        processed_records.append(temp_row)
    return processed_records
