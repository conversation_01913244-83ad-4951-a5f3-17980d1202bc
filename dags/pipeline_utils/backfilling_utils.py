from airflow.models import Variable
from airflow.models import XCom
from airflow.utils.db import provide_session
from airflow.providers.amazon.aws.hooks.emr import <PERSON>rHook
from typing import Dict, Optional, Union, Any
from datetime import timedelta, datetime
import pendulum


@provide_session
def clean_xcom(session=None, **context):
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()


class BackfillingUtils:
    START_DATE = ['2022-07-21']
    END_DATE = ['2022-07-23']
    ASSETS = ['cashin']
    try:
        backfill_config = Variable.get("BACKFILL_CONFIG", deserialize_json=True)
        START_DATE = backfill_config.get('start_date')
        END_DATE = backfill_config.get('end_date')
        ASSETS = backfill_config.get('asset')
        ENV = backfill_config.get('ENV')
    except:
        START_DATE = []
        END_DATE = []
        ASSETS = []

    def __init__(
            self,
            files_path: str,
            jar_path: str,
            job_config_path: str,
            resource_config: Dict,
            log_path: str,
            env: str,
    ) -> None:
        self.files_path = files_path
        self.jar_path = jar_path
        self.job_config_path = job_config_path
        self.resource_config = resource_config
        self.log_path = log_path
        self.env = env

    def x_com_pull(task_ids, key, context):
        ti = context['ti']
        data = ti.xcom_pull(task_ids=task_ids, key=key)
        return data

    def get_run_configs(self, asset):
        asset_name = asset
        start_date = "NA"
        end_date = "NA"
        for i in range(len(self.ASSETS)):
            if self.ASSETS[i] == asset:
                asset_name = asset
                start_date = self.START_DATE[i]
                end_date = self.END_DATE[i]
        return asset_name, start_date, end_date

    def execute_gold_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("gold_returns")
        steps = [
            {
                "Name": "execute_gold_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/gold/snap_gold_returns.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name

                    ]
                },
            },
        ]
        return steps

    def execute_bappebti_wallets_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("bappebti_wallet")
        steps = [
            {
                "Name": "execute_bappebti_wallets_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/bappebti_wallets/snap_bappebti_wallet.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_crypto_avg_hold_time(self):
        asset_name, start_date, end_date = self.get_run_configs("crypto_avg_hold_time")
        steps = [
            {
                "Name": "execute_crypto_avg_hold_time",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/crypto_currency/crypto_avg_holding_time.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_crypto_buy_sell_ratio(self):
        asset_name, start_date, end_date = self.get_run_configs("crypto_buy_sell_ratio")
        steps = [
            {
                "Name": "execute_crypto_buy_sell_ratio",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/crypto_currency/crypto_buy_sell_ratio.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name

                    ]
                },
            },
        ]
        return steps

    def execute_forex_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("forex_returns")
        steps = [
            {
                "Name": "execute_forex_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/forex/snap_forex_returns.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_fund_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("fund_returns")
        steps = [
            {
                "Name": "execute_fund_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/fund/snap_fund_returns_usd.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def copy_spark_logs_hdfs_to_s3(self, date):
        dt = date.strftime("%Y-%m-%d")
        steps = [
            {
                "Name": "Copy-Spark-logs",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "s3-dist-cp",
                        "--src=hdfs:///var/log/spark/apps",
                        f'--dest=s3://'+self.log_path+'/app_log/dt='+dt+'/.'
                    ],
                },
            },
        ]
        return steps

    def execute_stock_index_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("stock_index_returns")
        steps = [
            {
                "Name": "execute_stock_index_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/stock_index/snap_stock_index_returns.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_crypto_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("crypto_returns")
        steps = [
            {
                "Name": "execute_crypto_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/crypto_currency/snap_crypto_currency_returns.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_cashin_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("cashin")
        steps = [
            {
                "Name": "execute_cashin_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/cashin/snap_cashin.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_cashout_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("cashouts")
        steps = [
            {
                "Name": "execute_cashout_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/cashout/snap_cashouts.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_global_stock_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("global_stocks_returns")
        steps = [
            {
                "Name": "execute_global_stock_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/global_stock/snap_global_stock_returns.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_gold_gift_and_withdrawal_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("global_stocks_dividend")
        steps = [
            {
                "Name": "execute_gold_gift_and_withdrawal_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/gold_gift_and_withdrawal/snap_gold_gift_and_withdrawal.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_portfolio_snapshot(self):
        asset_name, start_date, end_date = self.get_run_configs("portfolio")
        steps = [
            {
                "Name": "execute_portfolio_snapshot",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/portfolio/snap_portfolio.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_daily_interm_pnl_calculation(self):
        asset_name, start_date, end_date = self.get_run_configs("daily_aggregate_profit_loss_value")
        steps = [
            {
                "Name": "execute_daily_interm_pnl_calculation",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/pnl_calculation/daily_aggregate_profit_loss_value.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_daily_pnl_calculation(self):
        asset_name, start_date, end_date = self.get_run_configs("daily_pnl")
        steps = [
            {
                "Name": "execute_daily_pnl_calculation",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/pnl_calculation/daily_pnl.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_weekly_pnl_calculation(self):
        asset_name, start_date, end_date = self.get_run_configs("weekly_pnl")
        steps = [
            {
                "Name": "execute_weekly_pnl_calculation",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/pnl_calculation/weekly_pnl.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_monthly_pnl_calculation(self):
        asset_name, start_date, end_date = self.get_run_configs("monthly_pnl")
        steps = [
            {
                "Name": "execute_monthly_pnl_calculation",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/pnl_calculation/monthly_pnl.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_quarterly_pnl_calculation(self):
        asset_name, start_date, end_date = self.get_run_configs("quarterly_pnl")
        steps = [
            {
                "Name": "execute_quarterly_pnl_calculation",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/pnl_calculation/quarterly_pnl.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_yearly_pnl_calculation(self):
        asset_name, start_date, end_date = self.get_run_configs("yearly_pnl")
        steps = [
            {
                "Name": "execute_yearly_pnl_calculation",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/pnl_calculation/yearly_pnl.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_indo_stock_returns(self):
        asset_name, start_date, end_date = self.get_run_configs("indo_stocks_returns")
        steps = [
            {
                "Name": "execute_indo_stock_returns",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/indo_stocks/snap_indo_stock_returns.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps

    def execute_indo_stock_dividend(self):
        asset_name, start_date, end_date = self.get_run_configs("indo_stocks_dividend")
        steps = [
            {
                "Name": "execute_indo_stock_dividend",
                "ActionOnFailure": "CONTINUE",
                "HadoopJarStep": {
                    "Jar": "command-runner.jar",
                    "Args": [
                        "spark-submit",
                        "--deploy-mode",
                        "cluster",
                        "--executor-memory",
                        "5G",
                        "--num-executors",
                        "2",
                        "--executor-cores",
                        "3",
                        "--py-files",
                        "s3://" + self.files_path + "/common.py,s3://" + self.files_path + "/structs.py,s3://" + self.files_path + "/indo_stocks/snap_indo_stock_dividend.py",
                        "--files",
                        self.job_config_path + "/{{ params.config }}",
                        "s3://" + self.files_path + "/backFilling/backfilling.py",
                        "--start_date",
                        start_date,
                        "--end_date",
                        end_date,
                        "--asset",
                        asset_name
                    ]
                },
            },
        ]
        return steps
