from airflow.models import Variable
from airflow.models import XCom
from google.cloud import bigquery
from google.oauth2 import service_account
from google.api_core.exceptions import BadRequest
import json,logging, os
from datetime import datetime, timedelta
import pytz, sys
import boto3


ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)

s3_to_bq_utils_dir = 'dags/pipeline_utils/s3_to_bq'
config_path = os.path.join(s3_to_bq_utils_dir, 'config/config.json')
bq_schema_path = os.path.join(s3_to_bq_utils_dir, f'config/bq_table_schema.json')
sys.path.append(s3_to_bq_utils_dir)


ENV = Variable.get("ENV")
s3_to_bq_sync_config = Variable.get("s3_to_bq_sync_config", deserialize_json=True)
service_account_file = Variable.get("de_bq_secret", deserialize_json=True)
aws_credentials = Variable.get("emr_dag_config", deserialize_json=True)

context_global = None

if logging.getLogger().hasHandlers():
    logging.getLogger().setLevel(logging.INFO)
else:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


with open(config_path) as config_file:
    config_data = json.load(config_file)
bq_credentials = service_account.Credentials.from_service_account_info(service_account_file)
time_zone = pytz.timezone(s3_to_bq_sync_config['time_zone'])

failure_tables = []

try:
    BQ_CLIENT = bigquery.Client(credentials=bq_credentials)
    logging.info("Successfully created the big query connection {}".format(BQ_CLIENT))
except Exception as e:
    logging.exception(e)
    raise e


def get_bq_table_schema(bq_table=None):
    with open(bq_schema_path) as schema_file:
        schema_data = json.load(schema_file)
    try:
        schema_config = []
        if bq_table not in schema_data:
            return None
        for column in schema_data[bq_table]:
            if "fields" in column:
                fields_list = []
                for field in column["fields"]:
                    fields_list.append(bigquery.SchemaField(field["name"], field["type"], mode=field["mode"]))
                schema_config.append(
                    bigquery.SchemaField(column["name"], column["type"], mode=column["mode"], fields=fields_list))
            else:
                schema_config.append(bigquery.SchemaField(column["name"], column["type"], mode=column["mode"]))
    except Exception as e:
        logging.exception(e)
        raise e
    else:
        return schema_config

def load_data_to_bq_json(asset_name, s3_file_key, s3_to_bq_synced_files, bq_dataset_table, data):
    logging.info("Starting the Big Load Operation")
    try:
        bq_table = bq_dataset_table.split('.')[-1]
        bq_dataset = bq_dataset_table.split('.')[0]
        bq_dataset_in_config = bq_dataset if (s3_to_bq_sync_config.get("dataset_mapping").get(bq_dataset) is None) else s3_to_bq_sync_config.get("dataset_mapping").get(bq_dataset)
        schema = get_bq_table_schema(bq_table)
        job_config = bigquery.LoadJobConfig(write_disposition=bigquery.WriteDisposition.WRITE_APPEND, source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON)
        job_config.schema = schema
        load_job = BQ_CLIENT.load_table_from_json(json.loads(data.read().decode("utf-8")), "{}.{}".format(bq_dataset_in_config, bq_table), job_config=job_config, timeout=400000)
        logging.info("Starting job with id: {}".format(load_job.job_id))
        try:
            load_job.result()
            s3_to_bq_synced_files[s3_file_key] = 1
            XCom.set(key="s3_to_bq_files",
                     value=s3_to_bq_synced_files,
                     task_id="s3_to_bq_sync_files",
                     dag_id=context_global.get('task_instance').dag_id,
                     execution_date=context_global.get('execution_date')
                     )
            newrelic_metrics_client.send_newrelic_gauge_metrics("de-s3-to-bq.sync-status", 1, asset_name, metrics="sync_status")
            logging.info("BQ table updated successfully")
        except BadRequest as br:
            try:
                load_job = BQ_CLIENT.load_table_from_file(data, bq_dataset_table, job_config=job_config, timeout=400000)
                load_job.result()
                s3_to_bq_synced_files[s3_file_key] = 1
                XCom.set(key="s3_to_bq_files",
                         value=s3_to_bq_synced_files,
                         task_id="s3_to_bq_sync_files",
                         dag_id=context_global.get('task_instance').dag_id,
                         execution_date=context_global.get('execution_date')
                         )
                newrelic_metrics_client.send_newrelic_gauge_metrics("de-s3-to-bq.sync-status", 1, asset_name, metrics="sync_status")
                logging.info("BQ table updated successfully")
            except Exception as ex:
                newrelic_metrics_client.send_newrelic_gauge_metrics("de-s3-to-bq.sync-status", 0, asset_name, metrics="sync_status")
                for error in load_job.errors:
                    logging.info("ERROR while uploading to bq: {}".format(error["message"]))
    except Exception as ex:
        newrelic_metrics_client.send_newrelic_gauge_metrics("de-s3-to-bq.sync-status", 0, asset_name, metrics="sync_status")
        logging.info('ERROR while loading data in bq')
        logging.exception(ex)

def load_data_to_bq(asset_name, s3_file_key, s3_to_bq_synced_files, bq_dataset_table, data):
    logging.info("Starting the Big Load Operation")
    try:
        bq_table = bq_dataset_table.split('.')[-1]
        bq_dataset = bq_dataset_table.split('.')[0]
        bq_dataset_in_config = bq_dataset if (s3_to_bq_sync_config.get("dataset_mapping").get(bq_dataset) is None) else s3_to_bq_sync_config.get("dataset_mapping").get(bq_dataset)
        schema = get_bq_table_schema(bq_table)
        job_config = bigquery.LoadJobConfig(write_disposition=bigquery.WriteDisposition.WRITE_APPEND, source_format=bigquery.SourceFormat.CSV, skip_leading_rows=1)
        job_config.schema = schema
        load_job = BQ_CLIENT.load_table_from_file(data, "{}.{}".format(bq_dataset_in_config, bq_table), job_config=job_config, timeout=400000)
        logging.info("Starting job with id: {}".format(load_job.job_id))
        try:
            load_job.result()
            s3_to_bq_synced_files[s3_file_key] = 1
            XCom.set(key="s3_to_bq_files",
                     value=s3_to_bq_synced_files,
                     task_id="s3_to_bq_sync_files",
                     dag_id=context_global.get('task_instance').dag_id,
                     execution_date=context_global.get('execution_date')
                     )
            logging.info("BQ table updated successfully")
        except BadRequest as br:
            try:
                load_job = BQ_CLIENT.load_table_from_file(data, bq_dataset_table, job_config=job_config, timeout=400000)
                load_job.result()
                s3_to_bq_synced_files[s3_file_key] = 1
                XCom.set(key="s3_to_bq_files",
                         value=s3_to_bq_synced_files,
                         task_id="s3_to_bq_sync_files",
                         dag_id=context_global.get('task_instance').dag_id,
                         execution_date=context_global.get('execution_date')
                         )
                logging.info("BQ table updated successfully")
            except Exception as ex:
                for error in load_job.errors:
                    logging.info("ERROR while uploading to bq: {}".format(error["message"]))
    except Exception as ex:
        logging.info('ERROR while loading data in bq')
        logging.exception(ex)


def get_data_from_s3(bucket, s3_object_path, dt, asset_name, update_path=True):
    s3_client = boto3.client('s3', aws_access_key_id=aws_credentials["access_key"],
                             aws_secret_access_key=aws_credentials["secret_key"], region_name=s3_to_bq_sync_config["s3_bucket_region"])
    prefix = s3_object_path
    if update_path:
        prefix = s3_object_path + str(dt)
    result = s3_client.list_objects(Bucket=bucket, Prefix=prefix)
    logging.info(f"result of s3 path:{prefix}")
    if result.get('Contents') is not None:
        for s3_file_object in result.get('Contents'):
            file_format = str(s3_file_object.get('Key')).split('.')[-1]
            logging.info(f"file format:{file_format}")
            if file_format == "csv" or file_format == "json":
                logging.info(f"File Prefix :{s3_file_object.get('Key')}")
                data = s3_client.get_object(Bucket=bucket, Key=s3_file_object.get('Key'))
                contents = data['Body']
                bq_dataset_table = config_data[asset_name]["bq_dataset_table"]
                s3_to_bq_synced_files = XCom.get_one(key="s3_to_bq_files", task_id="s3_to_bq_sync_files",
                                                     execution_date=context_global.get('execution_date'))
                if s3_to_bq_synced_files is None:
                    s3_to_bq_synced_files = {}
                is_synced = s3_to_bq_synced_files.get(s3_file_object.get('Key'))
                if ((is_synced is None) or (is_synced == 0)) and file_format == "csv":
                    load_data_to_bq(asset_name, s3_file_object.get('Key'), s3_to_bq_synced_files, bq_dataset_table, contents)
                elif ((is_synced is None) or (is_synced == 0)) and file_format == "json":
                    load_data_to_bq_json(asset_name, s3_file_object.get('Key'), s3_to_bq_synced_files, bq_dataset_table, contents)
                else:
                    logging.info("this file is already synced to bq")
    else:
        logging.error("No files found in the bucket {} and path {}".format(bucket, prefix))


def push_s3_data_to_bq(bq_table):
    offset = config_data[bq_table]["offset"]
    s3_bucket = s3_to_bq_sync_config["s3_bucket"]
    s3_object_path = config_data[bq_table]["file_path"]
    date = datetime.now(tz=time_zone) - timedelta(offset)
    dt = date.strftime("%Y-%m-%d")
    update_path = True
    if "update_path" in config_data[bq_table]:
        update_path = config_data[bq_table]["update_path"]
    try:
        get_data_from_s3(s3_bucket, s3_object_path, dt, bq_table, update_path)
    except Exception as ex:
        logging.info("Exception occured in {0}".format(bq_table))
        logging.exception(ex)
        failure_tables.append(bq_table)


def main(event=None, **context):
    global context_global
    context_global = context
    for table in event['bq_tables_populate']:
        push_s3_data_to_bq(table)

    if len(failure_tables) > 0:
        logging.info("There is failure in load of these tables : {0}".format(str(set(failure_tables))))
        raise Exception("There is failure in load of some table, Please check the full log ")