{"user_portfolio": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "global_stock_total_dividend", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "global_stock_total_dividend_idr", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "cashDiffValue", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "idrAssetsValue", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "usdAssetsValueInIdr", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "usdAssetsValueInUsd", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "realisedGainValue", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "unrealisedGainValue", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "portfolioValue", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "portfolioAUM", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "totalCost", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}], "global_stock_quote_prices": [{"mode": "NULLABLE", "name": "global_stock_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "stock_symbol", "type": "STRING"}, {"mode": "NULLABLE", "name": "source", "type": "STRING"}, {"mode": "NULLABLE", "name": "effective_spread", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_spread", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "buy_back_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "sell_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "mid_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_event_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "is_market_open", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "is_transaction_allowed_by_time", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "event_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_pre_market_open", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "stock_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "stock_status", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_event_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_global_stock_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "raw_price_details_event_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "raw_price_details_symbol", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_ask_exchange_code", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_ask_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_details_ask_size", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_details_bid_exchange_code", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_bid_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_details_bid_size", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_details_source", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_tape", "type": "STRING"}, {"mode": "NULLABLE", "name": "id", "type": "INTEGER"}], "global_stock_trade_prices": [{"mode": "NULLABLE", "name": "global_stock_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "stock_symbol", "type": "STRING"}, {"mode": "NULLABLE", "name": "source", "type": "STRING"}, {"mode": "NULLABLE", "name": "effective_spread", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_spread", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "buy_back_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "sell_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "mid_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_event_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "is_market_open", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "is_transaction_allowed_by_time", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "event_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_pre_market_open", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "stock_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "stock_status", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_event_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_global_stock_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "raw_price_details_event_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "raw_price_details_symbol", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_trade_exchange_code", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_trade_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_details_trade_size", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "raw_price_details_source", "type": "STRING"}, {"mode": "NULLABLE", "name": "raw_price_details_tape", "type": "STRING"}, {"mode": "NULLABLE", "name": "id", "type": "INTEGER"}], "crypto_currency_master_prices": [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "crypto_currency_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "buy_back_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "last_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "mid_price_by_order_book", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "mid_price_by_order_book_in_usdt", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "sell_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "spread", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "source", "type": "STRING"}, {"mode": "NULLABLE", "name": "usdt_mid_price_by_order_book", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}], "crypto_currency_partner_prices": [{"mode": "NULLABLE", "name": "master_price_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "crypto_currency_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "effective_spread", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "buy_back_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "sell_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "mid_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "mid_price_by_order_book", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usdt_effective_spread", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usdt_mid_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usdt_mid_price_by_order_book", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "id", "type": "INTEGER"}], "agg_aum_user_daily": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "product_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "quantity", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "product_mid_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "product_buy_back_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "product_sell_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "currency", "type": "STRING"}, {"mode": "NULLABLE", "name": "asset_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "asset_subtype", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_pocket", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "client_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_pocket_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "recurring_transaction_id", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_recurring", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "first_created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "last_updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "options_contract_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "wallet_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "cash_rewards_cashback", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usd_to_idr_mid_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usd_to_idr_buyback_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usd_to_idr_sell_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created_at", "type": "DATE"}, {"mode": "NULLABLE", "name": "updated_at", "type": "DATE"}], "monthly_profit_and_loss": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "start_time_pnl_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "start_time_pnl", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "end_time_pnl_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "end_time_pnl", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "cashin", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "forex_cashin", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_crypto_receive", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "pnl_diff_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "pnl_diff", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "granularity", "type": "STRING"}, {"mode": "NULLABLE", "name": "end_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "start_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated_at", "type": "TIMESTAMP"}], "gtv_user_daily": [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "product_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "quantity", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "currency", "type": "STRING"}, {"mode": "NULLABLE", "name": "asset_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "asset_subtype", "type": "STRING"}, {"mode": "NULLABLE", "name": "total_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "execute_date", "type": "DATE"}, {"mode": "NULLABLE", "name": "total_value_usd", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "transaction_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_pocket", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "client_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_pocket_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "recurring_transaction_id", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_recurring", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "options_contract_id", "type": "INTEGER"}], "leverage_wallet_accounts_snapshot": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "client_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "balance", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "locked_margin", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "trading_margin", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_overnight_fee", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "eligibility_status", "type": "STRING"}, {"mode": "NULLABLE", "name": "first_order_date", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "margin_call_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_notification_sent", "type": "STRING"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "currency_to_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "equity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "processing_time", "type": "TIMESTAMP"}], "bappebti_wallet_snapshot": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "cash_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "voucher_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "cashback", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "free_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "client_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "cash_reward", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "first_order_date", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "blocked_cash_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "blocked_voucher_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "blocked_free_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "snapshot_time", "type": "TIMESTAMP"}], "gold_day_return": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "gold_balance_frozen", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "client_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "first_order_date", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "total_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "NUMERIC"}], "crypto_currency_day_return": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "crypto_currency_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "reward_balance", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "locked_balance", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_reward_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "NUMERIC"}], "forex_day_return": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "forex_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "NUMERIC"}], "fund_day_return": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "fund_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "currency", "type": "STRING"}, {"mode": "NULLABLE", "name": "weighted_cost_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_value_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain_idr", "type": "NUMERIC"}], "stock_index_day_return": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "stock_index_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "usd_to_idr_quantity_average", "type": "STRING"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "currency_to_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain_idr", "type": "NUMERIC"}], "global_stock_day_return": [{"mode": "NULLABLE", "name": "global_stock_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_dividend", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_dividend_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_trading_margin_used", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "free_credit", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "free_credit_in_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usd_to_idr_quantity_average", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "pending_reward_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_reward_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "reward_balance", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "is_cfd_leverage", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "total_value", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "currency_to_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_reward_value", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}], "indo_stock_day_return": [{"mode": "NULLABLE", "name": "id", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "stock_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "dividend", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "stock_code", "type": "STRING"}, {"mode": "NULLABLE", "name": "unit_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_value", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}], "usd_yield_mtd": [{"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "forex_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "forex_yield_joining_date", "type": "STRING"}, {"mode": "NULLABLE", "name": "is_forex_yield_active", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "gss_kyc_verfied", "type": "STRING"}, {"mode": "NULLABLE", "name": "kyc_verified_date", "type": "STRING"}, {"mode": "NULLABLE", "name": "tag_name", "type": "STRING"}, {"mode": "NULLABLE", "name": "pluang_plus_flag", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "sum_of_account_balance_of_pluang_plus_user", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "sum_of_account_balance_of_regular_user", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "yield_days_of_pluang_plus_user", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "yield_days_of_regular_user", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "average_balance_of_regular_user_for_yield_consider", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "average_balance_of_pluang_plus_user_for_yield_consider", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "average_wallet_balance", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "yield_quantity", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "yield_percentage", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "execution_date", "type": "DATE"}, {"mode": "NULLABLE", "name": "tax_percentage", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "tax_amount", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "last_month_yield_quantity", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "last_month_tax_percentage", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "last_month_tax_amount", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "usd_to_idr", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "disbursement_flag", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "update_last_month_yield_flag", "type": "BOOLEAN"}], "user_tag_mappings": [{"mode": "NULLABLE", "name": "id", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "tag_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "user_id", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "tag_pool_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "tag_name", "type": "STRING"}, {"mode": "NULLABLE", "name": "actor_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "actor_role", "type": "STRING"}, {"mode": "NULLABLE", "name": "actor_email", "type": "STRING"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "snapshot_time", "type": "TIMESTAMP"}], "daily_profit_and_loss": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "start_time_pnl_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "start_time_pnl", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "end_time_pnl_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "end_time_pnl", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "cashin", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "forex_cashin", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "total_crypto_receive", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "pnl_diff_cash", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "pnl_diff", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "granularity", "type": "STRING"}, {"mode": "NULLABLE", "name": "end_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "start_time", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated_at", "type": "TIMESTAMP"}], "invested_amount": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_topup", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_cashout", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_gold_send", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_gold_receive", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_gold_withdrawal", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_crypto_receive", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_crypto_send", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_forex_topups_idr", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_forex_cashouts_idr", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "realised_gain_value", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "invested_value", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}], "frequency_based_recurring_orders_snapshot": [{"name": "accountId", "type": "INTEGER", "mode": "NULLABLE"}, {"name": "amount", "type": "NUMERIC", "mode": "NULLABLE"}, {"name": "amountIdr", "type": "BIGNUMERIC", "mode": "NULLABLE"}, {"name": "assetId", "type": "INTEGER", "mode": "NULLABLE"}, {"name": "assetType", "type": "STRING", "mode": "NULLABLE"}, {"name": "clientId", "type": "INTEGER", "mode": "NULLABLE"}, {"name": "consecutiveFailureCount", "type": "INTEGER", "mode": "NULLABLE"}, {"name": "created", "type": "TIMESTAMP", "mode": "NULLABLE"}, {"name": "currency", "type": "STRING", "mode": "NULLABLE"}, {"name": "currentPrice", "type": "NUMERIC", "mode": "NULLABLE"}, {"name": "duration", "type": "STRING", "mode": "NULLABLE"}, {"name": "endDate", "type": "TIMESTAMP", "mode": "NULLABLE"}, {"name": "frequency", "type": "STRING", "mode": "NULLABLE"}, {"name": "id", "type": "STRING", "mode": "NULLABLE"}, {"name": "nextOrderDate", "type": "TIMESTAMP", "mode": "NULLABLE"}, {"name": "partnerId", "type": "INTEGER", "mode": "NULLABLE"}, {"name": "priceRange", "type": "STRING", "mode": "NULLABLE"}, {"name": "startDate", "type": "TIMESTAMP", "mode": "NULLABLE"}, {"name": "status", "type": "STRING", "mode": "NULLABLE"}, {"name": "symbol", "type": "STRING", "mode": "NULLABLE"}, {"name": "totalInvestedAmount", "type": "BIGNUMERIC", "mode": "NULLABLE"}, {"name": "totalQuantity", "type": "BIGNUMERIC", "mode": "NULLABLE"}, {"name": "updated", "type": "TIMESTAMP", "mode": "NULLABLE"}, {"name": "userId", "type": "INTEGER", "mode": "NULLABLE"}, {"name": "version", "type": "NUMERIC", "mode": "NULLABLE"}, {"name": "snapshotTime", "type": "TIMESTAMP", "mode": "NULLABLE"}], "invested_amount_eod": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_topup", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_cashout", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_gold_send", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_gold_receive", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_gold_withdrawal", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_crypto_receive", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_crypto_send", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_forex_topups_idr", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "total_forex_cashouts_idr", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "realised_gain_value", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "invested_value", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}], "crypto_currency_cashback_niv": [{"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_crypto_buy", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_crypto_sell", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_crypto_deposit", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_crypto_withdrawal", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_crypto_internal_deposit", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_topup", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_cashout", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "net_crypto_niv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "net_cash_niv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "todays_eligible_niv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "base_eligible_niv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_eligible_niv_after_promotion_end", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "num_of_days", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "avg_eligible_niv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "eligible_niv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "cashback", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "state", "type": "STRING"}, {"mode": "NULLABLE", "name": "created_at", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated_at", "type": "TIMESTAMP"}], "crypto_currency_cashback_transactions": [{"mode": "NULLABLE", "name": "id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "crypto_currency_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "quantity", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unit_price", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "asset_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "transaction_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "transfer_network", "type": "STRING"}, {"mode": "NULLABLE", "name": "total_value", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "processing_date", "type": "DATE"}], "crypto_futures_price": [{"mode": "NULLABLE", "name": "crypto_future_pair_symbol", "type": "STRING"}, {"mode": "NULLABLE", "name": "crypto_futures_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "last_updated_at", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "last_traded_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "prev_price24h", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "volume24h", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "funding_rate", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "predicted_funding_rate", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "low24h", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "high24h", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "open_interest", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "mark_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "price_precision", "type": "INTEGER"}], "crypto_futures_day_return": [{"mode": "NULLABLE", "name": "crypto_future_instrument_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "source", "type": "STRING"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "crypto_future_user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_quantity", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "open_order_buy_quantity", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "open_order_sell_quantity", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "liquidation_price", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "leverage", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "current_position_realised_pnl", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unit_price", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "total_value", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "currency_to_idr", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "total_value_idr", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "unrealised_gain_idr", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "weighted_cost", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "realised_gain", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}], "user_transactions": [{"name": "asset_id", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "asset_type", "mode": "NULLABLE", "type": "STRING", "description": "", "fields": []}, {"name": "account_id", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "user_id", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "transaction_id", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "asset_sub_type", "mode": "NULLABLE", "type": "STRING", "description": "", "fields": []}, {"name": "created", "mode": "NULLABLE", "type": "TIMESTAMP", "description": "", "fields": []}, {"name": "updated", "mode": "NULLABLE", "type": "TIMESTAMP", "description": "", "fields": []}, {"name": "transaction_time", "mode": "NULLABLE", "type": "TIMESTAMP", "description": "", "fields": []}, {"name": "transaction_type", "mode": "NULLABLE", "type": "STRING", "description": "", "fields": []}, {"name": "leverage", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "status", "mode": "NULLABLE", "type": "STRING", "description": "", "fields": []}, {"name": "fees", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "executed_total_price", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "executed_quantity", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "executed_unit_price", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "currency_to_idr", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "current_unit_price", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "current_currency_to_idr", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "forex_price", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "usdt_price", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "remaining_quantity", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "forex_accounts", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "leverage_wallet_accounts", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "crypto_margin_wallets", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "bappebti_wallets", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "position", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "prev_position", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "position_time_in_sec", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "position_x_time", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "position_open_time", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "realized_pnl", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "unrealized_pnl", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "execution_time", "mode": "NULLABLE", "type": "TIMESTAMP", "description": "", "fields": []}, {"name": "trading_competition_start_time", "mode": "NULLABLE", "type": "TIMESTAMP", "description": "", "fields": []}, {"name": "row_number", "mode": "NULLABLE", "type": "INTEGER", "description": "", "fields": []}, {"name": "ignore_for_gtv", "mode": "NULLABLE", "type": "BOOLEAN", "description": "", "fields": []}, {"name": "gtv_multiplier", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "updated_executed_quantity", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}, {"name": "updated_executed_unit_price", "mode": "NULLABLE", "type": "FLOAT", "description": "", "fields": []}], "trading_competition_user_metrics": [{"mode": "NULLABLE", "name": "combined_pnl", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "opted_in_user_count", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "profitable_user_count", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "tier_upgraded_user_count", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "top_performing_assets", "type": "STRING"}], "gss_alpaca_fee_breakup": [{"mode": "NULLABLE", "name": "description", "type": "STRING"}, {"mode": "NULLABLE", "name": "activity_sub_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "net_amount", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "account_id", "type": "STRING"}, {"mode": "NULLABLE", "name": "activity_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "status", "type": "STRING"}, {"mode": "NULLABLE", "name": "date", "type": "DATE"}, {"mode": "NULLABLE", "name": "id", "type": "STRING"}], "options_contract_price_and_stats_alias": [{"mode": "NULLABLE", "name": "oci", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "gsi", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "p", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "lcp", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "pc", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "pcp", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "t", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "oi", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "iv", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "g", "type": "STRING"}, {"mode": "NULLABLE", "name": "v", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "ls", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "u", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "as", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "ds", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "rs", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "load_date", "type": "TIMESTAMP"}], "options_contract_quote_prices_alias": [{"mode": "NULLABLE", "name": "av", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "bp", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "bv", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "gsi", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "mp", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "oci", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "sp", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "t", "type": "BIGNUMERIC"}, {"mode": "NULLABLE", "name": "u", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "load_date", "type": "TIMESTAMP"}], "options_contract_price_subscription_details": [{"mode": "NULLABLE", "name": "ocid", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "et", "type": "STRING"}, {"mode": "NULLABLE", "name": "cat", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "gsid", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "pid", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "ss", "type": "STRING"}, {"mode": "NULLABLE", "name": "uat", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "load_date", "type": "TIMESTAMP"}]}