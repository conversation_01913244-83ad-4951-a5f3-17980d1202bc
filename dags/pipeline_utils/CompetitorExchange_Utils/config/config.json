{"DEV": {"project_id": "emasdigi-dev", "dataset": "cmc_competitor_exchange", "s3_bucket": "cmc-competitor-exchange", "bq_tables": {"raw_exchange_map": "Raw-Exchange-Map", "exchange_map": "exchange_map", "exchange_map_schema": [["timestamp", "TIMESTAMP", "NULLABLE"], ["id", "INTEGER", "NULLABLE"], ["slug", "STRING", "NULLABLE"], ["name", "STRING", "NULLABLE"], ["num_coins", "INTEGER", "NULLABLE"], ["quote", "STRING", "NULLABLE"]], "raw_exchange_marketpairs_latest": "Raw-Exchange-Marketpairs-Latest", "exchange_marketpairs_latest": "exchange_market_pairs_latest", "exchange_marketpairs_latest_schema": [["timestamp", "TIMESTAMP", "NULLABLE"], ["id", "INTEGER", "NULLABLE"], ["name_market_pair", "STRING", "NULLABLE"], ["slug", "STRING", "NULLABLE"], ["market_pair", "STRING", "NULLABLE"], ["market_pair_base", "STRING", "NULLABLE"], ["market_pair_quote", "STRING", "NULLABLE"], ["quote", "STRING", "NULLABLE"]]}}, "STAGE": {"project_id": "emasdigi-dev", "dataset": "cmc_competitor_exchange", "s3_bucket": "cmc-competitor-exchange", "bq_tables": {"raw_exchange_map": "Raw-Exchange-Map", "exchange_map": "exchange_map", "exchange_map_schema": [["timestamp", "TIMESTAMP", "NULLABLE"], ["id", "INTEGER", "NULLABLE"], ["slug", "STRING", "NULLABLE"], ["name", "STRING", "NULLABLE"], ["num_coins", "INTEGER", "NULLABLE"], ["quote", "STRING", "NULLABLE"]], "raw_exchange_marketpairs_latest": "Raw-Exchange-Marketpairs-Latest", "exchange_marketpairs_latest": "exchange_market_pairs_latest", "exchange_marketpairs_latest_schema": [["timestamp", "TIMESTAMP", "NULLABLE"], ["id", "INTEGER", "NULLABLE"], ["name_market_pair", "STRING", "NULLABLE"], ["slug", "STRING", "NULLABLE"], ["market_pair", "STRING", "NULLABLE"], ["market_pair_base", "STRING", "NULLABLE"], ["market_pair_quote", "STRING", "NULLABLE"], ["quote", "STRING", "NULLABLE"]]}}, "PROD": {"project_id": "bem---beli-emas-murni", "dataset": "cmc_competitor_exchange", "s3_bucket": "cmc-competitor-exchange-prod", "bq_tables": {"raw_exchange_map": "Raw-Exchange-Map", "exchange_map": "exchange_map", "exchange_map_schema": [["timestamp", "TIMESTAMP", "NULLABLE"], ["id", "INTEGER", "NULLABLE"], ["slug", "STRING", "NULLABLE"], ["name", "STRING", "NULLABLE"], ["num_coins", "INTEGER", "NULLABLE"], ["quote", "STRING", "NULLABLE"]], "raw_exchange_marketpairs_latest": "Raw-Exchange-Marketpairs-Latest", "exchange_marketpairs_latest": "exchange_market_pairs_latest", "exchange_marketpairs_latest_schema": [["timestamp", "TIMESTAMP", "NULLABLE"], ["id", "INTEGER", "NULLABLE"], ["name_market_pair", "STRING", "NULLABLE"], ["slug", "STRING", "NULLABLE"], ["market_pair", "STRING", "NULLABLE"], ["market_pair_base", "STRING", "NULLABLE"], ["market_pair_quote", "STRING", "NULLABLE"], ["quote", "STRING", "NULLABLE"]]}}}