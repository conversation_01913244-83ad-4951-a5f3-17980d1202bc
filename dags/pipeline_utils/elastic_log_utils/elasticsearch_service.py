from elasticsearch import Elasticsearch
from datetime import datetime, timedelta
import logging

class ElasticsearchService:
    def __init__(self,config) -> None:
        self.config = config
        self.es_client = self.init_es_client(config)
        self.repository = config["es_repository"]
    
    def init_es_client(self,config):
        logging.info("Initializing Elasticsearch client")
        if config["es_user"] and config["es_password"]:
            return Elasticsearch([config["es_host"]],http_auth=(config["es_user"], config["es_password"]))
        return Elasticsearch([config["es_host"]],verify_certs=False)


    def create_snapshot(self, index):
        snapshot_settings = {
            "indices": index,
            "ignore_unavailable": True,
            "include_global_state": False
        }
        logging.info("Creating snapshot: {} in repository: {}".format(index, self.repository))
        if self.es_client.indices.exists(index=index):
            response = self.es_client.snapshot.create(repository=self.repository, snapshot=index, body=snapshot_settings)
            logging.info("Snapshot creation response: {}".format(response))
        else:
            logging.warn("Index: {} does not exist".format(index))
        

    def get_snapshot_status(self, snapshot):
        # Check snapshot status
        snapshot_status = self.es_client.snapshot.status(repository=self.repository, snapshot=snapshot)
        logging.info("Snapshot status: {}".format(snapshot_status))
        return snapshot_status


    def delete_index(self,index):
        # Delete snapshot
        logging.info("Deleting index: {} ".format(index))
        if self.es_client.indices.exists(index=index):
            response = self.es_client.indices.delete(index=index)
            logging.info("Index deletion response: {}".format(response))
        else:
            logging.warn("Index: {} does not exist".format(index))
        

    def check_snapshot_status(self, snapshot):
        snapshot_status = self.get_snapshot_status(self.repository, snapshot)
        if snapshot_status["snapshots"][0]["state"] == "SUCCESS":
            logging.info("Snapshot: {} is successful".format(snapshot))
            return True
        else:
            logging.info("Snapshot: {} is not successful".format(snapshot))
            return False
    
        
    def execute_index_snapshots(self):
        offset = 1
        if self.config["offset"]:
            offset = self.config["offset"]
        yesterday = datetime.now() - timedelta(offset)
        for index in self.config["indices"]:
            index_to_snapshot = "{}-{}".format(index,yesterday.strftime("%Y.%m.%d"))
            self.create_snapshot(index_to_snapshot)            
        return
    
    
    def execute_index_deletes(self):
        for index in self.config["indices"]:
                delete_date = datetime.now() - timedelta(self.config["delete_offset"])
                index = "{}-{}".format(index,delete_date.strftime("%Y.%m.%d"))
                self.delete_index(index)
        return
