# import logging as root_logging
import sys
# import json_logging
from google.cloud import bigquery
from google.oauth2 import service_account
# from pipeline_utils.json_logger import CustomJSONLog


class BigQuery(object):
    def __init__(self, service_account_file: dict):
        # json_logging.init_non_web(custom_formatter=CustomJSONLog, enable_json=True)
        # self.logger = root_logging.getLogger("Big-Query")
        # self.logger.setLevel(root_logging.DEBUG)
        # self.logger.addHandler(root_logging.StreamHandler(sys.stdout))
        assert isinstance(service_account_file, dict), ValueError("Invalid Service Account File")
        google_credentials = service_account.Credentials.from_service_account_info(service_account_file)
        self.BQ_CLIENT = bigquery.Client(credentials=google_credentials)
        print("Big-Query Connection Created!")

    def insert_using_dataframe(self, dataframe, schema, dataset_path):
        try:
            # Load BQ Job Config
            job_config = bigquery.LoadJobConfig()
            job_config.schema = schema
            job_config.write_disposition = "WRITE_APPEND"
            job_config.source_format = bigquery.SourceFormat.CSV
            job_config.ignore_unknown_values = True

            # load using json
            load_job = self.BQ_CLIENT.load_table_from_dataframe(
                dataframe, dataset_path, job_config=job_config
            )
            print(f"BQ job id: {load_job.job_id}")
            load_job.result()
            print(f"Big Query Job finished!")
            print(f"Data Loaded to - {dataset_path}")
        except Exception as ex:
            print(f"{BigQuery.insert_using_dataframe.__name__}: {ex}")
            raise Exception(ex)

    def insert_using_json(self, json_object, schema, dataset_path):
        try:
            job_config = bigquery.LoadJobConfig(write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
                                                source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON,
                                                schema=schema,
                                                ignore_unknown_values=True)

            load_job = self.BQ_CLIENT.load_table_from_json(json_object,
                                                           dataset_path,
                                                           job_config=job_config)

            print(f"BQ job id: {load_job.job_id}")

            try:
                load_job.result()
                print(f"Big Query Job finished!")
                print(f"Data Loaded to - {dataset_path}")
            except Exception:
                for error in load_job.errors:
                    print(f"{BigQuery.insert_using_json.__name__} Error: {error.get('message')}")
        except Exception as ex:
            print(f"{BigQuery.insert_using_json.__name__}: {ex}")
            raise Exception(ex)

    def run_and_get_content(self, query_str):
        try:
            results = self.BQ_CLIENT.query(query_str)
            print(f"BQ job id: {results.job_id}")
            return results
        except Exception as ex:
            print(f"{BigQuery.delete_rows_acc_date.__name__}: {ex}")
            raise Exception(ex)
