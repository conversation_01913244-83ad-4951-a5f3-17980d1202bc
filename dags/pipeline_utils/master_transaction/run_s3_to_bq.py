from airflow.models import Variable
from airflow.models import TaskInstance
from airflow.utils.dates import days_ago
from airflow.hooks.base_hook import BaseHook
from airflow.models import XCom
from airflow.utils.db import provide_session
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from google.cloud import bigquery
from google.oauth2 import service_account
from google.api_core.exceptions import BadRequest
import json,logging, os, time, math
from datetime import datetime, timedelta
import pytz, sys, pendulum
import boto3

## Variables Init
ENV = Variable.get("ENV") ## DEV / PROD
SERVICE_ACCOUNT_FILE = Variable.get("de_bq_secret", deserialize_json=True)
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
CONFIG = Variable.get("master_transaction_s3_to_bq", deserialize_json=True)


def get_bq_table_schema(bq_table=None):
    schema_data = CONFIG["bq_table_schema"]
    try:
        schema_config = []
        if bq_table not in schema_data:
            return None

        for column in schema_data[bq_table]:
            if "fields" in column:
                fields_list = []
                for field in column["fields"]:
                    fields_list.append(bigquery.SchemaField(field["name"], field["type"], mode=field["mode"]))
                schema_config.append(
                    bigquery.SchemaField(column["name"], column["type"], mode=column["mode"], fields=fields_list))
            else:
                schema_config.append(bigquery.SchemaField(column["name"], column["type"], mode=column["mode"]))
    except Exception as e:
        logging.exception(e)
        raise e
    else:
        return schema_config

#Read the configuration
credentials = service_account.Credentials.from_service_account_info(SERVICE_ACCOUNT_FILE)
time_zone = pytz.timezone(CONFIG['time_zone'])
failure_tables = []


try:
    # Create a new Google BigQuery client using Google Cloud Platform project
    BQ_CLIENT = bigquery.Client(credentials=credentials)
    logging.info("Successfully created the big query connection {}".format(BQ_CLIENT))
except Exception as e:
    logging.exception(e)
    raise e


def load_data_bq(bq_dataset_table=None, data=dict()):
    logging.info("Starting the Big Query Operation")

    try:
        bq_table = bq_dataset_table.split('.')[1]
        schema = get_bq_table_schema(bq_table)
        job_config = bigquery.LoadJobConfig(write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
                                            source_format=bigquery.SourceFormat.CSV,skip_leading_rows=1
                                    )
        job_config.schema = schema
        logging.info(schema)
        load_job = BQ_CLIENT.load_table_from_file(data,
                                                  bq_dataset_table,
                                                  job_config=job_config)

        logging.info("Starting job with id: {}".format(load_job.job_id))
        try:
            load_job.result()
            logging.info(f"BQ table update is sucess:{data}")
        except BadRequest as br:
            for error in load_job.errors:
                logging.info('ERROR while uploading to bq: {}'.format(error['message']))
            return False
    except Exception as ex:
        logging.info('ERROR while bq operation')
        logging.exception(ex)
        return False
    else:
        return True


def get_data_from_s3(s3_bucket,s3_object_path,dt,asset_name):
    AWS_CREDENTIALS = Variable.get("emr_dag_config", deserialize_json=True)
    s3_client = boto3.client('s3', aws_access_key_id=AWS_CREDENTIALS["access_key"],
                               aws_secret_access_key=AWS_CREDENTIALS["secret_key"], region_name=AWS_CREDENTIALS["region"])
    prefix = s3_object_path + str(dt)
    bucket=s3_bucket
    result = s3_client.list_objects(Bucket = bucket, Prefix=prefix )
    logging.info(f"result of s3 path:{prefix}")
    for o in result.get('Contents'):
        file_format = str(o.get('Key')).split('.')[-1]
        logging.info(f"value of file name:{file_format}")
        if file_format =="csv":
            logging.info(f"key value:{o.get('Key')}")
            data = s3_client.get_object(Bucket=bucket, Key=o.get('Key'))
            contents = data['Body']
            logging.info(f"content value is=str({data})")
            bq_dataset_table=CONFIG["bq_dataset_table"]
            load_data_bq(bq_dataset_table,contents)

def push_S3_data_to_bq(bq_table:None):
    s3_object_path = CONFIG["file_path"] + CONFIG["asset"][bq_table]["file_path"]
    zone = pytz.timezone(CONFIG["time_zone"])
    date = datetime.now(tz=zone) - timedelta(CONFIG["offset"])
    dt = date.strftime("%Y-%m-%d")
    try:
        get_data_from_s3(CONFIG["s3_bucket"],s3_object_path,dt,bq_table)
    except Exception as ex:
        logging.info("Exception occured in {0}".format(bq_table))
        logging.exception(ex)
        failure_tables.append(bq_table)


def main(event=None):
    for table in event['bq_tables_populate']:
        push_S3_data_to_bq(table)

    if len(failure_tables) > 0:
        logging.info("There is failure in load of these tables : {0}".format(str(set(failure_tables))))
        raise Exception("There is failure in load of some table, Please check the full log ")