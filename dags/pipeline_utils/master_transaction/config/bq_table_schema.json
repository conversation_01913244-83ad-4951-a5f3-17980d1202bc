{"detail_all_transactions_daily": [{"mode": "NULLABLE", "name": "created", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "updated", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "date_paid_on", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "effective_date", "type": "TIMESTAMP"}, {"mode": "NULLABLE", "name": "user_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "account_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "asset_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "product", "type": "STRING"}, {"mode": "NULLABLE", "name": "product_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "asset_subtype", "type": "STRING"}, {"mode": "NULLABLE", "name": "status", "type": "STRING"}, {"mode": "NULLABLE", "name": "transaction_type", "type": "STRING"}, {"mode": "NULLABLE", "name": "partner_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "client_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "currency", "type": "STRING"}, {"mode": "NULLABLE", "name": "ref_id", "type": "INTEGER"}, {"mode": "NULLABLE", "name": "quantity", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "executed_quantity", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "net_quantity", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "unit_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "executed_unit_price", "type": "FLOAT"}, {"mode": "NULLABLE", "name": "unit_price_usd", "type": "STRING"}, {"mode": "NULLABLE", "name": "total_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "executed_total_price", "type": "NUMERIC"}, {"mode": "NULLABLE", "name": "ref_table", "type": "STRING"}, {"mode": "NULLABLE", "name": "load_date", "type": "TIMESTAMP"}]}