import logging
import boto3
import json
from botocore.exceptions import ClientError
import os
import pytz
from datetime import datetime
from airflow.exceptions import AirflowException


def upload_file_to_s3(bucket, s3_folder_path, data):
    """Upload a file to an S3 bucket
    :param bucket <str>: Bucket to upload to
    :param prefix <str>: Folder name
    :param file_name <json>: file name
    :param data <json>: data to upload
    """
    logging.info(f"Data to upload {len(data)}")
    # Upload the file
    s3_client = boto3.resource('s3', aws_access_key_id=CREDENTIALS[ENV]["aws_access_key_id"],
                               aws_secret_access_key=CREDENTIALS[ENV]["aws_secret_access_key"])
    s3_object = s3_client.Object(bucket, s3_folder_path)
    try:
        logging.info(f"Saving files to path {f'{bucket}/{s3_folder_path}'}")
        response = s3_object.put(Body=data)
        logging.info("Response: {}".format(response))
    except ClientError as e:
        logging.error(f"Upload File To S3 EXCEPTION: {e}")
        raise AirflowException(f"Upload File To S3 EXCEPTION: {e}")


def get_data_from_s3(s3_bucket,s3_object_path,dt,assert_name):
    CREDENTIALS = Variable.get("CREDENTIALS", deserialize_json=True)
    s3_client = boto3.client('s3', aws_access_key_id=CREDENTIALS[ENV]["aws_access_key_id"],
                               aws_secret_access_key=CREDENTIALS[ENV]["aws_secret_access_key"])
    prefix = s3_object_path + str(dt)
    bucket=s3_bucket
    result = s3_client.list_objects(Bucket = bucket, Prefix=prefix )
    logging.info(f"result of s3 path:{prefix}")
    for o in result.get('Contents'):
        file_format = str(o.get('Key')).split('.')[-1]
        logging.info(f"value of file name:{file_format}")
        if file_format =="csv":
            logging.info(f"key value:{o.get('Key')}")
            data = s3_client.get_object(Bucket=bucket, Key=o.get('Key'))
            contents = data['Body']
            logging.info(f"content value is=str({data})")
            bq_dataset_table=CONFIG[ENV][assert_name]["bq_dataset_table"]
            load_data_bq(bq_dataset_table,contents)


def push_S3_data_to_bq(bq_table:None):
    offset = CONFIG[ENV][bq_table]["offset"]
    s3_bucket = CONFIG[ENV][bq_table]["s3_bucket"]
    s3_object_path = CONFIG[ENV][bq_table]["file_path"]
    zone = pytz.timezone("Asia/Jakarta")
    date = datetime.now(tz=zone) - timedelta(offset)
    dt = date.strftime("%Y-%m-%d")
    try:
        get_data_from_s3(s3_bucket,s3_object_path,dt,bq_table)
    except Exception as ex:
        logging.info("Exception occured in {0}".format(bq_table))
        logging.exception(ex)
        failure_tables.append(bq_table)
