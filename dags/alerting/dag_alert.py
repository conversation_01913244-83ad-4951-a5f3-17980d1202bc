import sys
from airflow.hooks.base_hook import BaseHook
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.providers.opsgenie.hooks.opsgenie import OpsgenieAlertHook
from datetime import datetime

class DagAlert(object):
    def __init__(self, user_name:str, slack_conn_id:str,opsgenie_conn_id:str):
        self.opsgenie_conn_id = opsgenie_conn_id
        self.slack_conn_id = slack_conn_id
        self.user_name = user_name
        print("Dag Alert Init")

    def slack_alert_failure(self, context):
        """
        Slack Alert Failure
        """
        try:
            slack_webhook_token = BaseHook.get_connection(self.slack_conn_id).password
            task_id = 'slack_alert_failure'
            slack_msg = """
                    :red_circle: Task Failed.
                    *Task*: {task}
                    *Dag*: {dag}
                    *Execution Time*: {exec_date}
                    *Log Url*: {log_url}
                    """.format(
                task=context.get('task_instance').task_id,
                dag=context.get('task_instance').dag_id,
                ti=context.get('task_instance'),
                exec_date=context.get('execution_date'),
                log_url=context.get('task_instance').log_url,
            )
            failed_alert = SlackWebhookOperator(
                task_id=task_id,
                http_conn_id=self.slack_conn_id,
                webhook_token=slack_webhook_token,
                message=slack_msg,
                username=self.user_name)
            print(
                f"{DagAlert.slack_alert_failure.__name__}: task_id: {task_id} | http_conn_id: {self.slack_conn_id} | user_name: {self.user_name}")
            return failed_alert.execute(context=context)
        except Exception as ex:
            print(f"{DagAlert.slack_alert_failure.__name__}: {ex}")
            raise Exception(ex)

    def slack_alert_success(self, context):
        """
            Slack Alert Success
        """
        try:
            slack_webhook_token = BaseHook.get_connection(self.slack_conn_id).password
            task_id = 'slack_alert_success'
            slack_msg = """
                        🟢 Task Completed.
                        *Task*: {task}
                        *Dag*: {dag}
                        *Execution Time*: {exec_date}
                        *Log Url*: {log_url}
                        """.format(
                task=context.get('task_instance').task_id,
                dag=context.get('task_instance').dag_id,
                ti=context.get('task_instance'),
                exec_date=context.get('execution_date'),
                log_url=context.get('task_instance').log_url,
            )
            success_alert = SlackWebhookOperator(
                task_id=task_id,
                http_conn_id=self.slack_conn_id,
                webhook_token=slack_webhook_token,
                message=slack_msg,
                username=self.user_name)
            print(
                f"{DagAlert.slack_alert_success.__name__}: task_id: {task_id} | http_conn_id: {self.slack_conn_id} | user_name: {self.user_name}")
            return success_alert.execute(context=context)
        except Exception as ex:
            print(f"{DagAlert.slack_alert_success.__name__}: {ex}")
            raise Exception(ex)

    def set_slack_alert(self, context, task_id, msg):
        try:
            slack_webhook_token = BaseHook.get_connection(self.slack_conn_id).password
            slack_alert_web_hook = SlackWebhookOperator(
                task_id=task_id,
                http_conn_id=self.slack_conn_id,
                webhook_token=slack_webhook_token,
                message=msg,
                username=self.user_name)
            print(
                f"{DagAlert.set_slack_alert.__name__}: task_id: {task_id} | http_conn_id: {self.slack_conn_id} | user_name: {self.user_name}")
            return slack_alert_web_hook.execute(context=context)
        except Exception as ex:
            print(f"{DagAlert.set_slack_alert.__name__}: {ex}")
            raise Exception(ex)

    def all_channel_alert_failure(self, context, priority: str = "P1", is_opsgenie_alert_enabled: bool = False):
        """
        All Channel - Slack Alert
        """
        try:
            slack_webhook_token = BaseHook.get_connection(self.slack_conn_id).password
            task_id = 'slack_alert_failure'
            slack_msg = """
                    :red_circle: Task Failed.
                    *Task*: {task}
                    *Dag*: {dag}
                    *Execution Time*: {exec_date}
                    *Log Url*: {log_url}
                    """.format(
                task=context.get('task_instance').task_id,
                dag=context.get('task_instance').dag_id,
                exec_date=context.get('execution_date'),
                ti=context.get('task_instance'),
                log_url=context.get('task_instance').log_url,
            )
            failed_alert = SlackWebhookOperator(
                task_id=task_id,
                http_conn_id=self.slack_conn_id,
                webhook_token=slack_webhook_token,
                message=slack_msg,
                username=self.user_name)
            print(
                f"{DagAlert.all_channel_alert_failure.__name__}: task_id: {task_id} | http_conn_id: {self.slack_conn_id} | user_name: {self.user_name}")
            failed_alert.execute(context=context)
        except Exception as ex:
            print(f"{DagAlert.all_channel_alert_failure.__name__}: {ex}")
        """
            All Channel - Opsgenie Alert
        """
        print("is opsgenie alert enabled: {}, priority: {}".format(is_opsgenie_alert_enabled, priority))
        if is_opsgenie_alert_enabled:
            print("executing opsgenie")
            hook = OpsgenieAlertHook(self.opsgenie_conn_id)
            task_instance = context.get("task_instance")
            alert_config = {
                "message": "Failure in task {},dag: {}".format(task_instance.task_id,task_instance.dag_id),
                "description": "See more here {}".format(task_instance.log_url),
                "responders": [{"name": "data_engineering_squad", "type": "team"}],
                # "tags": self.tags,
                "priority": priority,
                "alias": "Failure in task {},dag: {}".format(task_instance.task_id,task_instance.dag_id)
            }
            print(alert_config)
            opsgenie_alert_response = hook.create_alert(alert_config)
            print(opsgenie_alert_response)
            return opsgenie_alert_response
        print("all channel alert successfully executed")