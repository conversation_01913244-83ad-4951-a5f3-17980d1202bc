from newrelic_telemetry_sdk import GaugeMetric, SummaryMetric, CountMetric, MetricClient
from airflow.models import Variable


class NewRelic:
    def __init__(self, newrelic_integration_enabled):
        self.env = Variable.get("ENV")
        newrelic_api_key = Variable.get("NEWRELIC_API_KEY")
        self.metric_client = MetricClient(newrelic_api_key)
        self.newrelic_integration_enabled = newrelic_integration_enabled

    def send_newrelic_gauge_metrics(self, key, value, app_name, **kwargs):
        if self.newrelic_integration_enabled == 1:
            tags = {"app_name": app_name, "metrics": "record_count", "enviornment": self.env, "unit": "number"}
            tag_keys = tags.keys()
            for tag_key in tag_keys:
                if kwargs.get(tag_key) is not None:
                    tags[tag_key] = kwargs[tag_key]
            metrics = [
                GaugeMetric(
                    key, value, tags
                )
            ]
            response = self.metric_client.send_batch(metrics)
            if response.raise_for_status() is not None:
                raise Exception("Error in publishing metric to new relic")