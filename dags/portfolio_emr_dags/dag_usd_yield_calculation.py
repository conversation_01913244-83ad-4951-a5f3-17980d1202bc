import os, sys
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.utils.helpers import chain
import pendulum
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJobFlowOperator
from airflow_kubernetes_job_operator.kubernetes_job_operator import KubernetesJobOperator
start_date = (Variable.get("default_args", deserialize_json=True).get("start_date").split(","))
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
from base_dags.base_dag import BASE_DAG
from portfolio_emr_dags.portfolio_emr_utils import *
from helpers.helper import Helper
from alerting.dag_alert import DagAlert
pipeline_utils_dir_path = os.path.join(ROOT_DIR, "dags/pipeline_utils")
sys.path.append(pipeline_utils_dir_path)
from pipeline_utils.s3_to_bq import run_s3_to_bq
env = Variable.get("ENV")
helper = Helper(env=env)
NAME = Variable.get("MWAA_NAMESPACE")


config = Variable.get("dag_usd_yield_emr_config", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
s3_to_bq_tables_to_sync = Variable.get("s3_to_bq_sync_config", deserialize_json=True).get("usd_yield_calculation_dag")
ZONE, DELTA = dateutil.tz.gettz("Asia/Jakarta"), -1
DATE = datetime.now(tz=ZONE) + timedelta(DELTA)
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)

emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])

## Utils Init
portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)


aws_conn_id=emr_config["aws_conn_id"]

## Base DAG INIT
BaseDag = BASE_DAG(
    dag_id=config["dag_id"],
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval="00 04 * * *",
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

# Create EMR Dag
(
    DAG,
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    sensor_Setup_Hadoop_Debugging,
) = BaseDag.Create_EMR_Dag(
    dagrun_timeout=timedelta(minutes=240),
    max_active_runs=1,
    concurrency=config["concurrency"],
    emr_core=config["emr_core"],
    env=env,
    script_bootstrap_action_file=emr_config["script_bootstrap_action_file"],
    region=emr_config["region"],
    log_uri_bucket=emr_config["log_uri_bucket"],
    resource_bucket=emr_config["resource_bucket"],
    emr_key_pair=emr_config["emr_key_pair"],
    master_security_group=emr_config["master_security_group"],
    slave_security_group=emr_config["slave_security_group"],
    service_access_security_group=emr_config["service_access_security_group"],
    subnet_id=emr_config["subnet_id"],
    ec2_instance_type_config=emr_config["ec2_instance_type_config"],
    aws_conn_id=aws_conn_id,
    emr_conn_id=emr_config["emr_conn_id"],
)

globals()[config["dag_id"]] = DAG
chain(
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    sensor_Setup_Hadoop_Debugging
)

kafka_batch_consumer = EmrAddStepsOperator(
    task_id="kafka_batch_consumer",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_kafka_batch_consumer", "kafka-batch-consumer/batch_consumer.py","small", schedule="jkt_07_00"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_kafka_batch_consumer = EmrStepSensor(
    task_id='sensor_kafka_batch_consumer',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('kafka_batch_consumer', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_kafka_batch_consumer", "kafka-batch-consumer/batch_consumer.py","small", schedule="jkt_07_00")) - 1) + "]}}",
    aws_conn_id= aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_user_tag_mappings_snapshot = EmrAddStepsOperator(
    task_id="execute_user_tag_mappings_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_user_tag_mappings_snapshot", "user_profile/snap_user_tag_mappings.py","small",offset="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_user_tag_mappings_snapshot = EmrStepSensor(
    task_id="sensor_execute_user_tag_mappings_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_user_tag_mappings_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_user_tag_mappings_snapshot", "user_profile/snap_user_tag_mappings.py","small",offset="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_gss_kyc_information_snapshot = EmrAddStepsOperator(
    task_id="execute_gss_kyc_information_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_snapshot", "user_profile/snap_gss_kyc_information.py", "small",offset="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_gss_kyc_information_snapshot = EmrStepSensor(
    task_id="sensor_execute_gss_kyc_information_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gss_kyc_information_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_snapshot", "user_profile/snap_gss_kyc_information.py", "small",offset="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_forex_yield_opt_in_details_snapshot = EmrAddStepsOperator(
    task_id="execute_forex_yield_opt_in_details_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_forex_yield_opt_in_details_snapshot", "yield_forex/forex_yield_opt_in_details.py", "small",offset="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_forex_yield_opt_in_details_snapshot = EmrStepSensor(
    task_id="sensor_execute_forex_yield_opt_in_details_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_yield_opt_in_details_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_forex_yield_opt_in_details_snapshot", "yield_forex/forex_yield_opt_in_details.py", "small",offset="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)


execute_forex_yield_calculation = EmrAddStepsOperator(
    task_id='execute_forex_yield_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_forex_yield_calculation", "yield_forex/forex_yield_calculation.py","small"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_forex_yield_calculation = EmrStepSensor(
    task_id='sensor_execute_forex_yield_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_yield_calculation', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_forex_yield_calculation", "yield_forex/forex_yield_calculation.py","small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

run_s3_to_bq = PythonOperator(
    task_id=f"Run-S3-To-BQ",
    python_callable=run_s3_to_bq.main,
    op_args=[{"bq_tables_populate": s3_to_bq_tables_to_sync}],
    execution_timeout=timedelta(minutes=60),
    dag=globals()[config["dag_id"]],
    provide_context=True
)


copy_spark_logs_hdfs_to_s3 = EmrAddStepsOperator(
    task_id="copy_spark_logs_hdfs_to_s3",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE),
    dag=globals()[config["dag_id"]],
)

sensor_copy_spark_logs_hdfs_to_s3 = EmrStepSensor(
    task_id="sensor_copy_spark_logs_hdfs_to_s3",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('copy_spark_logs_hdfs_to_s3', key='return_value')["
            + str(len(portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE)) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

terminate_emr_cluster = EmrTerminateJobFlowOperator(
    task_id="Terminate-EMR-Cluster",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

delete_xcom = PythonOperator(
    task_id="delete_xcom_global_stock_dividend_dag",
    python_callable=portfolio_emr_utils.clean_xcom,
    op_kwargs={'dag_ids': [config['dag_id']]},
    dag=globals()[config["dag_id"]],
)

end = DummyOperator(
    task_id='dag_end_global_stock_dividend',
    dag=globals()[config["dag_id"]]
)

chain(
    sensor_Setup_Hadoop_Debugging,
    kafka_batch_consumer,
    sensor_kafka_batch_consumer,
    execute_user_tag_mappings_snapshot,
    sensor_execute_user_tag_mappings_snapshot,
    execute_gss_kyc_information_snapshot,
    sensor_execute_gss_kyc_information_snapshot,
    execute_forex_yield_opt_in_details_snapshot,
    sensor_execute_forex_yield_opt_in_details_snapshot,
    execute_forex_yield_calculation,
    sensor_execute_forex_yield_calculation,
    copy_spark_logs_hdfs_to_s3,
    sensor_copy_spark_logs_hdfs_to_s3,
    terminate_emr_cluster,
    run_s3_to_bq,
    delete_xcom,
    end
)