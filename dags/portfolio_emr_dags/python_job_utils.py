import logging
import os, sys
import time
import requests
import pysftp
import json
from pathlib import Path
from datetime import datetime, timedelta
from boto3.session import Session
import pandas as pd
from pymongo import MongoClient
import pytz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
from helpers.helper import Helper
from portfolio_emr_dags.emr_dag_config import *


class PythonJobUtils:
    def __init__(
            self,
            env: str,
            aws_access_key: str,
            aws_secret_key: str,
            slack_webhook_url: str,
    ) -> None:
        self.env = env
        self.aws_access_key = aws_access_key
        self.aws_secret_key = aws_secret_key
        self.slack_webhook_url = slack_webhook_url

    def get_s3_resource(self, data_region):
        s3_session = Session(aws_access_key_id=self.aws_access_key, aws_secret_access_key=self.aws_secret_key, region_name=data_region)
        s3_resource = s3_session.resource('s3')
        return s3_resource

    def read_data_from_s3(self, s3_resource, bucket, path):
        try:
            obj = s3_resource.Object(bucket, path)
            prev_df = pd.read_csv(obj.get()["Body"])
            return prev_df
        except:
            return None

    def get_files_in_s3_folder(self, s3_resource, bucket, prefix):
        bucket_obj = s3_resource.Bucket(bucket)
        files_arr = []
        for objects in bucket_obj.objects.filter(Prefix=prefix+"/"):
            files_arr.append(objects.key)
        return files_arr

    def time_delay(self, **kwargs):
        logging.info("waiting for {} seconds for delay".format(kwargs.get("secs")))
        time.sleep(kwargs.get("secs"))

    def user_current_portfolio_alert(self, **kwargs):
        emr_dag_config = kwargs
        s3_resource = self.get_s3_resource(emr_dag_config["region"])
        all_assets = emr_dag_validation_config["portfolio_validation_alerts"]["assets_folders"].keys()
        bucket = emr_dag_config["data_bucket"]
        offset_date = Helper.get_date(emr_dag_validation_config["offset"])
        total_descrepency = 0
        descrepency_files = []
        individual_asset_count_msg = ""
        for asset in all_assets:
            resp_arr = self.get_files_in_s3_folder(s3_resource, bucket, emr_dag_validation_config["portfolio_validation_alerts"]["folder"]+"/"+emr_dag_validation_config["portfolio_validation_alerts"]["assets_folders"][asset]+"/dt="+str(offset_date))
            for fl in resp_arr:
                if fl.find(".csv") != -1:
                    df = self.read_data_from_s3(s3_resource, bucket, fl)
                    cnt = len(df)
                    if cnt > 0:
                        descrepency_files.append("s3://"+bucket+"/"+fl)
                        individual_asset_count_msg = "{}*{}*: {}\n".format(individual_asset_count_msg, asset, cnt)
                    total_descrepency = total_descrepency + len(df)
        if total_descrepency > 0:
            assets_str = None
            for fl in descrepency_files:
                if assets_str is not None:
                    assets_str = assets_str + ", " + fl.split("/")[5]
                else:
                    assets_str = fl.split("/")[5]
            slack_msg = "*@channel*\n*Portfolio validation date*: {offset_date}\n*Collection*: {collection}\n{individual_asset_count_msg}*Total descrepencies*: {total_descrepency}\n*Asset list with descrepencies*: {assets_str}\n*Result data path*: {s3_result_path}".format(
                offset_date=offset_date,
                collection="user_current_portfolio",
                individual_asset_count_msg=individual_asset_count_msg,
                total_descrepency=total_descrepency,
                assets_str=assets_str,
                s3_result_path=emr_dag_config["user_portfolio_validation_folder_url"]
            )
            Helper.slack_msg(slack_msg, self.slack_webhook_url)

    def upload_to_sftp_clevertap_prop(self, **kwargs):
        config_data = kwargs
        zone = pytz.timezone("Asia/Jakarta")
        offset_date = Helper.get_date(config_data["clevertap_sftp"]["offset"])
        current_ts = datetime.now(tz=zone)
        logging.info("offset date is {}".format(offset_date))
        s3_resource = self.get_s3_resource(config_data["clevertap_sftp"]["region_name"])
        files_arr = self.get_files_in_s3_folder(s3_resource, config_data["clevertap_sftp"]["bucket"], "{}/dt={}".format(config_data["clevertap_sftp"]["s3_folder_path"],offset_date))
        for fl in files_arr:
            logging.info("file prefix to upload is {}".format(fl))
            if fl.find(".csv") != -1:
                local_path = f"/usr/local/airflow/clevertap_user_properties"+''.join(e for e in str(current_ts) if e.isalnum())+".csv"
                id_rsa_local_path = config_data["clevertap_sftp"]["private_key_path"]
                logging.info("downloading csv file")
                s3_object = s3_resource.Object(config_data["clevertap_sftp"]["bucket"], fl)
                Path(local_path).touch()
                with open(local_path, 'wb') as file:
                    s3_object.download_fileobj(file)
                logging.info("downloading private key file")
                s3_object = s3_resource.Object(config_data["clevertap_sftp"]["private_key_bucket"], config_data["clevertap_sftp"]["private_key_prefix"])
                Path(id_rsa_local_path).touch()
                with open(id_rsa_local_path, 'wb') as file:
                    s3_object.download_fileobj(file)
                columns = {
                    "identity": {
                        "ctName": "identity",
                        "dataType": "STRING"
                    }
                }

                additional_cols = config_data["clevertap_sftp"]["columns"]
                for key, value in additional_cols.items():
                    columns[key] = {"ctName": key, "dataType": value}
                filename = local_path.split("/")[-1]
                manifest_dict = dict(fileName=filename, type="profile", columns=columns,
                                     clientEmail=config_data["clevertap_sftp"]["email"])
                manifest_path = f"{local_path}.manifest"
                logging.info("generating menifest file")
                with open(manifest_path, "w") as f:
                    json.dump(manifest_dict, f, indent=4)
                cnopts = pysftp.CnOpts()
                cnopts.hostkeys = None
                with pysftp.Connection(config_data["clevertap_sftp"]["sftp_host"], username=config_data["clevertap_sftp"]["account_id"], private_key=config_data["clevertap_sftp"]["private_key_path"], cnopts=cnopts) as sftp:
                    with open(manifest_path, "r") as f:
                        manifest_dict = json.load(f)
                    logging.info("uploading csv file to sftp")
                    sftp.put(local_path, f"{manifest_dict['fileName']}")

                    logging.info(f"File uploaded successfully: {manifest_dict['fileName']}")
                    logging.info("uploading menifest file")
                    sftp.put(manifest_path, f"{manifest_path.split('/')[-1]}")
                    logging.info("Menifest File uploaded successfully")
                logging.info("Delete files")
                try:
                    os.remove(local_path)
                    os.remove(id_rsa_local_path)
                    os.remove(f"{local_path}.manifest")
                except Exception as e:
                    logging.warning("old file doesn't exist")



    def pluang_plus_member_operation(self, **kwargs):
        config_data = kwargs
        plus_config = config_data["pluang_plus"]
        offset_date = Helper.get_date(plus_config["offset"])
        logging.info("offset date is {}".format(offset_date))
        s3_resource = self.get_s3_resource(plus_config["region_name"])
        self.add_or_remove_plus_member(s3_resource, plus_config, offset_date, "ADD", plus_config["add_key_prefix"])
        # self.add_or_remove_plus_member(s3_resource, plus_config, offset_date, "REMOVE", plus_config["remove_key_prefix"])

    def add_or_remove_plus_member(self, s3_resource, plus_config, offset_date, ops_type, key_prefix):
        logging.info("starting {} operation for the date {}".format(ops_type, offset_date))
        files_arr = self.get_files_in_s3_folder(s3_resource, plus_config["bucket"], "{}/dt={}".format(key_prefix, offset_date))
        logging.info("total no of files for post api is {}".format(len(files_arr)-1))
        for fl in files_arr:
            if fl.find(".csv") != -1:
                df = self.read_data_from_s3(s3_resource, plus_config["bucket"], fl)
                cnt = len(df)
                if cnt > 0:
                    logging.info("total no of {} operation is {} in the file {}".format(ops_type, cnt, fl))
                    auth_token = plus_config["auth_token"]
                    headers = {"Authorization": f"Bearer {auth_token}"}
                    data = {
                        "tagId": plus_config["tag_id"],
                        "inputFileUrl": "https://{}.s3.amazonaws.com/{}".format(plus_config["bucket"], fl),
                        "key": fl,
                        "action": ops_type,
                        "actorRole": plus_config["actor_role"],
                        "tagPoolId": plus_config["tag_pool_id"],
                        "tagName": plus_config["tag_name"]
                    }
                    retry_delay = 0
                    while retry_delay < 20:
                        response = requests.post(plus_config["post_url"], json=data, headers=headers)
                        if response.status_code == 200:
                            logging.info("{} operation is successful".format(ops_type))
                            break
                        else:
                            time.sleep(5)
                            retry_delay = retry_delay + 5
                            logging.info("retrying the api call")
                else:
                    logging.info("No user id is found in the file {}".format(fl))

    def check_global_stock_option_price_execution(self, run_time):
        current_time = datetime.now(tz=pytz.timezone("UTC"))
        seconds_to_wait = (run_time - current_time).total_seconds()
        logging.info(f"Wait time in seconds:  {seconds_to_wait}")
        if seconds_to_wait > 0:
            time.sleep(seconds_to_wait)
        return run_time

    def fetch_global_stock_option_price_from_mongo(self, **kwargs):
        logging.info("Starting fetch option price from mongo operation")
        price_config = kwargs
        t_1_date = Helper.get_date(price_config["offset"])
        t_1 = datetime(year=t_1_date.year, month=t_1_date.month, day=t_1_date.day, hour=price_config["price_cutoff_time"]["hour"], minute=price_config["price_cutoff_time"]["minute"], second=price_config["price_cutoff_time"]["second"], microsecond=price_config["price_cutoff_time"]["microsecond"]).replace(tzinfo=pytz.UTC)
        t_2 = (t_1 - timedelta(days=1)).replace(hour=17, minute=0, second=0, microsecond=0)
        logging.info("Price filter t_1 : {} and t_2: {}".format(t_1, t_2))
        run_time = self.check_global_stock_option_price_execution(t_1)
        logging.info("Started fetching option price from mongo")
        filter_condition = {"u": {"$lte": t_1, "$gt": t_2}}
        mongo_uri = price_config["mongo_uri"]
        client = MongoClient('{}?readPreference=secondary'.format(mongo_uri))
        db = client[price_config["db_name"]]
        logging.info("db name is {}".format(price_config["db_name"]))
        df_collection = db[price_config["collection_name"]]
        projection = {
            "_id": 0,
            "oci": 1,
            "gsi": 1,
            "u": 1,
            "p": 1,
            "lcp": 1,
            "t": 1
        }
        df = pd.DataFrame(list(df_collection.find(filter_condition, projection, batch_size=200)))

        # Rename columns back to original names
        rename_mapping = {
            "oci": "optionsContractId",
            "gsi": "globalStockId",
            "u": "updatedAt",
            "p": "price",
            "lcp": "lastDayClosePrice",
            "t": "rawPriceEventTime"
        }

        df.rename(columns=rename_mapping, inplace=True)

        num_of_rows = len(df)
        logging.info("Number of rows fetched from mongo is {}".format(num_of_rows))
        if num_of_rows > 0:
            logging.info("columns fetched are: {}".format(df.columns))
            s3_price_path_suffix = "{}{}/price_{}.json".format(price_config["raw_price_path"], t_1_date, run_time)
            s3_resource = self.get_s3_resource(price_config["bucket_region"])
            s3_resource.Object(price_config["bucket"], s3_price_path_suffix).put(Body=df.to_json(orient="records", lines=True).encode())


