import os, sys
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.utils.helpers import chain
import pendulum
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.operators.python_operator import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.operators.dummy_operator import DummyOperator
start_date = (Variable.get("default_args", deserialize_json=True).get("start_date").split(","))
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
from base_dags.base_dag import BASE_DAG
from portfolio_emr_dags.portfolio_emr_utils import *
from helpers.helper import Helper
from alerting.dag_alert import DagAlert

env = Variable.get("ENV")
helper = Helper(env=env)
config = Variable.get("portfolio_emr_dag_config", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)
root_dag_id = config["dag_id"]
config["dag_id"] = config["mongo_write_dag_id"]
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)

emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
aws_conn_id=emr_config["aws_conn_id"]

portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)

BaseDag = BASE_DAG(
    dag_id=config["dag_id"],
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval="55 16 * * *",
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

DAG = BaseDag.Create_Dag(
    dagrun_timeout=timedelta(minutes=390),
    max_active_runs=1,
    concurrency=config["concurrency"]
)

globals()[config["dag_id"]] = DAG

external_dag_id = config['external_dag_id'][config["dag_id"]]
external_task_id = config['external_task_id'][config["dag_id"]]
dag_start = ExternalTaskSensor(
    external_dag_id=external_dag_id,
    external_task_id=external_task_id,
    allowed_states=["success", "upstream_failed", "failed"],
    dag=globals()[config["dag_id"]],
    task_id="start_mongo_write",
)

execute_mongo_write_indo_stock = EmrAddStepsOperator(
    task_id='execute_mongo_write_indo_stock',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("asset_name", "indo_stock", "asset_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_indo_stock = EmrStepSensor(
    task_id='sensor_execute_mongo_write_indo_stock',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_indo_stock', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("asset_name", "indo_stock", "asset_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_gold = EmrAddStepsOperator(
    task_id='execute_mongo_write_gold',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("asset_name", "gold", "asset_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_gold = EmrStepSensor(
    task_id='sensor_execute_mongo_write_gold',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_gold', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("asset_name", "gold", "asset_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_fund = EmrAddStepsOperator(
    task_id='execute_mongo_write_fund',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("asset_name", "fund", "asset_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_fund = EmrStepSensor(
    task_id='sensor_execute_mongo_write_fund',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_fund', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("asset_name", "fund", "asset_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_forex = EmrAddStepsOperator(
    task_id='execute_mongo_write_forex',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("asset_name", "forex", "asset_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_forex = EmrStepSensor(
    task_id='sensor_execute_mongo_write_forex',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_forex', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("asset_name", "forex", "asset_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_cypto_currency = EmrAddStepsOperator(
    task_id='execute_mongo_write_cypto_currency',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("asset_name", "crypto_currency", "asset_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_cypto_currency = EmrStepSensor(
    task_id='sensor_execute_mongo_write_cypto_currency',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_cypto_currency', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("asset_name", "crypto_currency", "asset_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_crypto_future_mongo_write = EmrAddStepsOperator(
    task_id='execute_crypto_future_mongo_write',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_crypto_future_mongo_write", "crypto_futures/snap_crypto_preps_accounts.py", "small", job_type="mongo_write"),
    params={"config": "config.json"},
    dag=DAG
)

sensor_execute_crypto_future_mongo_write = EmrStepSensor(
    task_id='sensor_execute_crypto_future_mongo_write',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_future_mongo_write', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_crypto_future_mongo_write", "crypto_futures/snap_crypto_preps_accounts.py", "small", job_type="mongo_write")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG
)

execute_mongo_write_pocket_asset = EmrAddStepsOperator(
    task_id='execute_mongo_write_pocket_asset',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("asset_name", "pocket_asset", "asset_mongo_write_in_pocket.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_pocket_asset = EmrStepSensor(
    task_id='sensor_execute_mongo_write_pocket_asset',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_pocket_asset', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("asset_name", "pocket_asset", "asset_mongo_write_in_pocket.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_pocket_write = EmrAddStepsOperator(
    task_id='execute_mongo_pocket_write',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_mongo_pocket_write", "pocket_mongo_write.py", "small"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_pocket_write = EmrStepSensor(
    task_id='sensor_execute_mongo_pocket_write',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_pocket_write', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_spark_job("execute_mongo_pocket_write", "pocket_mongo_write.py", "small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)


execute_mongo_write_portfolio = EmrAddStepsOperator(
    task_id='execute_mongo_write_portfolio',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_mongo_write_portfolio", "portfolio/snap_portfolio_mongo_write.py", "medium"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_portfolio = EmrStepSensor(
    task_id='sensor_execute_mongo_write_portfolio',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_portfolio', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_spark_job("execute_mongo_write_portfolio", "portfolio/snap_portfolio_mongo_write.py", "medium")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_pnl_daily = EmrAddStepsOperator(
    task_id='execute_mongo_write_pnl_daily',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("pnl_name", "daily_profit_and_loss", "pnl_calculation/pnl_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_pnl_daily = EmrStepSensor(
    task_id='sensor_execute_mongo_write_pnl_daily',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_pnl_daily', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("pnl_name", "daily_profit_and_loss", "pnl_calculation/pnl_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_pnl_weekly = EmrAddStepsOperator(
    task_id='execute_mongo_write_pnl_weekly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("pnl_name", "weekly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_pnl_weekly = EmrStepSensor(
    task_id='sensor_execute_mongo_write_pnl_weekly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_pnl_weekly', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("pnl_name", "weekly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_pnl_monthly = EmrAddStepsOperator(
    task_id='execute_mongo_write_pnl_monthly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("pnl_name", "monthly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_pnl_monthly = EmrStepSensor(
    task_id='sensor_execute_mongo_write_pnl_monthly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_pnl_monthly', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("pnl_name", "monthly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_pnl_quarterly = EmrAddStepsOperator(
    task_id='execute_mongo_write_pnl_quarterly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("pnl_name", "quarterly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_pnl_quarterly = EmrStepSensor(
    task_id='sensor_execute_mongo_write_pnl_quarterly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_pnl_quarterly', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("pnl_name", "quarterly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_mongo_write_pnl_yearly = EmrAddStepsOperator(
    task_id='execute_mongo_write_pnl_yearly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_mongo_write_job("pnl_name", "yearly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_mongo_write_pnl_yearly = EmrStepSensor(
    task_id='sensor_execute_mongo_write_pnl_yearly',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_mongo_write_pnl_yearly', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_mongo_write_job("pnl_name", "yearly_profit_and_loss", "pnl_calculation/pnl_mongo_write.py")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

end = DummyOperator(task_id="dag_end_mongo_write", dag=globals()[config["dag_id"]])

mongo_write_group_first = [
    execute_mongo_write_indo_stock,
    execute_mongo_write_forex
]

mongo_write_group_first_sensor = [
    sensor_execute_mongo_write_indo_stock,
    sensor_execute_mongo_write_forex
]

mongo_write_group_second = [
    execute_mongo_write_fund,
    execute_crypto_future_mongo_write
]

mongo_write_group_second_sensor = [
    sensor_execute_mongo_write_fund,
    sensor_execute_crypto_future_mongo_write
]

mongo_write_group_third = [
    execute_mongo_write_pocket_asset,
    execute_mongo_pocket_write
]

mongo_write_group_third_sensor = [
    sensor_execute_mongo_write_pocket_asset,
    sensor_execute_mongo_pocket_write
]

mongo_write_group_fourth = [
    execute_mongo_write_pnl_weekly,
    execute_mongo_write_pnl_monthly
]

mongo_write_group_fourth_sensor = [
    sensor_execute_mongo_write_pnl_weekly,
    sensor_execute_mongo_write_pnl_monthly
]

mongo_write_group_fifth = [
    execute_mongo_write_pnl_quarterly,
    execute_mongo_write_pnl_yearly
]

mongo_write_group_fifth_sensor = [
    sensor_execute_mongo_write_pnl_quarterly,
    sensor_execute_mongo_write_pnl_yearly
]

chain(
    dag_start,
    mongo_write_group_first,
    mongo_write_group_first_sensor,
    execute_mongo_write_gold,
    sensor_execute_mongo_write_gold,
    mongo_write_group_second,
    mongo_write_group_second_sensor,
    execute_mongo_write_cypto_currency,
    sensor_execute_mongo_write_cypto_currency,
    mongo_write_group_third,
    mongo_write_group_third_sensor,
    execute_mongo_write_portfolio,
    sensor_execute_mongo_write_portfolio,
    mongo_write_group_fourth,
    mongo_write_group_fourth_sensor,
    execute_mongo_write_pnl_daily,
    sensor_execute_mongo_write_pnl_daily,
    mongo_write_group_fifth,
    mongo_write_group_fifth_sensor,
    end
)