import os, sys
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.utils.helpers import chain
import pendulum
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.operators.python_operator import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJobFlowOperator
from airflow.operators.dummy_operator import DummyOperator
start_date = (Variable.get("default_args", deserialize_json=True).get("start_date").split(","))
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
pipeline_utils_dir_path = os.path.join(ROOT_DIR, "dags/pipeline_utils")
sys.path.append(pipeline_utils_dir_path)
from pipeline_utils.s3_to_bq import run_s3_to_bq
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
from base_dags.base_dag import BASE_DAG
from portfolio_emr_dags.portfolio_emr_utils import *
from portfolio_emr_dags.python_job_utils import *
from alerting.dag_alert import DagAlert
from helpers.helper import Helper


env = Variable.get("ENV")
helper = Helper(env=env)
config = Variable.get("portfolio_emr_dag_config", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)
s3_to_bq_tables_to_sync = Variable.get("s3_to_bq_sync_config", deserialize_json=True).get("portfolio_snapshot_dag")
dag_ids_key = list(config["external_dag_id"].keys())
dag_ids_values = list(config["external_dag_id"].values())
dag_set = set(dag_ids_key).union(set(dag_ids_values))
dag_ids = list(dag_set)
root_dag_id = config["dag_id"]
config["dag_id"] = config["data_validation_dag_id"]

ZONE, DELTA = dateutil.tz.gettz("Asia/Jakarta"), -1
DATE = datetime.now(tz=ZONE) + timedelta(DELTA)
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)

emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
aws_conn_id=emr_config["aws_conn_id"]
emr_config["user_portfolio_validation_folder_url"] = config["user_portfolio_validation_folder_url"]

portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)

python_job_utils = PythonJobUtils(
    env=env,
    aws_access_key=emr_config["access_key"],
    aws_secret_key=emr_config["secret_key"],
    slack_webhook_url=emr_config["slack_webhook_url"]
)

BaseDag = BASE_DAG(
    dag_id=config["dag_id"],
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval="55 16 * * *",
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

DAG = BaseDag.Create_Dag(
    dagrun_timeout=timedelta(minutes=390),
    max_active_runs=1,
    concurrency=config["concurrency"]
)

globals()[config["dag_id"]] = DAG

external_dag_id = config['external_dag_id'][config["dag_id"]]
external_task_id = config['external_task_id'][config["dag_id"]]
dag_start = ExternalTaskSensor(
    external_dag_id=external_dag_id,
    external_task_id=external_task_id,
    allowed_states=["success", "upstream_failed", "failed"],
    dag=DAG,
    task_id="start_data_validations",
)

execute_clevertap_user_properties = EmrAddStepsOperator(
    task_id="execute_clevertap_user_properties",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_clevertap_user_properties", "clevertap_profile/clevertap_user_properties.py", "medium"),
    params={"config": "config.json"},
    dag = DAG
)

sensor_execute_clevertap_user_properties = EmrStepSensor(
    task_id="sensor_execute_clevertap_user_properties",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_clevertap_user_properties', key='return_value')[" + str(len(portfolio_emr_utils.execute_spark_job("execute_clevertap_user_properties", "clevertap_profile/clevertap_user_properties.py", "medium")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag = DAG
)

execute_portfolio_data_validation = EmrAddStepsOperator(
    task_id='execute_portfolio_data_validation',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_portfolio_data_validation", "data_validation/current_user_portfolio_validation.py", "small"),
    params={"config": "config.json"},
    dag = DAG
)

sensor_execute_portfolio_data_validation = EmrStepSensor(
    task_id='sensor_execute_portfolio_data_validation',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_portfolio_data_validation', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_spark_job("execute_portfolio_data_validation", "data_validation/current_user_portfolio_validation.py", "small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag = DAG
)

execute_user_pocket_data_validation = EmrAddStepsOperator(
    task_id='execute_user_pocket_data_validation',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_user_pocket_data_validation", "data_validation/user_pocket_portfolio_validation.py", "small"),
    params={"config": "config.json"},
    dag = DAG
)

sensor_execute_user_pocket_data_validation = EmrStepSensor(
    task_id='sensor_execute_user_pocket_data_validation',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_user_pocket_data_validation', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_spark_job("execute_user_pocket_data_validation", "data_validation/user_pocket_portfolio_validation.py", "small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag = DAG
)

user_current_portfolio_validation_slack_alert = PythonOperator(
    task_id=f"user_current_portfolio_validation_slack_alert",
    python_callable=python_job_utils.user_current_portfolio_alert,
    op_kwargs=emr_config,
    execution_timeout=timedelta(minutes=120),
    dag = DAG,
)

user_clevertap_prop_sftp_upload = PythonOperator(
    task_id="user_clevertap_prop_sftp_upload",
    python_callable=python_job_utils.upload_to_sftp_clevertap_prop,
    op_kwargs=config,
    execution_timeout=timedelta(minutes=60),
    dag = DAG,
)

run_s3_to_bq = PythonOperator(
    task_id=f"Run-S3-To-BQ",
    python_callable=run_s3_to_bq.main,
    op_args=[{"bq_tables_populate": s3_to_bq_tables_to_sync}],
    execution_timeout=timedelta(minutes=60),
    dag = DAG,
    provide_context=True
)


copy_spark_logs_hdfs_to_s3 = EmrAddStepsOperator(
    task_id='copy_spark_logs_hdfs_to_s3',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps= portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE),
    dag = DAG,
)

sensor_copy_spark_logs_hdfs_to_s3 = EmrStepSensor(
    task_id='sensor_copy_spark_logs_hdfs_to_s3',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('copy_spark_logs_hdfs_to_s3', key='return_value')[" + str(
        len(portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE)) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag = DAG,
)

terminate_emr_cluster = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    dag = DAG,
)

delete_xcom = PythonOperator(
    task_id="delete_xcom_portfolio_emr_dags",
    python_callable=portfolio_emr_utils.clean_xcom,
    op_kwargs={'dag_ids': dag_ids},
    dag = DAG,
)

end = DummyOperator(task_id="dag_end_data_validation", dag = DAG)

data_validations = [execute_portfolio_data_validation,
                    execute_user_pocket_data_validation,
                    execute_clevertap_user_properties]

data_validations_sensors = [sensor_execute_portfolio_data_validation,
                            sensor_execute_user_pocket_data_validation,
                            sensor_execute_clevertap_user_properties]


chain(
    dag_start,
    data_validations,
    data_validations_sensors,
    copy_spark_logs_hdfs_to_s3,
    sensor_copy_spark_logs_hdfs_to_s3,
    terminate_emr_cluster,
    user_current_portfolio_validation_slack_alert,
    user_clevertap_prop_sftp_upload,
    run_s3_to_bq,
    delete_xcom,
    end
)