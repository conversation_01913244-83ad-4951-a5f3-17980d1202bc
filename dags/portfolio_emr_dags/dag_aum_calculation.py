import os, sys
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.utils.helpers import chain
import pendulum
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.operators.python_operator import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.operators.dummy_operator import DummyOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJobFlowOperator
start_date = (Variable.get("default_args", deserialize_json=True).get("start_date").split(","))
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
from base_dags.base_dag import BASE_DAG
from portfolio_emr_dags.portfolio_emr_utils import *
from helpers.helper import Helper
from alerting.dag_alert import DagAlert

env = Variable.get("ENV")
helper = Helper(env=env)
config = Variable.get("portfolio_emr_dag_config", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)
dag_ids = list(config["external_dag_id"].keys())
dag_ids.append(config["dag_id"])
root_dag_id = config["dag_id"]
config["dag_id"] = config["aum_calculation_dag_id"]
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)

emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
aws_conn_id = emr_config["aws_conn_id"]

portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)

BaseDag = BASE_DAG(
    dag_id=config["dag_id"],
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval="55 16 * * *",
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

DAG = BaseDag.Create_Dag(
    dagrun_timeout=timedelta(minutes=390),
    max_active_runs=1,
    concurrency=config["concurrency"]
)

globals()[config["dag_id"]] = DAG

external_dag_id = config['external_dag_id'][config["dag_id"]]
external_task_id = config['external_task_id'][config["dag_id"]]
dag_start = ExternalTaskSensor(
    external_dag_id=external_dag_id,
    external_task_id=external_task_id,
    allowed_states=["success", "upstream_failed", "failed"],
    dag=DAG,
    task_id="start_aum_calculation",
)

execute_crypto_transaction = EmrAddStepsOperator(
    task_id="execute_crypto_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_crypto_transaction", "aum_calculation/cryptoTransaction/aum_crypto_transaction.py","large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_transaction = EmrStepSensor(
    task_id="sensor_execute_crypto_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_crypto_transaction", "aum_calculation/cryptoTransaction/aum_crypto_transaction.py","large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)


execute_forex_transaction = EmrAddStepsOperator(
    task_id="execute_forex_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_forex_transaction", "aum_calculation/forexTransaction/aum_forex_transaction.py","medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_forex_transaction = EmrStepSensor(
    task_id="sensor_execute_forex_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_forex_transaction", "aum_calculation/forexTransaction/aum_forex_transaction.py","medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_forex_leverage_aum_transaction = EmrAddStepsOperator(
    task_id="execute_forex_leverage_aum_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_forex_leverage_aum_transaction", "aum_calculation/forexTransaction/aum_forex_leverage.py","medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_forex_leverage_aum_transaction = EmrStepSensor(
    task_id="sensor_execute_forex_leverage_aum_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_leverage_aum_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_forex_leverage_aum_transaction", "aum_calculation/forexTransaction/aum_forex_leverage.py","medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)


execute_fund_transaction = EmrAddStepsOperator(
    task_id="execute_fund_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_fund_transaction", "aum_calculation/fundTransaction/aum_fund_transaction.py","medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_fund_transaction = EmrStepSensor(
    task_id="sensor_execute_fund_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_fund_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_fund_transaction", "aum_calculation/fundTransaction/aum_fund_transaction.py","medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_transaction = EmrAddStepsOperator(
    task_id="execute_global_stock_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_global_stock_transaction", "aum_calculation/globalStockTransaction/aum_global_stock_transaction.py","medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_transaction = EmrStepSensor(
    task_id="sensor_execute_global_stock_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_global_stock_transaction", "aum_calculation/globalStockTransaction/aum_global_stock_transaction.py","medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_indo_stock_transaction = EmrAddStepsOperator(
    task_id="execute_indo_stock_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_indo_stock_transaction", "aum_calculation/indoStock/aum_indo_stock_transaction.py","medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_indo_stock_transaction = EmrStepSensor(
    task_id="sensor_execute_indo_stock_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_indo_stock_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_indo_stock_transaction", "aum_calculation/indoStock/aum_indo_stock_transaction.py","medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gold_transaction = EmrAddStepsOperator(
    task_id="execute_gold_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_gold_transaction", "aum_calculation/goldTransaction/aum_gold_transaction.py","large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gold_transaction = EmrStepSensor(
    task_id="sensor_execute_gold_transaction",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gold_transaction', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_gold_transaction", "aum_calculation/goldTransaction/aum_gold_transaction.py","large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_snap_aum_calculation = EmrAddStepsOperator(
    task_id="execute_snap_aum_calculation",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job_aum("execute_snap_aum_calculation", "aum_calculation/snapAum/snap_aum_calculation.py","large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_snap_aum_calculation = EmrStepSensor(
    task_id="sensor_execute_snap_aum_calculation",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + root_dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_snap_aum_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job_aum("execute_snap_aum_calculation", "aum_calculation/snapAum/snap_aum_calculation.py","large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

end = DummyOperator(task_id="dag_end_aum_calculation", dag=DAG)

aum_calculations = [
    execute_crypto_transaction,
    execute_forex_transaction,
    execute_fund_transaction,
    execute_global_stock_transaction,
    execute_gold_transaction,
    execute_indo_stock_transaction,
    execute_forex_leverage_aum_transaction
]

sensor_aum_calculations = [
    sensor_execute_crypto_transaction,
    sensor_execute_forex_transaction,
    sensor_execute_fund_transaction,
    sensor_execute_global_stock_transaction,
    sensor_execute_gold_transaction,
    sensor_execute_indo_stock_transaction,
    sensor_execute_forex_leverage_aum_transaction
]

chain(
    dag_start,
    aum_calculations,
    sensor_aum_calculations,
    execute_snap_aum_calculation,
    sensor_execute_snap_aum_calculation,
    end
)


