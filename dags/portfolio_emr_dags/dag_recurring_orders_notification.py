import os, sys
from datetime import timedelta, datetime
from base_dags.base_dag import BASE_DAG
from airflow.models import Variable
from airflow.utils.helpers import chain
from datetime import timedelta
import pendulum
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import (
    EmrTerminateJobFlowOperator,
)
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.models import XCom
from airflow.utils.db import provide_session

ROOT_DIR = os.path.dirname(os.path.abspath("__file__"))
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
Helpers_Dir_Path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(Helpers_Dir_Path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
custom_operator_path = os.path.join(ROOT_DIR, "dags/pluang_custom_operators")
sys.path.append(custom_operator_path)
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
from helpers.helper import Helper
from portfolio_emr_dags.portfolio_emr_utils import PortfolioEmrUtils
from alerting.dag_alert import DagAlert

pipeline_utils_dir_path = os.path.join(ROOT_DIR, "dags/pipeline_utils")
sys.path.append(pipeline_utils_dir_path)
from pluang_custom_operators.pluang_emr_step_sensor import PluangEmrStepSensor
from pipeline_utils.s3_to_bq import run_s3_to_bq
ZONE, DELTA = dateutil.tz.gettz("Asia/Jakarta"), 0
DATE = datetime.now(tz=ZONE) + timedelta(DELTA)
env = Variable.get("ENV")
helper = Helper(env=env)
s3_to_bq_tables_to_sync = Variable.get("s3_to_bq_sync_config", deserialize_json=True).get("frequency_based_recurring_orders_dag")
config = Variable.get("frequency_based_recurring_orders_notification", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)

opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)

emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
aws_conn_id=emr_config["aws_conn_id"]


# Override EmrStepSensor globally within this DAG file
globals()["EmrStepSensor"] = PluangEmrStepSensor


portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)


## Base DAG INIT
BaseDag = BASE_DAG(
    dag_id=config["dag_id"],
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval=config["schedule_interval"],
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

# Create EMR Dag
(
    DAG,
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    SENSOR_Setup_Hadoop_Debugging,
) = BaseDag.Create_EMR_Dag(
    dagrun_timeout=timedelta(minutes=240),
    max_active_runs=1,
    concurrency=config["concurrency"],
    emr_core=config["emr_core"],
    env=env,
    script_bootstrap_action_file=emr_config["script_bootstrap_action_file"],
    region=emr_config["region"],
    log_uri_bucket=emr_config["log_uri_bucket"],
    resource_bucket=emr_config["resource_bucket"],
    emr_key_pair=emr_config["emr_key_pair"],
    master_security_group=emr_config["master_security_group"],
    slave_security_group=emr_config["slave_security_group"],
    service_access_security_group=emr_config["service_access_security_group"],
    subnet_id=emr_config["subnet_id"],
    ec2_instance_type_config=emr_config["ec2_instance_type_config"],
    aws_conn_id=aws_conn_id,
    emr_conn_id=emr_config["emr_conn_id"],
)

globals()[config["dag_id"]] = DAG
chain(
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    SENSOR_Setup_Hadoop_Debugging,
)

kafka_batch_consumer = EmrAddStepsOperator(
    task_id="Kafka_Batch_Consumer",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_kafka_batch_consumer", "kafka-batch-consumer/batch_consumer.py","small", schedule="jkt_07_00"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_kafka_batch_consumer = EmrStepSensor(
    task_id="SENSOR_Kafka_Batch_Consumer",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Kafka_Batch_Consumer', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_kafka_batch_consumer", "kafka-batch-consumer/batch_consumer.py","small", schedule="jkt_07_00")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_bappebti_wallets_snapshot = EmrAddStepsOperator(
    task_id="Execute-Bappebti-Wallets-Snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_bappebti_wallets_snapshot", "bappebti_wallets/snap_bappebti_wallet.py", "small", offset="0",cut_off_time="1730JKT"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_bappebti_wallets_snapshot = EmrStepSensor(
    task_id="SENSOR_execute_bappebti_wallets_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Bappebti-Wallets-Snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_bappebti_wallets_snapshot", "bappebti_wallets/snap_bappebti_wallet.py", "small", offset="0", cut_off_time="1730JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_forex_account_snapshot = EmrAddStepsOperator(
    task_id="Execute-Forex-Account-Snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_forex_account_snapshot", "forex/snap_forex_accounts.py", "small", offset="0", cut_off_time="1730JKT"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_forex_account_snapshot = EmrStepSensor(
    task_id="SENSOR_execute_forex_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Forex-Account-Snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_forex_account_snapshot", "forex/snap_forex_accounts.py", "small", offset="0", cut_off_time="1730JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_due_recurring_order = EmrAddStepsOperator(
    task_id="Execute-Due-Recurring-Order",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_due_recurring_order", "recurring_order/frequency_based_recurring_orders_snapshot.py", "small",offset="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_due_recurring_order = EmrStepSensor(
    task_id="SENSOR_execute_due_recurring_order",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Due-Recurring-Order', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_due_recurring_order", "recurring_order/frequency_based_recurring_orders_snapshot.py","small", offset="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_frequency_based_recurring_order_notification = EmrAddStepsOperator(
    task_id="Execute-Frequency-Based-Recurring-Orders-Notification",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_frequency_based_recurring_order_notification", "recurring_order/frequency_based_recurring_orders_notification.py", "small",offset="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_frequency_based_recurring_order_notification = EmrStepSensor(
    task_id="SENSOR_execute_frequency_based_recurring_order_notification",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Frequency-Based-Recurring-Orders-Notification', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_frequency_based_recurring_order_notification", "recurring_order/frequency_based_recurring_orders_notification.py","small", offset="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_snap_price = EmrAddStepsOperator(
    task_id="execute_snap_price",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_snap_price", "snap_price/snap_price.py", "small", price_group="jkt_5_30_pm", offset="0", hour="10", min="30", sec="0", ms="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_snap_price = EmrStepSensor(
    task_id="sensor_execute_snap_price",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_snap_price', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_snap_price", "snap_price/snap_price.py", "small", price_group="jkt_5_30_pm", offset="0", hour="10", min="30", sec="0", ms="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

execute_global_stock_dividend = EmrAddStepsOperator(
    task_id='execute_global_stock_dividend',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_global_stock_dividend", "global_stock/snap_global_stock_dividend.py","small"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_global_stock_dividend = EmrStepSensor(
    task_id='sensor_execute_global_stock_dividend',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_dividend', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_global_stock_dividend", "global_stock/snap_global_stock_dividend.py","small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_gss_daily_statement = EmrAddStepsOperator(
    task_id='execute_gss_daily_statement',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_gss_daily_statement", "user_daily_statement/gss_daily_statement.py","small"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_gss_daily_statement = EmrStepSensor(
    task_id='sensor_execute_gss_daily_statement',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gss_daily_statement', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_gss_daily_statement", "user_daily_statement/gss_daily_statement.py", "small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_accounts_snapshot = EmrAddStepsOperator(
    task_id='execute_accounts_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_accounts_snapshot", "snap_accounts/snap_accounts.py", "small", offset="0"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_accounts_snapshot = EmrStepSensor(
    task_id='sensor_execute_accounts_snapshotnt',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_accounts_snapshot', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_spark_job("execute_accounts_snapshot", "snap_accounts/snap_accounts.py","small", offset="0")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_daily_statement = EmrAddStepsOperator(
    task_id='execute_daily_statement',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_daily_statement", "user_daily_statement/daily_statement.py","small"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_daily_statement = EmrStepSensor(
    task_id='sensor_execute_daily_statement',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_daily_statement', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_daily_statement", "user_daily_statement/daily_statement.py","small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_monthly_statement = EmrAddStepsOperator(
    task_id='execute_monthly_statement',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_monthly_statement", "user_daily_statement/monthly_statement.py","small"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]]
)

sensor_execute_monthly_statement = EmrStepSensor(
    task_id='sensor_execute_monthly_statement',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_monthly_statement', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_monthly_statement", "user_daily_statement/monthly_statement.py","small")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]]
)

execute_gss_kyc_information_snapshot = EmrAddStepsOperator(
    task_id="execute_gss_kyc_information_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_snapshot", "user_profile/snap_gss_kyc_information.py", "small",offset="0",hour = "10",minute = "30"),
    params={"config": "config.json"},
    dag=globals()[config["dag_id"]],
)

sensor_execute_gss_kyc_information_snapshot = EmrStepSensor(
    task_id="sensor_execute_gss_kyc_information_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gss_kyc_information_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_snapshot", "user_profile/snap_gss_kyc_information.py", "small",offset="0",hour = "10",minute = "30")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

run_s3_to_bq = PythonOperator(
    task_id=f"Run-S3-To-BQ",
    python_callable=run_s3_to_bq.main,
    op_args=[{"bq_tables_populate": s3_to_bq_tables_to_sync}],
    execution_timeout=timedelta(minutes=30),
    dag=globals()[config["dag_id"]],
    provide_context=True
)


copy_spark_logs_hdfs_to_s3 = EmrAddStepsOperator(
    task_id="copy_spark_logs_hdfs_to_s3",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE),
    dag=globals()[config["dag_id"]],
)

sensor_copy_spark_logs_hdfs_to_s3 = EmrStepSensor(
    task_id="sensor_copy_spark_logs_hdfs_to_s3",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('copy_spark_logs_hdfs_to_s3', key='return_value')["
            + str(len(portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE)) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

terminate_emr_cluster = EmrTerminateJobFlowOperator(
    task_id="Terminate-EMR-Cluster",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + config["dag_id"] + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[config["dag_id"]],
)

delete_xcom = PythonOperator(
    task_id="delete_xcom",
    python_callable=portfolio_emr_utils.clean_xcom,
    op_kwargs={'dag_ids': [config["dag_id"]]},
    dag=globals()[config["dag_id"]],
)


@provide_session
def clean_xcom(session=None, dag_id=None, **context):
    if not dag_id:
        dag = context["dag"]
        dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()


end = DummyOperator(task_id="End", dag=globals()[config["dag_id"]])

recurring_orders_data_prep_jobs = [
    execute_due_recurring_order,
    execute_snap_price,
    execute_forex_account_snapshot,
    execute_bappebti_wallets_snapshot
]

sensor_recurring_orders_data_prep_jobs = [
    sensor_execute_due_recurring_order,
    sensor_execute_snap_price,
    sensor_execute_forex_account_snapshot,
    sensor_execute_bappebti_wallets_snapshot
]

asset_daily_statements = [
    execute_accounts_snapshot,
    execute_gss_daily_statement
]

sensor_asset_daily_statements = [
    sensor_execute_accounts_snapshot,
    sensor_execute_gss_daily_statement
]

chain(
    SENSOR_Setup_Hadoop_Debugging,
    kafka_batch_consumer,
    sensor_kafka_batch_consumer,
    recurring_orders_data_prep_jobs,
    sensor_recurring_orders_data_prep_jobs,
    execute_frequency_based_recurring_order_notification,
    sensor_execute_frequency_based_recurring_order_notification,
    execute_global_stock_dividend,
    sensor_execute_global_stock_dividend,
    execute_gss_kyc_information_snapshot,
    sensor_execute_gss_kyc_information_snapshot,
    asset_daily_statements,
    sensor_asset_daily_statements,
    execute_daily_statement,
    sensor_execute_daily_statement,
    execute_monthly_statement,
    sensor_execute_monthly_statement,
    copy_spark_logs_hdfs_to_s3,
    sensor_copy_spark_logs_hdfs_to_s3,
    terminate_emr_cluster,
    run_s3_to_bq,
    delete_xcom,
    end
)