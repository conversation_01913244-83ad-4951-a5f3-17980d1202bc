import os, sys
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.utils.helpers import chain
import pendulum
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.operators.python_operator import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow_kubernetes_job_operator.kubernetes_job_operator import KubernetesJobOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJobFlowOperator

start_date = (Variable.get("default_args", deserialize_json=True).get("start_date").split(","))
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
custom_operator_path = os.path.join(ROOT_DIR, "dags/pluang_custom_operators")
sys.path.append(custom_operator_path)
from base_dags.base_dag import BASE_DAG
from portfolio_emr_dags.portfolio_emr_utils import *
from helpers.helper import Helper
from alerting.dag_alert import DagAlert
from pluang_custom_operators.pluang_emr_step_sensor import PluangEmrStepSensor
from pipeline_utils.s3_to_bq import run_s3_to_bq

env = Variable.get("ENV")
helper = Helper(env=env)
config = Variable.get("event_and_price_emr_dag_config", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)
dag_id = config["dag_id"]
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)
NAME = Variable.get("MWAA_NAMESPACE")
IMAGE = Variable.get("ecr_image_firebase_transaction")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
ZONE, DELTA = dateutil.tz.gettz("Asia/Jakarta"), -1
DATE = datetime.now(tz=ZONE) + timedelta(DELTA)
s3_to_bq_tables_to_sync = Variable.get("s3_to_bq_sync_config", deserialize_json=True).get("price_event_consumer_dag")


emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
aws_conn_id=emr_config["aws_conn_id"]


# Override EmrStepSensor globally within this DAG file
globals()["EmrStepSensor"] = PluangEmrStepSensor


portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)

BaseDag = BASE_DAG(
    dag_id=dag_id,
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval="55 16 * * *",
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

# Create EMR Dag
(
    DAG,
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    sensor_Setup_Hadoop_Debugging,
) = BaseDag.Create_EMR_Dag(
    dagrun_timeout=timedelta(minutes=390),
    max_active_runs=1,
    concurrency=config["concurrency"],
    emr_core=config["emr_core"],
    env=env,
    script_bootstrap_action_file=emr_config["script_bootstrap_action_file"],
    region=emr_config["region"],
    log_uri_bucket=emr_config["log_uri_bucket"],
    resource_bucket=emr_config["resource_bucket"],
    emr_key_pair=emr_config["emr_key_pair"],
    master_security_group=emr_config["master_security_group"],
    slave_security_group=emr_config["slave_security_group"],
    service_access_security_group=emr_config["service_access_security_group"],
    subnet_id=emr_config["subnet_id"],
    ec2_instance_type_config=emr_config["ec2_instance_type_config"],
    aws_conn_id=aws_conn_id,
    emr_conn_id=emr_config["emr_conn_id"],
)

globals()[dag_id] = DAG

chain(
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    sensor_Setup_Hadoop_Debugging
)

execute_global_stock_price_consumer = EmrAddStepsOperator(
    task_id="execute_global_stock_price_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_global_stock_price_consumer", "global_stocks_price_consumer/global_stock_price_consumer.py","price"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_price_consumer = EmrStepSensor(
    task_id="sensor_execute_global_stock_price_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_price_consumer', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_global_stock_price_consumer", "global_stocks_price_consumer/global_stock_price_consumer.py","price")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_currency_price_consumer = EmrAddStepsOperator(
    task_id="execute_crypto_currency_price_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_crypto_currency_price_consumer", "crypto_currency_price_consumer/crypto_currency_price_consumer.py","price"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_currency_price_consumer = EmrStepSensor(
    task_id="sensor_execute_crypto_currency_price_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_currency_price_consumer', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_crypto_currency_price_consumer", "crypto_currency_price_consumer/crypto_currency_price_consumer.py", "price")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_futures_price_consumer = EmrAddStepsOperator(
    task_id="execute_crypto_futures_price_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_crypto_futures_price_consumer", "crypto_futures/crypto_futures_price_consumer.py","price"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_futures_price_consumer = EmrStepSensor(
    task_id="sensor_execute_crypto_futures_price_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_futures_price_consumer', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_crypto_futures_price_consumer", "crypto_futures/crypto_futures_price_consumer.py", "price")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_pluang_events_consumer = EmrAddStepsOperator(
    task_id="execute_pluang_events_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_pluang_events_consumer", "pluang_events_consumer/events_consumer.py","price"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_pluang_events_consumer = EmrStepSensor(
    task_id="sensor_execute_pluang_events_consumer",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_pluang_events_consumer', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_pluang_events_consumer", "pluang_events_consumer/events_consumer.py","price")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_pluang_clevertap_flatten_data = EmrAddStepsOperator(
    task_id="execute_pluang_clevertap_flatten_data",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_pluang_clevertap_flatten_data", "pluang_events_consumer/event_consumer_flatten_data.py","price"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_pluang_clevertap_flatten_data = EmrStepSensor(
    task_id="sensor_execute_pluang_clevertap_flatten_data",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_pluang_clevertap_flatten_data', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_pluang_events_consumer", "pluang_events_consumer/event_consumer_flatten_data.py","price")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_pluang_crypto_order_book_processor = EmrAddStepsOperator(
    task_id="execute_pluang_crypto_order_book_processor",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_pluang_crypto_order_book_processor", "pluang_events_consumer/pluang_crypto_order_book_processor.py","price"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_pluang_crypto_order_book_processor = EmrStepSensor(
    task_id="sensor_execute_pluang_crypto_order_book_processor",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_pluang_crypto_order_book_processor', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("sensor_execute_pluang_crypto_order_book_processor", "pluang_events_consumer/pluang_crypto_order_book_processor.py","price")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

crypto_advanced_order_events_sync = KubernetesJobOperator(
    task_id="crypto_advanced_order_events_sync",
    namespace=NAME,
    image=IMAGE,
    delete_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    command=["python3","-m","main","--type","crypto_advanced"],
    get_logs=True,
    dag=DAG,
)

s3_to_bq_sync = PythonOperator(
    task_id=f"Run-S3-To-BQ-Sync",
    python_callable=run_s3_to_bq.main,
    op_args=[{"bq_tables_populate": s3_to_bq_tables_to_sync}],
    execution_timeout=timedelta(minutes=60),
    dag = DAG,
    provide_context=True
)

copy_spark_logs_hdfs_to_s3 = EmrAddStepsOperator(
    task_id='copy_spark_logs_hdfs_to_s3',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps= portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE),
    dag = DAG,
)

sensor_copy_spark_logs_hdfs_to_s3 = EmrStepSensor(
    task_id='sensor_copy_spark_logs_hdfs_to_s3',
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('copy_spark_logs_hdfs_to_s3', key='return_value')[" + str(
        len(portfolio_emr_utils.copy_spark_logs_hdfs_to_s3(DATE)) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag = DAG,
)


terminate_emr_cluster = EmrTerminateJobFlowOperator(
    task_id="terminate_emr_cluster",
    job_flow_id="{{ task_instance.xcom_pull(dag_id='" + dag_id + "',task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    dag = DAG,
)

delete_xcom = PythonOperator(
    task_id="delete_xcom_events_and_price_consumer",
    python_callable=portfolio_emr_utils.clean_xcom,
    op_kwargs={'dag_ids': [dag_id]},
    dag = DAG,
)

chain(
    sensor_Setup_Hadoop_Debugging,
    execute_global_stock_price_consumer,
    sensor_execute_global_stock_price_consumer,
    execute_crypto_currency_price_consumer,
    sensor_execute_crypto_currency_price_consumer,
    execute_crypto_futures_price_consumer,
    sensor_execute_crypto_futures_price_consumer,
    execute_pluang_events_consumer,
    sensor_execute_pluang_events_consumer,
    execute_pluang_clevertap_flatten_data,
    sensor_execute_pluang_clevertap_flatten_data,
    execute_pluang_crypto_order_book_processor,
    sensor_execute_pluang_crypto_order_book_processor,
    copy_spark_logs_hdfs_to_s3,
    sensor_copy_spark_logs_hdfs_to_s3,
    terminate_emr_cluster
)
terminate_emr_cluster >> s3_to_bq_sync >> delete_xcom
terminate_emr_cluster >> crypto_advanced_order_events_sync