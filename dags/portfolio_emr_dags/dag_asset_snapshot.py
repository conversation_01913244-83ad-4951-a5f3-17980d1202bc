import os, sys
from datetime import timedelta, datetime
from airflow.models import Variable
from airflow.utils.helpers import chain
import pendulum
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
start_date = (Variable.get("default_args", deserialize_json=True).get("start_date").split(","))
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
portfolio_emr_dags_path = os.path.join(ROOT_DIR, "dags/portfolio_emr_dags")
sys.path.append(portfolio_emr_dags_path)
helpers_path = os.path.join(ROOT_DIR, "dags/helpers")
sys.path.append(helpers_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
custom_operator_path = os.path.join(ROOT_DIR, "dags/pluang_custom_operators")
sys.path.append(custom_operator_path)
from base_dags.base_dag import BASE_DAG
from portfolio_emr_dags.portfolio_emr_utils import *
from portfolio_emr_dags.python_job_utils import *
from helpers.helper import Helper
from alerting.dag_alert import DagAlert
from pluang_custom_operators.pluang_emr_step_sensor import PluangEmrStepSensor

env = Variable.get("ENV")
helper = Helper(env=env)

config = Variable.get("portfolio_emr_dag_config", deserialize_json=True)
emr_config = Variable.get("emr_dag_config", deserialize_json=True)
secret_config = Variable.get("PLUANG_DE_COMMON_ENVIRONMENT_VARIABLES", deserialize_json=True)
options_price_config = config["global_stock_option_price"]
options_price_config["mongo_uri"] = "mongodb+srv://{}:{}@{}/".format(secret_config["pricing_mongo_user_name"], secret_config["pricing_mongo_password"], secret_config["pricing_mongo_host"])
options_price_config["bucket"] = emr_config["data_bucket"]
options_price_config["access_key"] = secret_config["access_key"]
options_price_config["secret_key"] = secret_config["secret_access_key"]
options_price_config["bucket_region"] = emr_config["region"]
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
dag_alert = DagAlert(emr_config["username"], emr_config["slack_conn_id"], OPSGENIE_CONN_ID)
aws_conn_id=emr_config["aws_conn_id"]
emr_config["DEFAULT_ARGS"]["start_date"] = pendulum.datetime(year, month, day, tz="UTC")
emr_config["DEFAULT_ARGS"]["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])


globals()["EmrStepSensor"] = PluangEmrStepSensor


## Utils Init
portfolio_emr_utils = PortfolioEmrUtils(
    files_path=emr_config["files"],
    jar_path=emr_config["jars"],
    job_config_path=emr_config["job_config_path"],
    resource_config=emr_config["resource_config"],
    log_path=emr_config["log_path"],
    env=env,
    aiven_kafka_auth_file=emr_config["aiven_kafka_auth_file"]
)

python_job_utils = PythonJobUtils(
    env=env,
    aws_access_key=emr_config["access_key"],
    aws_secret_key=emr_config["secret_key"],
    slack_webhook_url=emr_config["slack_webhook_url"]
)

## Base DAG INIT
BaseDag = BASE_DAG(
    dag_id=config["dag_id"],
    default_args=emr_config["DEFAULT_ARGS"],
    schedule_interval="55 16 * * *",
    catchup=False,
    tags=["data-eng", "spark", "EMR", "portfolio"],
    team="data-eng",
)

# Create EMR Dag
(
    DAG,
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    sensor_Setup_Hadoop_Debugging,
) = BaseDag.Create_EMR_Dag(
    dagrun_timeout=timedelta(minutes=390),
    max_active_runs=1,
    concurrency=config["concurrency"],
    emr_core=config["emr_core"],
    env=env,
    script_bootstrap_action_file=emr_config["script_bootstrap_action_file"],
    region=emr_config["region"],
    log_uri_bucket=emr_config["log_uri_bucket"],
    resource_bucket=emr_config["resource_bucket"],
    emr_key_pair=emr_config["emr_key_pair"],
    master_security_group=emr_config["master_security_group"],
    slave_security_group=emr_config["slave_security_group"],
    service_access_security_group=emr_config["service_access_security_group"],
    subnet_id=emr_config["subnet_id"],
    ec2_instance_type_config=emr_config["ec2_instance_type_config"],
    aws_conn_id=aws_conn_id,
    emr_conn_id=emr_config["emr_conn_id"],
)

globals()[config["dag_id"]] = DAG

option_price_from_mongo = PythonOperator(
    task_id='option_price_from_mongo',
    python_callable=python_job_utils.fetch_global_stock_option_price_from_mongo,
    op_kwargs=options_price_config,
    execution_timeout=timedelta(minutes=20),
    dag=DAG
)

cluster_startup_group = [is_cluster_active, option_price_from_mongo]

chain(
    Create_EMR_Cluster,
    cluster_startup_group,
    Setup_Hadoop_Debugging,
    sensor_Setup_Hadoop_Debugging
)

kafka_batch_consumer = EmrAddStepsOperator(
    task_id="kafka_batch_consumer",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_kafka_batch_consumer", "kafka-batch-consumer/batch_consumer.py","large", schedule="default"),
    params={"config": "config.json"},
    dag=DAG
)

sensor_kafka_batch_consumer = EmrStepSensor(
    task_id='sensor_kafka_batch_consumer',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('kafka_batch_consumer', key='return_value')[" + str(len(
        portfolio_emr_utils.execute_kafka_job("execute_kafka_batch_consumer", "kafka-batch-consumer/batch_consumer.py","large", schedule="default")) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG
)

execute_kafka_msg_validation = EmrAddStepsOperator(
    task_id="execute_kafka_msg_validation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_kafka_msg_validation", "kafka-batch-consumer/check_kafka_last_msg_time.py", "medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_kafka_msg_validation = EmrStepSensor(
    task_id="sensor_execute_kafka_msg_validation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_kafka_msg_validation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_kafka_msg_validation", "kafka-batch-consumer/check_kafka_last_msg_time.py", "medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_s3_to_hdfs_copy = EmrAddStepsOperator(
    task_id="execute_s3_to_hdfs_copy",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_s3_to_hdfs_copy", "copy_to_hdfs/s3_to_hdfs.py", "large", group="asset_return_snapshot_group"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_s3_to_hdfs_copy = EmrStepSensor(
    task_id="sensor_execute_s3_to_hdfs_copy",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_s3_to_hdfs_copy', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_s3_to_hdfs_copy", "copy_to_hdfs/s3_to_hdfs.py", "large", group="asset_return_snapshot_group")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_due_recurring_order = EmrAddStepsOperator(
    task_id="execute_due_recurring_order",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_kafka_job("execute_due_recurring_order", "recurring_order/frequency_based_recurring_orders_snapshot.py","medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_due_recurring_order = EmrStepSensor(
    task_id="sensor_execute_due_recurring_order",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_due_recurring_order', key='return_value')["
            + str(len(portfolio_emr_utils.execute_kafka_job("execute_due_recurring_order", "recurring_order/frequency_based_recurring_orders_snapshot.py","medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_snap_price = EmrAddStepsOperator(
    task_id="execute_snap_price",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_snap_price", "snap_price/snap_price.py", "small", price_group="jkt_0_am", offset="1", hour="17", min="0", sec="0", ms="0"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_snap_price = EmrStepSensor(
    task_id="sensor_execute_snap_price",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_snap_price', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_snap_price", "snap_price/snap_price.py", "small", price_group="jkt_0_am", offset="1", hour="17", min="0", sec="0", ms="0")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_leverage_wallet_account_snapshot = EmrAddStepsOperator(
    task_id="execute_leverage_wallet_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_leverage_wallet_account_snapshot", "snap_leverage/leverage_wallet_accounts.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_leverage_wallet_account_snapshot = EmrStepSensor(
    task_id="sensor_execute_leverage_wallet_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_leverage_wallet_account_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_leverage_wallet_account_snapshot", "snap_leverage/leverage_wallet_accounts.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_bappebti_wallets_snapshot = EmrAddStepsOperator(
    task_id="execute_bappebti_wallets_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_bappebti_wallets_snapshot", "bappebti_wallets/snap_bappebti_wallet.py", "medium",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_bappebti_wallets_snapshot = EmrStepSensor(
    task_id="sensor_execute_bappebti_wallets_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_bappebti_wallets_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_bappebti_wallets_snapshot", "bappebti_wallets/snap_bappebti_wallet.py", "medium",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_cashin_snapshot = EmrAddStepsOperator(
    task_id="execute_cashin_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_cashin_snapshot", "cashin/snap_cashin.py", "medium",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_cashin_snapshot = EmrStepSensor(
    task_id="sensor_execute_cashin_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_cashin_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_cashin_snapshot", "cashin/snap_cashin.py", "medium",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_cashouts_snapshot = EmrAddStepsOperator(
    task_id="execute_cashouts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_cashouts_snapshot", "cashout/snap_cashouts.py", "medium",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_cashouts_snapshot = EmrStepSensor(
    task_id="sensor_execute_cashouts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_cashouts_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_cashouts_snapshot", "cashout/snap_cashouts.py", "medium",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_currency_avg_hold_time = EmrAddStepsOperator(
    task_id="execute_crypto_currency_avg_hold_time",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_crypto_currency_avg_hold_time", "crypto_currency/crypto_avg_holding_time.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_currency_avg_hold_time = EmrStepSensor(
    task_id="sensor_execute_crypto_currency_avg_hold_time",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_currency_avg_hold_time', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_crypto_currency_avg_hold_time", "crypto_currency/crypto_avg_holding_time.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_currency_buy_sell_ratio = EmrAddStepsOperator(
    task_id="execute_crypto_currency_buy_sell_ratio",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_crypto_currency_buy_sell_ratio", "crypto_currency/crypto_buy_sell_ratio.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_currency_buy_sell_ratio = EmrStepSensor(
    task_id="sensor_execute_crypto_currency_buy_sell_ratio",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_currency_buy_sell_ratio', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_crypto_currency_buy_sell_ratio", "crypto_currency/crypto_buy_sell_ratio.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_currency_return_snapshot = EmrAddStepsOperator(
    task_id="execute_crypto_currency_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_crypto_currency_return_snapshot", "crypto_currency/snap_crypto_currency_returns.py", "large",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_currency_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_crypto_currency_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_currency_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_crypto_currency_return_snapshot",  "crypto_currency/snap_crypto_currency_returns.py", "large",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_forex_return_snapshot = EmrAddStepsOperator(
    task_id="execute_forex_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_forex_return_snapshot", "forex/snap_forex_returns.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_forex_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_forex_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_forex_return_snapshot", "forex/snap_forex_returns.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_forex_account_snapshot = EmrAddStepsOperator(
    task_id="execute_forex_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_forex_account_snapshot", "forex/snap_forex_accounts.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_forex_account_snapshot = EmrStepSensor(
    task_id="sensor_execute_forex_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_account_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_forex_account_snapshot", "forex/snap_forex_accounts.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_fund_return_snapshot = EmrAddStepsOperator(
    task_id="execute_fund_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_fund_return_snapshot", "fund/snap_fund_returns_usd.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_fund_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_fund_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_fund_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_fund_return_snapshot", "fund/snap_fund_returns_usd.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_avg_hold_time = EmrAddStepsOperator(
    task_id="execute_global_stock_avg_hold_time",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_global_stock_avg_hold_time", "global_stock/global_stock_avg_holding_time.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_avg_hold_time = EmrStepSensor(
    task_id="sensor_execute_global_stock_avg_hold_time",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_avg_hold_time', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_global_stock_avg_hold_time", "global_stock/global_stock_avg_holding_time.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_buy_sell_ratio = EmrAddStepsOperator(
    task_id="execute_global_stock_buy_sell_ratio",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_global_stock_buy_sell_ratio", "global_stock/global_stock_buy_sell_ratio.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_buy_sell_ratio = EmrStepSensor(
    task_id="sensor_execute_global_stock_buy_sell_ratio",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_buy_sell_ratio', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_global_stock_buy_sell_ratio", "global_stock/global_stock_buy_sell_ratio.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_return_snapshot = EmrAddStepsOperator(
    task_id="execute_global_stock_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_global_stock_return_snapshot", "global_stock/snap_global_stock_returns.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_global_stock_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_global_stock_return_snapshot", "global_stock/snap_global_stock_returns.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gold_avg_hold_time = EmrAddStepsOperator(
    task_id="execute_gold_avg_hold_time",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gold_avg_hold_time", "gold/gold_avg_holding_time.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gold_avg_hold_time = EmrStepSensor(
    task_id="sensor_execute_gold_avg_hold_time",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gold_avg_hold_time', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gold_avg_hold_time", "gold/gold_avg_holding_time.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gold_buy_sell_ratio = EmrAddStepsOperator(
    task_id="execute_gold_buy_sell_ratio",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gold_buy_sell_ratio", "gold/gold_buy_sell_ratio.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gold_buy_sell_ratio = EmrStepSensor(
    task_id="sensor_execute_gold_buy_sell_ratio",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gold_buy_sell_ratio', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gold_buy_sell_ratio", "gold/gold_buy_sell_ratio.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gold_return_snapshot = EmrAddStepsOperator(
    task_id="execute_gold_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gold_return_snapshot", "gold/snap_gold_returns.py", "large",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gold_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_gold_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gold_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gold_return_snapshot", "gold/snap_gold_returns.py", "large",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id = aws_conn_id,
    dag=DAG,
)

execute_indo_stock_return_snapshot = EmrAddStepsOperator(
    task_id="execute_indo_stock_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_indo_stock_return_snapshot", "indo_stocks/snap_indo_stock_returns.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_indo_stock_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_indo_stock_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_indo_stock_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_indo_stock_return_snapshot", "indo_stocks/snap_indo_stock_returns.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_pocket_return_snapshot = EmrAddStepsOperator(
    task_id="execute_global_stock_pocket_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_global_stock_pocket_return_snapshot", "pocket_returns/snap_global_stock_pocket_returns.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_pocket_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_global_stock_pocket_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_pocket_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_global_stock_pocket_return_snapshot", "pocket_returns/snap_global_stock_pocket_returns.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_currency_pocket_return_snapshot = EmrAddStepsOperator(
    task_id="execute_crypto_currency_pocket_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_crypto_currency_pocket_return_snapshot", "pocket_returns/snap_crypto_currency_pocket_returns.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_currency_pocket_return_snapshot = EmrStepSensor(
    task_id="sensor_execute_crypto_currency_pocket_return_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_currency_pocket_return_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_crypto_currency_pocket_return_snapshot", "pocket_returns/snap_crypto_currency_pocket_returns.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_intraday_account_snapshot = EmrAddStepsOperator(
    task_id="execute_global_stock_intraday_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_global_stock_intraday_account_snapshot", "global_stock/snap_global_stock_intra_day_accounts.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_intraday_account_snapshot = EmrStepSensor(
    task_id="sensor_execute_global_stock_intraday_account_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_intraday_account_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_global_stock_intraday_account_snapshot", "global_stock/snap_global_stock_intra_day_accounts.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_user_tag_mappings_snapshot = EmrAddStepsOperator(
    task_id="execute_user_tag_mappings_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_user_tag_mappings_snapshot", "user_profile/snap_user_tag_mappings.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_user_tag_mappings_snapshot = EmrStepSensor(
    task_id="sensor_execute_user_tag_mappings_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_user_tag_mappings_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_user_tag_mappings_snapshot", "user_profile/snap_user_tag_mappings.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gss_kyc_information_snapshot = EmrAddStepsOperator(
    task_id="execute_gss_kyc_information_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_snapshot", "user_profile/snap_gss_kyc_information.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gss_kyc_information_snapshot = EmrStepSensor(
    task_id="sensor_execute_gss_kyc_information_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gss_kyc_information_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_snapshot", "user_profile/snap_gss_kyc_information.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_forex_yield_opt_in_details_snapshot = EmrAddStepsOperator(
    task_id="execute_forex_yield_opt_in_details_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_forex_yield_opt_in_details_snapshot", "yield_forex/forex_yield_opt_in_details.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_forex_yield_opt_in_details_snapshot = EmrStepSensor(
    task_id="sensor_execute_forex_yield_opt_in_details_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_forex_yield_opt_in_details_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_forex_yield_opt_in_details_snapshot", "yield_forex/forex_yield_opt_in_details.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gss_kyc_information_state_transitions = EmrAddStepsOperator(
    task_id="execute_gss_kyc_information_state_transitions",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id= aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_state_transitions", "snap_mission_fees_reversal/snap_gss_kyc_state_transaction.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gss_kyc_information_state_transitions = EmrStepSensor(
    task_id="sensor_execute_gss_kyc_information_state_transitions",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gss_kyc_information_state_transitions', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gss_kyc_information_state_transitions", "snap_mission_fees_reversal/snap_gss_kyc_state_transaction.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_gold_gift_and_withdrawal_aggregation = EmrAddStepsOperator(
    task_id="execute_gold_gift_and_withdrawal_aggregation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_gold_gift_and_withdrawal_aggregation", "gold_gift_and_withdrawal/snap_gold_gift_and_withdrawal.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_gold_gift_and_withdrawal_aggregation = EmrStepSensor(
    task_id="sensor_execute_gold_gift_and_withdrawal_aggregation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_gold_gift_and_withdrawal_aggregation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_gold_gift_and_withdrawal_aggregation", "gold_gift_and_withdrawal/snap_gold_gift_and_withdrawal.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_accounts_snapshot = EmrAddStepsOperator(
    task_id="execute_accounts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_accounts_snapshot", "snap_accounts/snap_accounts.py", "medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_accounts_snapshot = EmrStepSensor(
    task_id="sensor_execute_accounts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_accounts_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_accounts_snapshot", "snap_accounts/snap_accounts.py", "medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_portfolio_calculation = EmrAddStepsOperator(
    task_id="execute_portfolio_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_portfolio_calculation", "portfolio/snap_portfolio.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_portfolio_calculation = EmrStepSensor(
    task_id="sensor_execute_portfolio_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_portfolio_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_portfolio_calculation", "portfolio/snap_portfolio.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_indo_stock_dividend = EmrAddStepsOperator(
    task_id="execute_indo_stock_dividend",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_indo_stock_dividend", "indo_stocks/snap_indo_stock_dividend.py", "small"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_indo_stock_dividend = EmrStepSensor(
    task_id="sensor_execute_indo_stock_dividend",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_indo_stock_dividend', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_indo_stock_dividend", "indo_stocks/snap_indo_stock_dividend.py", "small")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_global_stock_options_accounts_snapshot = EmrAddStepsOperator(
    task_id="execute_global_stock_options_accounts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_global_stock_options_accounts_snapshot", "global_stock/snap_global_stock_options_accounts.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_global_stock_options_accounts_snapshot = EmrStepSensor(
    task_id="sensor_execute_global_stock_options_accounts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_global_stock_options_accounts_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_global_stock_options_accounts_snapshot", "global_stock/snap_global_stock_options_accounts.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_crypto_future_accounts_snapshot = EmrAddStepsOperator(
    task_id="execute_crypto_future_accounts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_crypto_future_accounts_snapshot", "crypto_futures/snap_crypto_preps_accounts.py", "small",cut_off_time="0000JKT"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_crypto_future_accounts_snapshot = EmrStepSensor(
    task_id="sensor_execute_crypto_future_accounts_snapshot",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_crypto_future_accounts_snapshot', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_crypto_future_accounts_snapshot", "crypto_futures/snap_crypto_preps_accounts.py", "small",cut_off_time="0000JKT")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)


execute_snap_assets = EmrAddStepsOperator(
    task_id="execute_snap_assets",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_snap_assets", "asset_snapshot/snap_asset.py", "medium"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_snap_assets = EmrStepSensor(
    task_id="sensor_execute_snap_assets",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_snap_assets', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_snap_assets","asset_snapshot/snap_asset.py", "medium")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_daily_intermediate_pnl_calculation = EmrAddStepsOperator(
    task_id="execute_daily_intermediate_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_daily_intermediate_pnl_calculation", "pnl_calculation/daily_aggregate_profit_loss_value.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_daily_intermediate_pnl_calculation = EmrStepSensor(
    task_id="sensor_execute_daily_intermediate_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_daily_intermediate_pnl_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_daily_intermediate_pnl_calculation", "pnl_calculation/daily_aggregate_profit_loss_value.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)


execute_daily_pnl_calculation = EmrAddStepsOperator(
    task_id="execute_daily_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_daily_pnl_calculation", "pnl_calculation/daily_pnl.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_daily_pnl_calculation = EmrStepSensor(
    task_id="sensor_execute_daily_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_daily_pnl_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_daily_pnl_calculation", "pnl_calculation/daily_pnl.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_weekly_pnl_calculation = EmrAddStepsOperator(
    task_id="execute_weekly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_weekly_pnl_calculation", "pnl_calculation/weekly_pnl.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_weekly_pnl_calculation = EmrStepSensor(
    task_id="sensor_execute_weekly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_weekly_pnl_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_weekly_pnl_calculation", "pnl_calculation/weekly_pnl.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_monthly_pnl_calculation = EmrAddStepsOperator(
    task_id="execute_monthly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_monthly_pnl_calculation", "pnl_calculation/monthly_pnl.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_monthly_pnl_calculation = EmrStepSensor(
    task_id="sensor_execute_monthly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_monthly_pnl_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_monthly_pnl_calculation", "pnl_calculation/monthly_pnl.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_quarterly_pnl_calculation = EmrAddStepsOperator(
    task_id="execute_quarterly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_quarterly_pnl_calculation", "pnl_calculation/quarterly_pnl.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_quarterly_pnl_calculation = EmrStepSensor(
    task_id="sensor_execute_quarterly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_quarterly_pnl_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_quarterly_pnl_calculation", "pnl_calculation/quarterly_pnl.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_yearly_pnl_calculation = EmrAddStepsOperator(
    task_id="execute_yearly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_yearly_pnl_calculation", "pnl_calculation/yearly_pnl.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_yearly_pnl_calculation = EmrStepSensor(
    task_id="sensor_execute_yearly_pnl_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_yearly_pnl_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_yearly_pnl_calculation", "pnl_calculation/yearly_pnl.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

execute_invested_value_calculation = EmrAddStepsOperator(
    task_id="execute_invested_value_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_invested_value_calculation", "pluang_plus_member_validity/snap_pluang_plus_member_validity.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_invested_value_calculation = EmrStepSensor(
    task_id="sensor_execute_invested_value_calculation",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_invested_value_calculation', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_invested_value_calculation", "pluang_plus_member_validity/snap_pluang_plus_member_validity.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)


execute_snap_forex_topups_and_cashouts = EmrAddStepsOperator(
    task_id="execute_snap_forex_topups_and_cashouts",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_emr_utils.execute_spark_job("execute_snap_forex_topups_and_cashouts", "forex/snap_forex_topups_and_cashouts.py", "large"),
    params={"config": "config.json"},
    dag=DAG,
)

sensor_execute_snap_forex_topups_and_cashouts = EmrStepSensor(
    task_id="sensor_execute_snap_forex_topups_and_cashouts",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('execute_snap_forex_topups_and_cashouts', key='return_value')["
            + str(len(portfolio_emr_utils.execute_spark_job("execute_snap_forex_topups_and_cashouts", "forex/snap_forex_topups_and_cashouts.py", "large")) - 1)
            + "]}}",
    aws_conn_id=aws_conn_id,
    dag=DAG,
)

portfolio_calculations = DummyOperator(
    task_id='portfolio_calculations',
    dag=DAG
)

pnl_interm_jobs_start = DummyOperator(
    task_id='pnl_interm_jobs_start',
    dag=DAG
)

pnl_calculation_start = DummyOperator(
    task_id='pnl_calculation_start',
    dag=DAG
)

snapshot_job_start = DummyOperator(
    task_id='snapshot_job_start',
    dag=DAG
)

end = DummyOperator(
    task_id='dag_end_asset_returns',
    dag=DAG
)


initial_jobs = [
    kafka_batch_consumer,
    execute_snap_price
]

sensor_initial_jobs = [
    sensor_kafka_batch_consumer,
    sensor_execute_snap_price
]

recurring_order_and_hdfs_copy_jobs = [
    execute_due_recurring_order,
    execute_s3_to_hdfs_copy
]

sensor_recurring_order_and_hdfs_copy_jobs = [
    sensor_execute_due_recurring_order,
    sensor_execute_s3_to_hdfs_copy
]

asset_snapshot_jobs_first_group = [
    execute_gold_return_snapshot,
    execute_forex_return_snapshot,
    execute_indo_stock_return_snapshot,
    execute_global_stock_return_snapshot,
    execute_cashouts_snapshot,
    execute_cashin_snapshot,
    execute_user_tag_mappings_snapshot,
    execute_gss_kyc_information_snapshot,
    execute_forex_account_snapshot,
    execute_global_stock_intraday_account_snapshot,
    execute_snap_assets
]

sensor_asset_snapshot_jobs_first_group = [
    sensor_execute_gold_return_snapshot,
    sensor_execute_forex_return_snapshot,
    sensor_execute_indo_stock_return_snapshot,
    sensor_execute_global_stock_return_snapshot,
    sensor_execute_cashouts_snapshot,
    sensor_execute_cashin_snapshot,
    sensor_execute_user_tag_mappings_snapshot,
    sensor_execute_gss_kyc_information_snapshot,
    sensor_execute_forex_account_snapshot,
    sensor_execute_global_stock_intraday_account_snapshot,
    sensor_execute_snap_assets
]

asset_snapshot_jobs_second_group = [
    execute_crypto_currency_return_snapshot,
    execute_bappebti_wallets_snapshot,
    execute_global_stock_options_accounts_snapshot,
    execute_fund_return_snapshot,
    execute_global_stock_pocket_return_snapshot,
    execute_leverage_wallet_account_snapshot,
    execute_crypto_future_accounts_snapshot,
    execute_crypto_currency_pocket_return_snapshot,
    execute_gold_gift_and_withdrawal_aggregation,
    execute_accounts_snapshot,
    execute_snap_forex_topups_and_cashouts
]

sensor_asset_snapshot_jobs_second_group = [
    sensor_execute_crypto_currency_return_snapshot,
    sensor_execute_bappebti_wallets_snapshot,
    sensor_execute_global_stock_options_accounts_snapshot,
    sensor_execute_fund_return_snapshot,
    sensor_execute_global_stock_pocket_return_snapshot,
    sensor_execute_leverage_wallet_account_snapshot,
    sensor_execute_crypto_future_accounts_snapshot,
    sensor_execute_crypto_currency_pocket_return_snapshot,
    sensor_execute_gold_gift_and_withdrawal_aggregation,
    sensor_execute_accounts_snapshot,
    sensor_execute_snap_forex_topups_and_cashouts
]

portfolio_calculation_jobs = [
    execute_portfolio_calculation,
    execute_indo_stock_dividend,
    execute_invested_value_calculation,
    execute_gold_avg_hold_time,
    execute_gold_buy_sell_ratio,
    execute_crypto_currency_avg_hold_time,
    execute_gss_kyc_information_state_transitions
]

sensor_portfolio_calculation_jobs = [
    sensor_execute_portfolio_calculation,
    sensor_execute_indo_stock_dividend,
    sensor_execute_invested_value_calculation,
    sensor_execute_gold_avg_hold_time,
    sensor_execute_gold_buy_sell_ratio,
    sensor_execute_crypto_currency_avg_hold_time,
    sensor_execute_gss_kyc_information_state_transitions
]

pnl_interm_jobs = [
    execute_daily_intermediate_pnl_calculation,
    execute_crypto_currency_buy_sell_ratio,
    execute_global_stock_avg_hold_time,
    execute_global_stock_buy_sell_ratio,
    execute_forex_yield_opt_in_details_snapshot
]

sensor_pnl_interm_jobs = [
    sensor_execute_daily_intermediate_pnl_calculation,
    sensor_execute_crypto_currency_buy_sell_ratio,
    sensor_execute_global_stock_avg_hold_time,
    sensor_execute_global_stock_buy_sell_ratio,
    sensor_execute_forex_yield_opt_in_details_snapshot
]

pnl_jobs = [
    execute_daily_pnl_calculation,
    execute_weekly_pnl_calculation,
    execute_monthly_pnl_calculation,
    execute_quarterly_pnl_calculation,
    execute_yearly_pnl_calculation
]

sensor_pnl_jobs = [
    sensor_execute_daily_pnl_calculation,
    sensor_execute_weekly_pnl_calculation,
    sensor_execute_monthly_pnl_calculation,
    sensor_execute_quarterly_pnl_calculation,
    sensor_execute_yearly_pnl_calculation
]


chain(
    sensor_Setup_Hadoop_Debugging,
    initial_jobs,
    sensor_initial_jobs,
    execute_kafka_msg_validation,
    sensor_execute_kafka_msg_validation,
    recurring_order_and_hdfs_copy_jobs,
    sensor_recurring_order_and_hdfs_copy_jobs,
    snapshot_job_start,
    asset_snapshot_jobs_first_group,
    sensor_asset_snapshot_jobs_first_group,
    asset_snapshot_jobs_second_group,
    sensor_asset_snapshot_jobs_second_group,
    portfolio_calculations,
    portfolio_calculation_jobs,
    sensor_portfolio_calculation_jobs,
    pnl_interm_jobs_start,
    pnl_interm_jobs,
    sensor_pnl_interm_jobs,
    pnl_calculation_start,
    pnl_jobs,
    sensor_pnl_jobs,
    end
)