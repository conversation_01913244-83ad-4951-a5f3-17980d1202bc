import pendulum
from airflow.models import Variable
from airflow.models import XCom
from airflow.operators.dummy import DummyOperator
from airflow.utils.db import provide_session
from airflow_kubernetes_job_operator.kubernetes_job_operator import KubernetesJobOperator
from datetime import <PERSON><PERSON><PERSON>
import os, sys
from alerting.slack_alert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from alerting.dag_alert import Dag<PERSON>lert
from base_dags.base_dag import BASE_DAG

ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)


USERNAME = "airflow"
ENV = Variable.get("ENV")
job_config = Variable.get("pluang-bq-asset-analyser-env-var", deserialize_json=True)
IMAGE = job_config["image"]
SLACK_CONN_ID = "slack_default"
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)

dag_alert = DagAlert(USERNAME, SLACK_CONN_ID, OPSGENIE_CONN_ID)


@provide_session
def clean_xcom(session=None, **context):
    """Clean XComs for the current DAG

    Args:
        session (Session, optional): [description]. Defaults to None.
        context ([type]): [description]
    """
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 2,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": lambda context: dag_alert.all_channel_alert_failure(context, priority="P2", is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"]),
}

DAG_ID = "PLUANG_BQ_ASSET_ANALYSER"
bq_asset_dag = BASE_DAG(
    dag_id=DAG_ID,
    default_args=DEFAULT_ARGS,
    schedule_interval="0 0 * * *",
    catchup=False,
    tags=["data-eng", "bq-asset-analyser"],
    team="data-eng"
)
DAG = bq_asset_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=1),
    max_active_runs=1
)
globals()[DAG_ID] = DAG
start = DummyOperator(task_id="start", dag=globals()[DAG_ID])

bq_asset_analyser = KubernetesJobOperator(
    task_id="pluang_bq_asset_analyser",
    namespace=NAME,
    image=IMAGE,
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy="Always",
    command=[],
    get_logs=True,
    dag=globals()[DAG_ID],
)

start >> bq_asset_analyser
