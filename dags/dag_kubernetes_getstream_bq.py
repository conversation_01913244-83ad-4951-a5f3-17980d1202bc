import pytz, pendulum
from datetime import timedelta
from airflow.utils.helpers import chain
from base_dags.base_dag import BASE_DAG
from airflow.models import XCom
from airflow.models import Variable
from airflow.utils.db import provide_session
from airflow.hooks.base_hook import BaseHook
from kubernetes.client import models as k8s
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow_kubernetes_job_operator.kubernetes_job_operator import \
    KubernetesJobOperator  # https://github.com/LamaAni/KubernetesJobOperator

USERNAME = 'airflow'
SLACK_CONN_ID = 'slack_default'
ENV = Variable.get("ENV")
IMAGE = Variable.get("ecr_image_get_stream_bq")
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])


def slack_alert(context):
    """
    Slack Alert
    """
    slack_webhook_token = BaseHook.get_connection(SLACK_CONN_ID).password
    slack_msg = """
            :red_circle: Task Failed.
            *Task*: {task}
            *Dag*: {dag}
            *Execution Time*: {exec_date}
            *Log Url*: {log_url}
            """.format(
        task=context.get('task_instance').task_id,
        dag=context.get('task_instance').dag_id,
        ti=context.get('task_instance'),
        exec_date=context.get('execution_date'),
        log_url=context.get('task_instance').log_url,
    )
    failed_alert = SlackWebhookOperator(
        task_id='slack_alert',
        http_conn_id=SLACK_CONN_ID,
        webhook_token=slack_webhook_token,
        message=slack_msg,
        username=USERNAME)
    return failed_alert.execute(context=context)


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 0,
    "retry_delay": timedelta(minutes=1),
    "on_failure_callback": slack_alert,
}
dag_id = 'Get-Stream-To-Bq'
GetStream = BASE_DAG(
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    schedule_interval="00 17 * * *",  ## 05:00pm UTC <-> (10:30 pm IST)
    catchup=False,
    tags=["data-eng", "get_stream_to_bq", "BQ"],
    team="data-eng"
)
DAG = GetStream.Create_Dag(
    dagrun_timeout=timedelta(hours=1),
    max_active_runs=1
)
globals()[dag_id] = DAG
########################################################################################################################
start = DummyOperator(
    task_id='start',
    dag=globals()[dag_id]
)
get_stream_package = KubernetesJobOperator(
    task_id="get-stream-bigquery",
    namespace=NAME,
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy='Always',
    get_logs=True,
    dag=globals()[dag_id]
)

end = DummyOperator(
    task_id='End',
    dag=globals()[dag_id]
)
chain(
    start,
    get_stream_package,
    end
)
