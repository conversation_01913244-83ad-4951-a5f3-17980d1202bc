import pendulum
from airflow.utils.db import provide_session
from airflow.models import Variable, XCom
from airflow.operators.python import PythonOperator
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from datetime import timed<PERSON><PERSON>

from alerting.slack_alert import Slack<PERSON><PERSON>t
from base_dags.base_dag import BASE_DAG

# Constants
USERNAME = "airflow"
VAR_CUSTOMER_RISK_RATING = "customer_risk_rating"
VAR_DEFAULT_ARGS = "default_args"
VAR_MWAA_NAMESPACE = "MWAA_NAMESPACE"
VAR_KUBE_CONFIG_PATH = "kube_config_path"
VAR_CLUSTER_CONTEXT = "cluster_context"
SLACK_CONN_ID = "customer_risk_rating_slack"

# DAG ID
DAG_ID = "Customer-Risk-Rating-Producer"

# Task IDs
TASK_ID_PRODUCER = "Customer-Risk-Rating-Producer"
TASK_ID_DELETE_XCOM = "delete_xcom"

# Fetch Variables
customer_risk_rating_dict = Variable.get(VAR_CUSTOMER_RISK_RATING, deserialize_json=True)
IMAGE = customer_risk_rating_dict.get("image")
slack_host_hook = customer_risk_rating_dict.get("slack_hook")
NAME = Variable.get(VAR_MWAA_NAMESPACE)
KUBE_CONFIG_PATH = Variable.get(VAR_KUBE_CONFIG_PATH)
CLUSTER_CONTEXT_EKS = Variable.get(VAR_CLUSTER_CONTEXT)

# Extract start_date
start_date_str = Variable.get(VAR_DEFAULT_ARGS, deserialize_json=True).get("start_date")
year, month, day = map(int, start_date_str.split(","))

# Initialize Slack Alert
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)

@provide_session
def clean_xcom(session=None, **context):
    """Delete XCom data for the given dag_ids"""
    dag_id = context["dag"].dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()

# Default arguments for the DAG
DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 0,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert.slack_alert_failure,
}

# Create Base DAG
customer_risk_rating_base_dag = BASE_DAG(
    dag_id=DAG_ID,
    default_args=DEFAULT_ARGS,
    schedule_interval='*/30 * 1 * *',  # Runs every 30 minutes only on the 1st day of the month
    catchup=False,
    tags=["data-eng", "CRR", "kafka", "bq"],
    team="data-eng",
)

# Define the DAG
with customer_risk_rating_base_dag.Create_Dag(
    dagrun_timeout=timedelta(minutes=30),
    max_active_runs=1,
    slack_conn_details=(SLACK_CONN_ID, slack_host_hook),
    params={"execution_date_offset": None}
) as dag:

    # Kubernetes Pod Operator Task
    customer_risk_rating_producer_task = KubernetesPodOperator(
        name=TASK_ID_PRODUCER,
        image=IMAGE,
        config_file=KUBE_CONFIG_PATH,
        task_id=TASK_ID_PRODUCER,
        cluster_context=CLUSTER_CONTEXT_EKS,
        in_cluster=False,
        namespace=NAME,
        execution_timeout=timedelta(minutes=28),
        on_failure_callback=slack_alert.slack_alert_failure,
        on_success_callback=slack_alert.slack_alert_success,
        cmds=["python3", "-m", "CustomerRiskRating.main", "-o", "{{ params.execution_date_offset}}"],
        get_logs=True,
        is_delete_operator_pod=True,
        image_pull_policy="Always"
    )

    # Python Operator Task to clean XCom
    delete_xcom = PythonOperator(
        task_id=TASK_ID_DELETE_XCOM, 
        python_callable=clean_xcom,
    )

    # Task Dependencies
    customer_risk_rating_producer_task >> delete_xcom

# Register the DAG
globals()[DAG_ID] = dag
