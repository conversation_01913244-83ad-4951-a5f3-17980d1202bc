import datetime
import pendulum
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from airflow.models import XCom
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.utils.db import provide_session
from airflow.utils.helpers import chain
from airflow_kubernetes_job_operator.kubernetes_job_operator import \
    KubernetesJobOperator  # https://github.com/LamaAni/KubernetesJobOperator
from datetime import timedelta
from base_dags.base_dag import BASE_DAG
from alerting.slack_alert import SlackAlert

USERNAME = 'airflow'
ENV = Variable.get("ENV")
forex_rates_daily_dict = Variable.get("forex_rates_daily", deserialize_json=True)
IMAGE = forex_rates_daily_dict.get("image")
slack_host_hook = forex_rates_daily_dict.get("slack_hook")
data_studio_url = forex_rates_daily_dict.get("data_studio_url")
SLACK_CONN_ID = "forex_rates_daily_slack_alert"
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)


def slack_fx_rates_report(**context):
    try:
        slack_msg = """
        Hi All
        Please kindly check *FX rates daily* tracker As per *{date}*
        Data-Studio URL: {data_studio_url}
        Thank You
        """.format(
            date=str(datetime.date.today()),
            data_studio_url=data_studio_url
        )

        slack_alert.set_slack_alert(context,
                                    task_id="slack_fx_daily",
                                    msg=slack_msg)
    except Exception as ex:
        raise Exception(f"{slack_voucher_report.__name__}: {ex}")


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 2,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert.slack_alert_failure
}

dag_id = 'RPA-Forex-Prices-Daily'
forex_rates_base_dag = BASE_DAG(
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    schedule_interval="45 02 * * *",  # 02-45am UTC <-> (09:45 am Jakarta) <-> (08:15 am IST)
    catchup=False,
    tags=["data-eng", "rpa", "forex_rates_daily", "bq"],
    team="data-eng"
)
DAG = forex_rates_base_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=1),
    max_active_runs=1,
    slack_conn_details=(SLACK_CONN_ID, slack_host_hook)
)
globals()[dag_id] = DAG
########################################################################################################################
start = DummyOperator(
    task_id='start',
    dag=globals()[dag_id]
)

forex_prices_task = KubernetesJobOperator(
    task_id="Forex-Prices-job",
    namespace=NAME,
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy='Always',
    get_logs=True,
    dag=globals()[dag_id]
)

slack_fx_rates_report_task = PythonOperator(
    task_id="Slack-FX-Rates-Report",
    python_callable=slack_fx_rates_report,
    provide_context=True,
    dag=globals()[dag_id]
)

end = DummyOperator(
    task_id='End',
    dag=globals()[dag_id],

)
chain(
    start,
    forex_prices_task,
    slack_fx_rates_report_task,
    end
)
