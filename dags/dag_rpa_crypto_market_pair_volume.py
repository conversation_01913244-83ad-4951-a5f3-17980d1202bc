# pylint: disable=eval-used
"""DAG for Crypto Market Pairs Volume"""

from datetime import timedelta
import pendulum
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.dummy import DummyOperator
from airflow_kubernetes_job_operator.kubernetes_job_operator import (
    KubernetesJobOperator,
)  # https://github.com/LamaAni/KubernetesJobOperator

from alerting.slack_alert import SlackAlert
from base_dags.base_dag import BASE_DAG

USERNAME = "airflow"
ENV = Variable.get("ENV")
crypto_tracker_dict = Variable.get("crypto_market_pair_volume", deserialize_json=True)
IMAGE = crypto_tracker_dict.get("image")
slack_host_hook = crypto_tracker_dict.get("slack_hook")
SLACK_CONN_ID = "crypto_market_pair_volume_slack_alert"
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)

DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 0,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert.slack_alert_failure,
}

DAG_ID = "Crypto-Market-Pairs-Volume"
crypto_market_pairs_volume_dag = BASE_DAG(
    dag_id=DAG_ID,
    default_args=DEFAULT_ARGS,
    schedule_interval="00 01 * * *",  # 01:00am UTC <-> 8:00 am JKT <-> 06:30 am IST,
    catchup=False,
    tags=["data-eng", "rpa", "crypto_volume", "kubernetes"],
    team="data-eng",
)
DAG = crypto_market_pairs_volume_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=2),
    max_active_runs=1,
    slack_conn_details=(SLACK_CONN_ID, slack_host_hook),
)
globals()[DAG_ID] = DAG
dag = globals()[DAG_ID]
########################################################################################################################
start = DummyOperator(task_id="start", dag=dag)
crypto_market_pairs_volume_task = KubernetesJobOperator(
    task_id="crypto_market_pairs_volume-task",
    namespace=NAME,
    image=IMAGE,
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy="Always",
    execution_timeout=timedelta(hours=1.3),
    command=[
        "python3",
        "-m",
        "CryptoTracker.crypto_market_pair_volume_main"
    ],
    get_logs=True,
    on_success_callback=slack_alert.slack_alert_success,
    dag=dag,
)

cmc_trending_coins_list_task = KubernetesJobOperator(
    task_id="cmc_trending_coins_list_task",
    namespace=NAME,
    image=IMAGE,
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy="Always",
    execution_timeout=timedelta(hours=1),
    command=[
        "python3",
        "-m",
        "CryptoTracker.cmc_trending_coins_list_main"
    ],
    get_logs=True,
    on_success_callback=slack_alert.slack_alert_success,
    dag=dag,
)

end = DummyOperator(
    task_id="End",
    dag=dag,
)
chain(start, crypto_market_pairs_volume_task, cmc_trending_coins_list_task, end)