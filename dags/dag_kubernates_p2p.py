from datetime import timed<PERSON><PERSON>
import pendulum
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from airflow.models import XCom
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.utils.db import provide_session
from airflow.utils.helpers import chain
from airflow_kubernetes_job_operator.kubernetes_job_operator import (
    KubernetesJobOperator,
)  # https://github.com/LamaAni/KubernetesJobOperator

from alerting.slack_alert import SlackAlert
from base_dags.base_dag import BASE_DAG

USERNAME = "airflow"
ENV = Variable.get("ENV")

p2p_report_dict = Variable.get("p2p_report", deserialize_json=True)
IMAGE = p2p_report_dict.get("image")
slack_host_hook = p2p_report_dict.get("slack_hook")
SLACK_CONN_ID = "p2p_report_slack_alert"
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])


@provide_session
def clean_xcom(session=None, **context):
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 3,
    "retry_delay": timedelta(minutes=7),
    "on_failure_callback":slack_alert.slack_alert_failure,
}

dag_id = "RPA-Planergy-to-Pluang-Report"
p2p = BASE_DAG(
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    schedule_interval="0 1 * * *",  # 8AM JKT
    catchup=False,
    tags=["data-eng", "rpa", "p2p", "kubernetes"],
    team="data-eng",
)
DAG = p2p.Create_Dag(
    dagrun_timeout=timedelta(hours=2),
    max_active_runs=1,
    slack_conn_details=(SLACK_CONN_ID, slack_host_hook),
)
globals()[dag_id] = DAG
########################################################################################################################
start = DummyOperator(task_id="start", dag=globals()[dag_id])

p2p_report_task = KubernetesJobOperator(
    task_id="p2p-report-job",
    namespace=NAME,
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy="Always",
    get_logs=True,
    on_success_callback=slack_alert.slack_alert_success,
    execution_timeout=timedelta(hours=1),
    dag=globals()[dag_id],
)

end = DummyOperator(
    task_id="End",
    dag=globals()[dag_id],
)
chain(start, p2p_report_task, end)
