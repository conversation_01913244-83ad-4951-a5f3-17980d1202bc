import sys, os
from datetime import timedelta
from airflow.models import Variable
from airflow.utils.helpers import chain
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from base_dags.base_dag import BASE_DAG


S3ToBQTables = Variable.get("s3_to_bq_tables", deserialize_json=True)
ROOT_DIR = os.path.dirname(os.path.abspath('__file__'))
Pipeline_Utils_Dir_Path = os.path.join(ROOT_DIR, 'dags/pipeline_utils')
sys.path.append(Pipeline_Utils_Dir_Path)
from pipeline_utils.s3_to_bq import run_s3_to_bq

dag_id = 'S3-TO-BQ'

BaseDag_s3_To_Bq = BASE_DAG(
    dag_id=dag_id,
    default_args=run_s3_to_bq.DEFAULT_ARGS,
    schedule_interval="00 23 * * *", # IST 4:30,
    catchup=False,
    tags=["data-eng", "kafka", "s3", "kafka_to_s3"],
    team="data-eng"
)

S3_To_Bq_DAG = BaseDag_s3_To_Bq.Create_Dag(
    dagrun_timeout=timedelta(minutes=50),
    max_active_runs=1,
)

globals()[dag_id] = S3_To_Bq_DAG

start = DummyOperator(
    task_id='Start',
    dag=globals()[dag_id]
)

run_s3_to_bq_ops = PythonOperator(task_id=f'Run-S3-To-BQ',
                                  python_callable=run_s3_to_bq.main,
                                  op_args=[{'bq_tables_populate': S3ToBQTables}],
                                  execution_timeout= timedelta(minutes=30),
                                  dag=globals()[dag_id],
                                  provide_context=True
                                )

delete_xcom = PythonOperator(
    task_id="s3_to_bq_clean_xcom",
    python_callable=run_s3_to_bq.clean_xcom,
    dag=globals()[dag_id],
    provide_context=True
)

end = DummyOperator(
    task_id='End',
    dag=globals()[dag_id]
)

chain(start,
      run_s3_to_bq_ops,
      delete_xcom,
      end
    )