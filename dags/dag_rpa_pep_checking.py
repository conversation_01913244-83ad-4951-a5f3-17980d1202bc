import pendulum
from airflow.utils.db import provide_session
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from airflow.models import XCom
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Python<PERSON>perator
from airflow.utils.helpers import chain
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
from datetime import date
from datetime import datetime
from datetime import timedelta

from alerting.slack_alert import SlackAlert
from base_dags.base_dag import BASE_DAG

USERNAME = "airflow"
ENV = Variable.get("ENV")
pep_checking_dict = Variable.get("pep_checking", deserialize_json=True)
IMAGE = pep_checking_dict.get("image")
slack_host_hook = pep_checking_dict.get("slack_hook")
pep_checking_day_offset = pep_checking_dict.get("day_offset")
SLACK_CONN_ID = "pep_checking_slack_alert"
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)

@provide_session
def clean_xcom(session=None, **context):
    """ Delete XCom data for the given dag_ids """
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()

DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 2,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert.slack_alert_failure,
}

dag_id = "RPA-PEP-Checking-Automation"
pep_checking_base_dag = BASE_DAG(
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    schedule_interval="00 03 * * 1-5",  # 03-00am UTC <-> (10:00 am Jakarta) <-> (08:30 am IST) MONDAY TO FRIDAY
    catchup=False,
    tags=["data-eng", "rpa", "PEP", "bq"],
    team="data-eng",
)
DAG = pep_checking_base_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=10.1),
    max_active_runs=1,
    slack_conn_details=(SLACK_CONN_ID, slack_host_hook),
    params={"backfill_from": None}
)
globals()[dag_id] = DAG
########################################################################################################################
start = DummyOperator(task_id="start", dag=globals()[dag_id])

pep_checking_producer_task = KubernetesPodOperator(
    name="PEP-Checking-Producer",
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    task_id="PEP-Checking-Producer",
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    namespace=NAME,
    execution_timeout=timedelta(hours=5),
    on_failure_callback=slack_alert.slack_alert_failure,
    on_success_callback=slack_alert.slack_alert_success,
    cmds=["python3",
            "-m",
            "PEPChecking.producer.main",
            "--day_offset",
            f"{pep_checking_day_offset}",
            "--backfill_from",
            "{{ params.backfill_from}}"
    ],
    get_logs=True,
    is_delete_operator_pod=True,
    dag=globals()[dag_id]
)

pep_checking_consumer_task = KubernetesPodOperator(
    name="PEP-Checking-Consumer",
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    task_id="PEP-Checking-Consumer",
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    namespace=NAME,
    execution_timeout=timedelta(hours=10),
    on_failure_callback=slack_alert.slack_alert_failure,
    on_success_callback=slack_alert.slack_alert_success,
    cmds=["python3", "-m", "PEPChecking.consumer.main"],
    get_logs=True,
    is_delete_operator_pod=True,
    dag=globals()[dag_id]
)

delete_xcom = PythonOperator(
    task_id="delete_xcom", 
    python_callable=clean_xcom,
    dag=globals()[dag_id]
    )

end = DummyOperator(task_id="End", dag=globals()[dag_id])

chain(start, pep_checking_producer_task, pep_checking_consumer_task, delete_xcom, end)
