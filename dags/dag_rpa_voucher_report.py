from datetime import timed<PERSON><PERSON>
import pendulum
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import BaseHook
from airflow.models import Variable
from airflow.models import XCom
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.utils.db import provide_session
from airflow.utils.helpers import chain
from pipeline_utils.big_query.big_query import BigQuery
from base_dags.base_dag import BASE_DAG
from alerting.slack_alert import SlackAlert

USERNAME = "airflow"
ENV = Variable.get("ENV")
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
bq_service_account_json = Variable.get("de_bq_secret", deserialize_json=True)
rpa_voucher_report_dict = Variable.get("rpa_voucher_report", deserialize_json=True)
SLACK_CONN_ID = "rpa_voucher_report"
slack_host_hook = rpa_voucher_report_dict.get("slack_hook")
sheet_url = rpa_voucher_report_dict.get("sheet_url")
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)

@provide_session
def clean_xcom(session=None, **context):
    """Clean xcom for this dag
    Args:
        session (Session, optional): airflow session. Defaults to None.
    """
    dag_from_context = context["dag"]
    dag_id = dag_from_context._dag_id  # pylint: disable=protected-access
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()


def get_voucher_data():
    try:
        bq = BigQuery(service_account_file=bq_service_account_json)
        project = rpa_voucher_report_dict.get("project")
        table_vouchers_purchase_orders = rpa_voucher_report_dict.get("table_vouchers_purchase_orders")
        table_vouchers_purchase_order_denominations = rpa_voucher_report_dict.get(
            "table_vouchers_purchase_order_denominations")
        table_pluang_users_merchants = rpa_voucher_report_dict.get("table_pluang_users_merchants")
        query_str = f"""
                SELECT 
                case when m.name = 'PLUANG Operation' then 'Pluang Operation' else m.name end name,
                sum(d.denomination*d.quantity) total_amount
                FROM `{project}.{table_vouchers_purchase_orders}` po
                left join `{project}.{table_vouchers_purchase_order_denominations}` d
                    on po.id =d.purchase_order_id
                left join `{project}.{table_pluang_users_merchants}` m
                    on po.merchant_id =m.id
                where date(datetime(po.created,'Asia/Jakarta'))between date_trunc(current_date('Asia/Jakarta')-1, month) and current_date('Asia/Jakarta')-1
                and ( m.name <> 'LAZADA'
                  and m.name <> 'BLIBLI2')
                and po.status = 'PROCESSED'
                group by 1
                order by 1 desc
                """
        job = bq.run_and_get_content(query_str)
        records = []
        for name, total_amount in job.result():
            records.append([str(name), int(total_amount)])
        return records
    except Exception as ex:
        raise Exception(f"{get_voucher_data.__name__}: {ex}")


def slack_voucher_report(**context):
    try:
        result = context['ti'].xcom_pull(task_ids="Get-Voucher-Data")
        operation_price = result[0][-1] if len(result)>=1 else 0
        operation_price = "{:,}".format(operation_price)
        marketing_price = result[1][-1] if len(result)>1 else 0
        marketing_price = "{:,}".format(marketing_price)
        slack_msg = """
        Hey All, This is a *Voucher Report* on MTD
        Voucher Operations Rp *{operation_price}*
        Voucher Marketing Rp *{marketing_price}*
        For details, you can check here {sheet_url}
        """.format(
            operation_price = operation_price,
            marketing_price = marketing_price,
            sheet_url = sheet_url
        )
        slack_alert.set_slack_alert(context,
                                    task_id="slack_voucher_report",
                                    msg=slack_msg)
    except Exception as ex:
        raise Exception(f"{slack_voucher_report.__name__}: {ex}")


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 3,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert.slack_alert_failure
}

dag_id = 'RPA-Voucher-Report'
voucher_report = BASE_DAG(
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    schedule_interval="00 04 * * *",  # 04-00am UTC <-> (11:00 am Jakarta) <-> (09:30 am IST)
    catchup=False,
    tags=[
         "data-eng",
         "rpa",
         "voucher_report"
    ],
    team="data-eng"
)
DAG = voucher_report.Create_Dag(
    dagrun_timeout=timedelta(hours=1),
    max_active_runs=1,
    slack_conn_details=(SLACK_CONN_ID, slack_host_hook)
)
globals()[dag_id] = DAG
########################################################################################################################
start = DummyOperator(
    task_id='start',
    dag=globals()[dag_id]
)

voucher_data_task = PythonOperator(
    task_id="Get-Voucher-Data",
    python_callable=get_voucher_data,
    provide_context=True,
    dag=globals()[dag_id]
)

slack_voucher_report_task = PythonOperator(
    task_id="Slack-Voucher-Report",
    python_callable=slack_voucher_report,
    provide_context=True,
    dag=globals()[dag_id]
)

delete_xcom = PythonOperator(
    task_id="delete-xcom", python_callable=clean_xcom, dag=globals()[dag_id]
)

end = DummyOperator(
    task_id='End',
    dag=globals()[dag_id],

)
chain(
    start,
    voucher_data_task,
    slack_voucher_report_task,
    delete_xcom,
    end
)
