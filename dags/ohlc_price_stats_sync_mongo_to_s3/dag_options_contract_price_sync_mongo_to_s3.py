import pendulum
import logging
import os, sys
from datetime import timedelta
from airflow.utils.helpers import chain
from airflow.models import XCom
from airflow.utils.db import provide_session
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
pipeline_utils_path = os.path.join(ROOT_DIR, "dags/pipeline_utils")
sys.path.append(pipeline_utils_path)
from pipeline_utils.s3_to_bq import run_s3_to_bq
from alerting.dag_alert import Dag<PERSON>lert
from base_dags.base_dag import BASE_DAG
from airflow.models import Variable
from airflow_kubernetes_job_operator.kubernetes_job_operator import KubernetesJobOperator

env = Variable.get("ENV")

sync_config = Variable.get("ohlc_price_stats_sync_mongo_to_s3", deserialize_json=True)
s3_to_bq_tables_to_sync = Variable.get("s3_to_bq_sync_config", deserialize_json=True).get("ohlc_price_stats_sync_mongo_to_s3")

KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
IMAGE = sync_config.get("image")
NAMESPACE = Variable.get("MWAA_NAMESPACE")
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
USERNAME = "airflow"
SLACK_CONN_ID = "slack_default"
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]

dag_alert = DagAlert(USERNAME, SLACK_CONN_ID, OPSGENIE_CONN_ID)


@provide_session
def clean_xcom(session=None, **context):
    """Clean XComs for the current DAG

    Args:
        session (Session, optional): [description]. Defaults to None.
        context ([type]): [description]
    """
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()



DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "on_failure_callback": lambda context: dag_alert.slack_alert_failure(context)
}


DAG_ID = "Options-Contract-Price-Stats-Sync-Mongo-To-S3"


base_dag = BASE_DAG(
    dag_id=DAG_ID,
    schedule_interval="0 0,17 * * *",
    default_args=DEFAULT_ARGS,
    catchup=False,
    tags=["data-eng", "options-contract-price-sync", "mongo", "s3", "kubernetes"],
    team="data-eng"
)


options_contract_price_stats_sync_mongo_to_s3_dag = base_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=1),
    max_active_runs=1
)

globals()[DAG_ID] = options_contract_price_stats_sync_mongo_to_s3_dag
dag = globals()[DAG_ID]


options_contract_price_stats_sync_mongo_to_s3_operator = KubernetesJobOperator(
    task_id="options_contract_price_stats_sync_mongo_to_s3",
    namespace=NAMESPACE,
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy='Always',
    get_logs=True,
    arguments=["--job_type", "options_price"],
    dag=globals()[DAG_ID],
)

run_s3_to_bq = PythonOperator(
    task_id="run_s3_to_bq_sync",
    python_callable=run_s3_to_bq.main,
    op_args=[{"bq_tables_populate": s3_to_bq_tables_to_sync}],
    execution_timeout=timedelta(minutes=30),
    dag=globals()[DAG_ID],
    provide_context=True
)

delete_xcom = PythonOperator(
    task_id="delete_xcom",
    python_callable=clean_xcom,
    dag=globals()[DAG_ID]
)

start = DummyOperator(task_id="Start", dag=dag)

end = DummyOperator(task_id="End", dag=dag)

chain(
    start,
    options_contract_price_stats_sync_mongo_to_s3_operator,
    run_s3_to_bq,
    delete_xcom,
    end
)
