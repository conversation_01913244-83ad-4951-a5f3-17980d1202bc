import pendulum
import logging
import os, sys
from datetime import timedelta
from airflow.utils.helpers import chain
from airflow.operators.dummy_operator import DummyOperator
ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
base_dags_path = os.path.join(ROOT_DIR, "dags/base_dags")
sys.path.append(base_dags_path)
from alerting.dag_alert import DagAlert
from base_dags.base_dag import BASE_DAG
from airflow.models import Variable
from airflow_kubernetes_job_operator.kubernetes_job_operator import KubernetesJobOperator

env = Variable.get("ENV")

sync_config = Variable.get("ohlc_price_stats_sync_mongo_to_s3", deserialize_json=True)
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
IMAGE = sync_config.get("image")
NAMESPACE = Variable.get("MWAA_NAMESPACE")
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
USERNAME = "airflow"
SLACK_CONN_ID = "slack_default"
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]

dag_alert = DagAlert(USERNAME, SLACK_CONN_ID, OPSGENIE_CONN_ID)


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "on_failure_callback": lambda context: dag_alert.slack_alert_failure(context)
}


DAG_ID = "ohlc-price-stats-sync-mongo-to-s3"


base_dag = BASE_DAG(
    dag_id=DAG_ID,
    schedule_interval="05 00 * * *",
    default_args=DEFAULT_ARGS,
    catchup=False,
    tags=["data-eng", "ohlc-price-sync", "mongo", "s3", "kubernetes"],
    team="data-eng"
)


ohlc_price_stats_sync_mongo_to_s3_dag = base_dag.Create_Dag(
    dagrun_timeout=timedelta(hours=4),
    max_active_runs=1
)

globals()[DAG_ID] = ohlc_price_stats_sync_mongo_to_s3_dag
dag = globals()[DAG_ID]


ohlc_price_stats_sync_mongo_to_s3_operator = KubernetesJobOperator(
    task_id="ohlc_price_stats_sync_mongo_to_s3",
    namespace=NAMESPACE,
    image=IMAGE,
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    delete_policy='Always',
    get_logs=True,
    dag=globals()[DAG_ID],
)

start = DummyOperator(task_id="Start", dag=dag)

end = DummyOperator(task_id="End", dag=dag)

chain(
    start,
    ohlc_price_stats_sync_mongo_to_s3_operator,
    end,
)
