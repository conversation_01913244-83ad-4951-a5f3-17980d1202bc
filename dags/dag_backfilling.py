import os, sys
from datetime import timedelta,datetime
from base_dags.base_dag import BASE_DAG
from airflow.models import Variable
from airflow.utils.helpers import chain
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import <PERSON>Hook
from datetime import timedelta
import pendulum
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.providers.amazon.aws.operators.emr_terminate_job_flow import EmrTerminateJobFlowOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
ROOT_DIR = os.path.dirname(os.path.abspath('__file__'))
Pipeline_Utils_Dir_Path = os.path.join(ROOT_DIR, 'dags/pipeline_utils')
sys.path.append(Pipeline_Utils_Dir_Path)
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
import dateutil.tz
from pipeline_utils.backfilling_utils import BackfillingUtils
from pipeline_utils.backfilling_utils import clean_xcom

#### SLACK ALERT ####

def slack_alert(context):
    """
    Slack Alert
    """
    slack_webhook_token = BaseHook.get_connection(slack_conn_id).password
    slack_msg = """
            :red_circle: Task Failed. 
            *Task*: {task}  
            *Dag*: {dag} 
            *Execution Time*: {exec_date}  
            *Log Url*: {log_url} 
            """.format(
        task=context.get('task_instance').task_id,
        dag=context.get('task_instance').dag_id,
        ti=context.get('task_instance'),
        exec_date=context.get('execution_date'),
        log_url=context.get('task_instance').log_url,
    )

    failed_alert = SlackWebhookOperator(
        task_id='slack_alert',
        http_conn_id=slack_conn_id,
        webhook_token=slack_webhook_token,
        message=slack_msg,
        username=username)
    failed_alert.execute(context=context)


## Variables Init

env = "PROD"
username = 'airflow'
aws_conn_id = "aws_default"
emr_conn_id = "emr_default"
slack_conn_id = 'slack_default'
files = "pluang-portfolio-snapshot-resources/snapshot-prod"
jars = "s3://pluang-portfolio-snapshot-resources/resources/kafka-clients-3.0.0.jar,s3://pluang-portfolio-snapshot-resources/resources/spark-token-provider-kafka-0-10_2.12-3.1.2.jar,s3://pluang-portfolio-snapshot-resources/resources/spark-sql-kafka-0-10_2.12-3.1.2.jar,s3://pluang-portfolio-snapshot-resources/resources/spark-streaming_2.12-3.1.2.jar,s3://pluang-portfolio-snapshot-resources/resources/commons-pool2-2.11.1.jar"
staging_files = "pluang-snapshot-resources/staging-pipeline"
staging_jars = "s3://pluang-snapshot-resources/kafka-clients-3.0.0.jar,s3://pluang-snapshot-resources/spark-token-provider-kafka-0-10_2.12-3.1.2.jar,s3://pluang-snapshot-resources/spark-sql-kafka-0-10_2.12-3.1.2.jar,s3://pluang-snapshot-resources/spark-streaming_2.12-3.1.2.jar,s3://pluang-snapshot-resources/commons-pool2-2.11.1.jar"
dev_files = "pluang-snapshot-resources/dev-pipeline"
dev_jars = "s3://pluang-snapshot-resources/kafka-clients-3.0.0.jar,s3://pluang-snapshot-resources/spark-token-provider-kafka-0-10_2.12-3.1.2.jar,s3://pluang-snapshot-resources/spark-sql-kafka-0-10_2.12-3.1.2.jar,s3://pluang-snapshot-resources/spark-streaming_2.12-3.1.2.jar,s3://pluang-snapshot-resources/commons-pool2-2.11.1.jar"
log_path = "pluang-datalake-calculated"
job_config_path = "/home/<USER>"
resource_config = {
    "large": {"memory": "10G", "executor": "5"},
    "medium": {"memory": "6G", "executor": "3"},
    "small": {"memory": "5G", "executor": "2"}
}

dag_id = 'BACKFILLING'
concurrency = 5
emr_core = 5
script_bootstrap_action_file = "add-aws-supporting-jars-kafka.sh"
DEFAULT_ARGS = {
    "owner": username,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "on_failure_callback": slack_alert,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
}
if env == "PROD":
    concurrency = 5
    emr_core = 2
    dag_id = 'BACKFILLING'
    script_bootstrap_action_file = "add-aws-supporting-jars-kafka.sh"
    log_path = "pluang-datalake-calculated"
    cluster = "large_cluster"
    DEFAULT_ARGS = {
        "owner": username,
        "depends_on_past": False,
        "wait_for_downstream": False,
        "start_date": pendulum.datetime(year, month, day, tz="UTC"),
        "on_failure_callback": slack_alert,
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
    }
elif env == "STAGING":
    concurrency = 1
    emr_core = 2
    dag_id = 'STAGING-BACKFILLING'
    script_bootstrap_action_file = "add-aws-supporting-jars-kafka-staging.sh"
    log_path = "pluang-staging-datalake-calculated"
    cluster = "small_cluster"
    files = staging_files
    jars = staging_jars
    DEFAULT_ARGS = {
        "owner": username,
        "depends_on_past": False,
        "wait_for_downstream": False,
        "start_date": pendulum.datetime(year, month, day, tz="UTC"),
        "on_failure_callback": slack_alert,
        "retries": 0,
        "retry_delay": timedelta(minutes=1),
    }
    resource_config = {
        "large": {"memory": "5G", "executor": "2"},
        "medium": {"memory": "5G", "executor": "2"},
        "small": {"memory": "5G", "executor": "2"}
    }
else:
    concurrency = 1
    emr_core = 2
    dag_id = 'DEV-BACKFILLING'
    script_bootstrap_action_file = "add-aws-supporting-jars-kafka.sh"
    log_path = "pluang-development-datalake-calculated"
    cluster = "small_cluster"
    files = dev_files
    jars = dev_jars
    DEFAULT_ARGS = {
        "owner": username,
        "depends_on_past": False,
        "wait_for_downstream": False,
        "start_date": pendulum.datetime(year, month, day, tz="UTC"),
        "on_failure_callback": slack_alert,
        "retries": 0,
        "retry_delay": timedelta(minutes=1),
    }
    resource_config = {
        "large": {"memory": "5G", "executor": "2"},
        "medium": {"memory": "5G", "executor": "2"},
        "small": {"memory": "5G", "executor": "2"}
    }


ZONE, DELTA = dateutil.tz.gettz("Asia/Jakarta"), -1
DATE = datetime.now(tz=ZONE) + timedelta(DELTA)

## Utils Init
portfolio_snapshot_utils = BackfillingUtils(
    files_path=files,
    jar_path=jars,
    job_config_path=job_config_path,
    resource_config=resource_config,
    log_path=log_path,
    env=env
)


## Base DAG INIT
BaseDag = BASE_DAG(
    dag_id=dag_id,
    default_args=DEFAULT_ARGS,
    schedule_interval=None,  #Manual Trigger
    catchup=False,
    tags=[
         "data-eng",
         "backfilling"
    ],
    team="data-eng"
)

# Create EMR Dag
DAG, \
Create_EMR_Cluster, \
is_cluster_active, \
Setup_Hadoop_Debugging, \
SENSOR_Setup_Hadoop_Debugging = BaseDag.Create_EMR_Dag(
    dagrun_timeout=timedelta(minutes=240), ##4 hour dag time out
    max_active_runs=1,
    concurrency=concurrency,
    on_failure_callback=slack_alert,
    emr_core=emr_core,
    env=env,
    script_bootstrap_action_file=script_bootstrap_action_file
)

globals()[dag_id] = DAG
chain(
    Create_EMR_Cluster,
    is_cluster_active,
    Setup_Hadoop_Debugging,
    SENSOR_Setup_Hadoop_Debugging
)
########################################################################################################################

execute_gold_snapshot = EmrAddStepsOperator(
    task_id='Execute-Gold-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_gold_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_gold_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_gold_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Gold-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_gold_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_bappebti_wallets_snapshot = EmrAddStepsOperator(
    task_id='Execute-Bappebti-Wallets-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_bappebti_wallets_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]

)

SENSOR_execute_bappebti_wallets_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_bappebti_wallets_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Bappebti-Wallets-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_bappebti_wallets_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_crypto_avg_hold_time = EmrAddStepsOperator(
    task_id='Execute-Crypto-Avg-Hold-Time',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_crypto_avg_hold_time(),
    params={"config": "config.json"},
    dag=globals()[dag_id]

)

SENSOR_execute_crypto_avg_hold_time = EmrStepSensor(
    task_id='SENSOR_execute_crypto_avg_hold_time',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Crypto-Avg-Hold-Time', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_crypto_avg_hold_time()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_crypto_buy_sell_ratio = EmrAddStepsOperator(
    task_id='Execute-Crypto-Buy-Sell-Ratio',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_crypto_buy_sell_ratio(),
    params={"config": "config.json"},
    dag=globals()[dag_id]

)

SENSOR_execute_crypto_buy_sell_ratio = EmrStepSensor(
    task_id='SENSOR_execute_crypto_buy_sell_ratio',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Crypto-Buy-Sell-Ratio', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_crypto_buy_sell_ratio()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_forex_snapshot = EmrAddStepsOperator(
    task_id='Execute-Forex-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_forex_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_forex_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_forex_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Forex-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_forex_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_fund_snapshot = EmrAddStepsOperator(
    task_id='Execute-Fund-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_fund_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_fund_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_fund_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Fund-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_fund_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_stock_index_snapshot = EmrAddStepsOperator(
    task_id='Execute-Stock-Index-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_stock_index_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_stock_index_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_stock_index_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Stock-Index-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_stock_index_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_crypto_snapshot = EmrAddStepsOperator(
    task_id='Execute-Crypto-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_crypto_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_crypto_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_crypto_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Crypto-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_crypto_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_indo_stock_returns = EmrAddStepsOperator(
    task_id='Execute-indo-stock-returns',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_indo_stock_returns(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_indo_stock_returns = EmrStepSensor(
    task_id='SENSOR_execute_indo_stock_returns',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-indo-stock-returns', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_indo_stock_returns()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_indo_stock_balance = EmrAddStepsOperator(
    task_id='Execute-indo-stock-balance',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_indo_stock_dividend(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_indo_stock_balance = EmrStepSensor(
    task_id='SENSOR_execute_indo_stock_balance',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-indo-stock-balance', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_indo_stock_dividend()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_cashin_snapshot = EmrAddStepsOperator(
    task_id='Execute-Cashin-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_cashin_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_cashin_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_cashin_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Cashin-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_cashin_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_cashout_snapshot = EmrAddStepsOperator(
    task_id='Execute-Cashout-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_cashout_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_cashout_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_cashout_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Cashout-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_cashout_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_global_stock_snapshot = EmrAddStepsOperator(
    task_id='Execute-Global-Stock-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_global_stock_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_global_stock_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_global_stock_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Global-Stock-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_global_stock_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)


execute_gold_gift_and_withdrawal_snapshot = EmrAddStepsOperator(
    task_id='Execute-Gold-Gift-And-Withdrawal-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_gold_gift_and_withdrawal_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_gold_gift_and_withdrawal_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_gold_gift_and_withdrawal_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Gold-Gift-And-Withdrawal-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_gold_gift_and_withdrawal_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)


execute_portfolio_snapshot = EmrAddStepsOperator(
    task_id='Execute-Portfolio-Snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_portfolio_snapshot(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_portfolio_snapshot = EmrStepSensor(
    task_id='SENSOR_execute_portfolio_snapshot',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Portfolio-Snapshot', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_portfolio_snapshot()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_daily_interm_pnl_calculation = EmrAddStepsOperator(
    task_id='Execute-Daily-Interm-PNL-Calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_daily_interm_pnl_calculation(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_daily_interm_pnl_calculation = EmrStepSensor(
    task_id='SENSOR_execute_daily_interm_pnl_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Daily-Interm-PNL-Calculation', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_daily_interm_pnl_calculation()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)



execute_daily_pnl_calculation = EmrAddStepsOperator(
    task_id='Execute-Daily-PNL-Calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_daily_pnl_calculation(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_daily_pnl_calculation = EmrStepSensor(
    task_id='SENSOR_execute_daily_pnl_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Daily-PNL-Calculation', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_daily_pnl_calculation()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_weekly_pnl_calculation = EmrAddStepsOperator(
    task_id='Execute-Weekly-PNL-Calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_weekly_pnl_calculation(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_weekly_pnl_calculation = EmrStepSensor(
    task_id='SENSOR_execute_weekly_pnl_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Weekly-PNL-Calculation', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_weekly_pnl_calculation()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_monthly_pnl_calculation = EmrAddStepsOperator(
    task_id='Execute-Monthly-PNL-Calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_monthly_pnl_calculation(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_monthly_pnl_calculation = EmrStepSensor(
    task_id='SENSOR_execute_monthly_pnl_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Monthly-PNL-Calculation', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_monthly_pnl_calculation()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_quarterly_pnl_calculation = EmrAddStepsOperator(
    task_id='Execute-Quarterly-PNL-Calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_quarterly_pnl_calculation(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_quarterly_pnl_calculation = EmrStepSensor(
    task_id='SENSOR_execute_quarterly_pnl_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Quarterly-PNL-Calculation', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_quarterly_pnl_calculation()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

execute_yearly_pnl_calculation = EmrAddStepsOperator(
    task_id='Execute-Yearly-PNL-Calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.execute_yearly_pnl_calculation(),
    params={"config": "config.json"},
    dag=globals()[dag_id]
)

SENSOR_execute_yearly_pnl_calculation = EmrStepSensor(
    task_id='SENSOR_execute_yearly_pnl_calculation',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Execute-Yearly-PNL-Calculation', key='return_value')[" + str(len(
        portfolio_snapshot_utils.execute_yearly_pnl_calculation()) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

copy_spark_logs_hdfs_to_s3 = EmrAddStepsOperator(
    task_id='Copy-Spark-Logs-S3',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    steps=portfolio_snapshot_utils.copy_spark_logs_hdfs_to_s3(DATE),
    dag=globals()[dag_id]
)

# ## SENSOR_copy spark logs hdfs to s3
SENSOR_copy_spark_logs_hdfs_to_s3 = EmrStepSensor(
    task_id='SENSOR-Copy-Spark-Logs-S3',
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    step_id="{{ task_instance.xcom_pull('Copy-Spark-Logs-S3', key='return_value')[" + str(
        len(portfolio_snapshot_utils.copy_spark_logs_hdfs_to_s3(DATE)) - 1) + "]}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

# Terminate the EMR cluster
terminate_emr_cluster = EmrTerminateJobFlowOperator(
    task_id="Terminate-EMR-Cluster",
    job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
    aws_conn_id=aws_conn_id,
    dag=globals()[dag_id]
)

delete_xcom = PythonOperator(
    task_id="delete_xcom",
    python_callable=clean_xcom,
    dag=globals()[dag_id]
)

end = DummyOperator(
    task_id='End',
    dag=globals()[dag_id]
)


asset_snapshot_jobs = [execute_crypto_snapshot,
                       execute_gold_snapshot,
                       execute_crypto_avg_hold_time,
                       execute_crypto_buy_sell_ratio,
                       execute_bappebti_wallets_snapshot,
                       execute_forex_snapshot,
                       execute_fund_snapshot,
                       execute_cashin_snapshot,
                       execute_cashout_snapshot,
                       execute_global_stock_snapshot,
                       execute_stock_index_snapshot,
                       execute_indo_stock_returns]

sensors_asset_snapshot_jobs = [SENSOR_execute_crypto_snapshot,
                               SENSOR_execute_gold_snapshot,
                               SENSOR_execute_crypto_avg_hold_time,
                               SENSOR_execute_crypto_buy_sell_ratio,
                               SENSOR_execute_bappebti_wallets_snapshot,
                               SENSOR_execute_forex_snapshot,
                               SENSOR_execute_fund_snapshot,
                               SENSOR_execute_cashin_snapshot,
                               SENSOR_execute_cashout_snapshot,
                               SENSOR_execute_global_stock_snapshot,
                               SENSOR_execute_stock_index_snapshot,
                               SENSOR_execute_indo_stock_returns]

execute_pnl_jobs = [execute_daily_pnl_calculation,
                    execute_weekly_pnl_calculation,
                    execute_monthly_pnl_calculation,
                    execute_quarterly_pnl_calculation,
                    execute_yearly_pnl_calculation]

sensors_execute_pnl_jobs = [SENSOR_execute_daily_pnl_calculation,
                            SENSOR_execute_weekly_pnl_calculation,
                            SENSOR_execute_monthly_pnl_calculation,
                            SENSOR_execute_quarterly_pnl_calculation,
                            SENSOR_execute_yearly_pnl_calculation]

## Dependency flow
chain(
    SENSOR_Setup_Hadoop_Debugging,
    asset_snapshot_jobs,
    sensors_asset_snapshot_jobs,
    execute_gold_gift_and_withdrawal_snapshot,
    SENSOR_execute_gold_gift_and_withdrawal_snapshot,
    execute_portfolio_snapshot,
    SENSOR_execute_portfolio_snapshot,
    execute_indo_stock_balance,
    SENSOR_execute_indo_stock_balance,
    execute_daily_interm_pnl_calculation,
    SENSOR_execute_daily_interm_pnl_calculation,
    execute_pnl_jobs,
    sensors_execute_pnl_jobs,
    copy_spark_logs_hdfs_to_s3,
    SENSOR_copy_spark_logs_hdfs_to_s3,
    terminate_emr_cluster,
    delete_xcom,
    end
)