# pylint: disable=eval-used
"""DAG for Regulatory Reporting Framework Execution"""

import os
import sys
import logging
from datetime import timedel<PERSON>, datetime
from pymongo import MongoClient

import pendulum
import yaml
from airflow.models import Variable
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import (
    KubernetesPodOperator,
)
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.utils.helpers import chain
from airflow.utils.db import provide_session
from airflow.models import XCom
from airflow.utils.trigger_rule import TriggerRule
from airflow.models.param import Param

from alerting.slack_alert import SlackAlert
from base_dags.base_dag import BASE_DAG
from alerting.dag_alert import DagAlert

#Airflow Variables
services_config = Variable.get("PLUANG_REGULATORY_REPORTING_DE_JOBS_CONFIGURATION", deserialize_json=True)
kubernetes_node_config = Variable.get("de_kubernetes_node_selector", deserialize_json=True)
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
config = services_config["services"]
ecr_image_map = config["ecr_images"]
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]

#Default Static and Final Variables
USERNAME = "airflow"
SLACK_CONN_ID = "slack_default"
slack_host_hook = config.get("slack_hook")
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)
ENV = Variable.get("ENV")
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
dag_alert = DagAlert(USERNAME, SLACK_CONN_ID, OPSGENIE_CONN_ID)


dags_array = []
date_format = "%Y-%m-%dT%H:%M:%S.%fZ"
DEFAULT_ARGS = {
    "owner": 'airflow',
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 1,
    "retry_delay": timedelta(minutes=2)
}

@provide_session
def clean_xcom(session=None, **context):
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()


try:
    client = MongoClient(config["mongo"]["host"], config["mongo"]["port"])
    dags_config = client[config["mongo"]["database"]][config["mongo"]["dag_collection"]]
    table_configs = client[config["mongo"]["database"]][config["mongo"]["table_collection"]]
except Exception as e:
    logging.exception(e)
    raise Exception("Cannot Connect to Mongo")

try:

    query_filter = {
        "active": True,
    }

    dags = dags_config.find(query_filter, {"_id": 0})
    for x in dags:
        dags_array.append(x)
except Exception as e:
    logging.exception(e)
    raise Exception("Mongo Query Error")

try:
    for dag in dags_array:
        dag_id = dag.get("dag_name")
        DEFAULT_ARGS['retries'] = dag.get("retries", 1)
        DEFAULT_ARGS['retry_delay'] = timedelta(minutes=dag.get("retry_delay", 2))
        DEFAULT_ARGS["on_failure_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
        DEFAULT_ARGS["on_skipped_callback"] = lambda context: dag_alert.all_channel_alert_failure(context, is_opsgenie_alert_enabled=opsgenie_config["is_opsgenie_alert_enabled"])
        BaseDagDataSync = BASE_DAG(
            dag_id=dag_id,
            default_args=DEFAULT_ARGS,
            schedule_interval=dag.get("schedule_interval", "*/30 * * * *"),
            catchup=False,
            tags=dag.get("tags", ["data-eng", "kafka", "bq"]),
            team=dag.get("team", "data-eng")
        )

        Dag = BaseDagDataSync.Create_Dag(
            dagrun_timeout=timedelta(minutes=dag.get("timeout", 10)),
            max_active_runs=1,
            slack_conn_details=(
                SLACK_CONN_ID,
                slack_host_hook,
            ),
            concurrency=dag.get("concurrency", 10),
            params={
                "manual_tasks": Param([], type="array")
            },
        )

        globals()[dag_id] = Dag

        start = DummyOperator(task_id="Start", dag=globals()[dag_id])

        pipelines_array = []
        try:
            filter = {"dag_name": dag_id, "active": True}
            pipelines = table_configs.find(filter, {"_id": 0, "created_at": 0, "updated_at": 0})
            for x in pipelines:
                pipelines_array.append(x)
        except Exception as e:
            logging.exception(e)
            raise Exception("Mongo Error")
        try:
            filter = {"object_type": "ecr_image"}
            ecr_image_config = table_configs.find(filter, {"_id": 0})
        except Exception as e:
            logging.exception(e)
            raise Exception("Mongo Error")


        # Data Extraction python tasks
        tasks = []
        task_name = None
        for pipeline in pipelines_array:
            pipeline_stages = pipeline["stages"]
            for stage_name, stage_value in pipeline["stages"].items():
                task = KubernetesPodOperator(
                    name=f"{pipeline['task_name']}-{stage_name}",
                    image=ecr_image_map[pipeline_stages[stage_name]['key']].get('image'),
                    node_selector={kubernetes_node_config.get("node_selector_key"): kubernetes_node_config.get("node_selector_value")},
                    config_file=KUBE_CONFIG_PATH,
                    task_id=f"{pipeline['task_name']}-{stage_name}",
                    cluster_context=CLUSTER_CONTEXT_EKS,
                    in_cluster=False,
                    namespace=NAME,
                    execution_timeout=timedelta(hours=1.5),
                    on_failure_callback=slack_alert.slack_alert_failure,
                    # Timeout to start up the Pod, default is 120.
                    startup_timeout_seconds=600,
                    cmds=[
                        "python3",
                        "-m",
                        f"{ecr_image_map[pipeline_stages[stage_name]['key']].get('entry_point')}",
                        f"{pipeline}",
                        f"{pipeline_stages[stage_name]['value']}",
                        "{{ params.manual_tasks }}"
                    ],
                    get_logs=True,
                    dag=globals()[dag_id]
                )
                tasks.append(task)

        delete_xcom = PythonOperator(
            task_id="delete_xcom", python_callable=clean_xcom, dag=globals()[dag_id]
        )
        ## End
        end = DummyOperator(task_id="End", dag=globals()[dag_id])

        ## Dependency flow
        chain(start, tasks, delete_xcom, end)
except Exception as e:
    logging.exception(e)
    raise Exception("Dag Config Error")
