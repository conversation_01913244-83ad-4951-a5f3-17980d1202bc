# pylint: disable=eval-used
"""DAG for Transaction Matching"""

import os
import sys
from datetime import datetime, timedelta, date
from enum import Enum
from airflow.utils.log.logging_mixin import LoggingMixin
import pendulum
import pytz
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow_kubernetes_job_operator.kubernetes_job_operator import (
    KubernetesJobOperator,
)  # https://github.com/LamaAni/KubernetesJobOperator
from alerting.slack_alert import SlackAlert
from base_dags.base_dag import BASE_DAG
logging = LoggingMixin().log


class Vendors(Enum):
    """Vendors."""
    DANA = "DANA"
    DANA_BUY_SELL = "DANA_BUY_SELL"
    DANA_TOPUP = "DANA_TOPUP"
    BLIBLI = "BLIBLI"
    TOKOPEDIA = "TOKOPEDIA"
    SHOPEE_PAY = "SHOPEE_PAY"
    GOPAY = "GOPAY"
    LINKAJA = "LINKAJA"
    GOINVEST = "GOINVEST"
    IRIS = "IRIS"
    MIDTRANS_VA = "MIDTRANS_VA"
    XENDIT = "XENDIT"
    GOPAY_MUTUAL_FUND = "GOPAY_MUTUAL_FUND"
    GOINVEST_MUTUAL_FUND = "GOINVEST_MUTUAL_FUND"
    MIDTRANS = "MIDTRANS"
    BUKALAPAK = "BUKALAPAK"


class VendorToScrapingTaskIDs(Enum):
    """Vendor to scraping task ids"""

    DANA_BUY_SELL = "Dana-Buy-Sell-Data-Scraping-Job"
    DANA_TOPUP = "Dana-Topup-Data-Scraping-Job"
    BLIBLI = "Blibli-Data-Scraping-Job"
    TOKOPEDIA = "Tokopedia-Data-Scraping-Job"
    SHOPEE_PAY = "Shopee-Pay-Data-Scraping-Job"
    GOPAY = "GoPay-Data-Scraping-Job"
    LINKAJA = "LinkAja-Data-Scraping-Job"
    GOINVEST = "GoInvest-Data-Scraping-Job"
    IRIS = "IRIS-Data-Scraping-Job"
    MIDTRANS_VA = "Midtrans-VA-Data-Scraping-Job"
    XENDIT = "Xendit-Data-Scraping-Job"
    GOPAY_MUTUAL_FUND = "GoPay-Mutual-Fund-Data-Scraping-Job"
    GOINVEST_MUTUAL_FUND = "GoInvest-Mutual-Fund-Data-Scraping-Job"
    BUKALAPAK = "Gold-Loan-Summary-Generator-Job"
    XERO_FILE = "Xero-File-Generation"


USERNAME = "airflow"
ENV = Variable.get("ENV")
SLACK_CONN_ID = "transaction_matching_slack"
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
NAME = Variable.get("MWAA_NAMESPACE")
ROOT_DIR = os.path.dirname(os.path.abspath("__file__"))
Pipeline_Utils_Dir_Path = os.path.join(ROOT_DIR, "dags/pipeline_utils")
sys.path.append(Pipeline_Utils_Dir_Path)
config_data = Variable.get("transaction_matching_config", deserialize_json=True)
transaction_matching_offset = config_data.get("transaction_matching_offset")
IMAGES = config_data.get("images")
slack_host_hook = config_data.get("slack_hook")
data_scraping_offset = config_data.get("data_scraping_offset")
midtrans_ui_scraping_offset = config_data.get("midtrans_ui_scraping_offset")
offset_timestamp = datetime.now(tz=pytz.timezone("Asia/Jakarta")) - timedelta(
    data_scraping_offset - 1
)
OFFSET_TIMESTAMP_DATE = str(offset_timestamp.date())
slack_alert = SlackAlert(USERNAME, SLACK_CONN_ID)
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])
MIDTRANS_COMMAND = "TransactionMatching.midtrans.main"
DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 0,
    "retry_delay": timedelta(seconds=60),
    "on_failure_callback": slack_alert.slack_alert_failure,
}


def get_first_day_of_the_month(internal_offset: int) -> datetime.date:
    """
    Get First day of the month

    Args:
        internal_offset (int): Day offset

    Returns:
        datetime.date: Date
    """
    today_dt = datetime.today() - timedelta(internal_offset)
    # For first day of the month
    first_day_of_month = today_dt.replace(day=1)
    return first_day_of_month.date()


def run_xero_file_generation(**context):
    """
    Running Xero File Generation as K8's Task

    Args:
        context: dict
    """    
    internal_config_data = Variable.get(
        "transaction_matching_config", deserialize_json=True
    )
    internal_data_scraping_offset = internal_config_data.get("data_scraping_offset")
    logging.info(f"internal_data_scraping_offset: {internal_data_scraping_offset}")
    is_monthly_run_forceful = internal_config_data.get("gold_loan_scraping_params").get("is_monthly_run")
    logging.info(f"is_monthly_run_forceful: {is_monthly_run_forceful}")
    is_monthly_run = is_monthly_run_forceful
    if not is_monthly_run_forceful:
        first_month_date = get_first_day_of_the_month(internal_data_scraping_offset -1)
        current_date =  datetime.today().date()
        is_monthly_run = True if first_month_date == current_date else False
    
    logging.info(f"internal_data_scraping_offset: {internal_data_scraping_offset}, is_monthly_run: {is_monthly_run}")

    job = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.XERO_FILE.value,
    namespace=NAME,
    image=IMAGES["XERO_FILE_GENERATION"],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    command=[
        "python3",
        "-m",
        "TransactionMatching.xero_file_generation.main",
        "--is_monthly_run",
        f"{is_monthly_run}"
    ],
    delete_policy="Always",
    get_logs=True
)
    job.pre_execute(context=context)
    job.execute(context=context)


def run_gold_loan(**context):
    """
    Running Gold Loan as K8's task

    Args:
        context: dict
    """ 
    internal_config_data = Variable.get(
        "transaction_matching_config", deserialize_json=True
    )
    logging.info(f"internal_config_data: {internal_config_data}")
    internal_data_scraping_offset = internal_config_data.get("data_scraping_offset")
    logging.info(f"internal_data_scraping_offset: {internal_data_scraping_offset}")
    is_monthly_run_forceful = internal_config_data.get("gold_loan_scraping_params").get("is_monthly_run")
    logging.info(f"is_monthly_run_forceful: {is_monthly_run_forceful}")
    is_monthly_run = is_monthly_run_forceful
    if not is_monthly_run_forceful:
        first_month_date = get_first_day_of_the_month(internal_data_scraping_offset -1)
        current_date =  datetime.today().date()
        is_monthly_run = True if first_month_date == current_date else False

    logging.info(f"internal_data_scraping_offset: {internal_data_scraping_offset}, is_monthly_run: {is_monthly_run}")

    job = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.BUKALAPAK.value,
    namespace=NAME,
    image=IMAGES[Vendors.BUKALAPAK.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    command=[
        "python3",
        "-m",
        "TransactionMatching.gold_loan.main",
        "--offset",
        f"{internal_data_scraping_offset}",
        "--is_monthly_run",
        f"{is_monthly_run}"
    ],
    delete_policy="Always",
    get_logs=True
    )
    job.pre_execute(context=context)
    job.execute(context=context)



def run_dana_scrapping(is_buy_sell:bool, is_topup:bool, **context):
    """Run Dana Scrapping

    Args:
        **context: context
    """
    dana_buy, dana_sell, dana_topup, dana_withdrawal = False, False, False, False
    image = IMAGES[Vendors.DANA.value]
    internal_config_data = Variable.get(
        "transaction_matching_config", deserialize_json=True
    )
    internal_data_scraping_offset = internal_config_data.get("data_scraping_offset")
    scraping_params = internal_config_data.get("dana_scraping_params")
    logging.info(f"OFFSET: {internal_data_scraping_offset}, Buy/Sell: {is_buy_sell}, Topup: {is_topup}")

    if is_buy_sell:
        task_id = VendorToScrapingTaskIDs.DANA_BUY_SELL.value
        dana_buy = (
            eval(scraping_params.get("dana_buy"))
            if not isinstance(scraping_params.get("dana_buy"), bool)
            else scraping_params.get("dana_buy")
        )
        dana_sell = (
            eval(scraping_params.get("dana_sell"))
            if not isinstance(scraping_params.get("dana_sell"), bool)
            else scraping_params.get("dana_sell")
        )

        dana_withdrawal = (
            eval(scraping_params.get("dana_withdrawal"))
            if not isinstance(scraping_params.get("dana_withdrawal"), bool)
            else scraping_params.get("dana_withdrawal")
        )

    if is_topup:
        task_id = VendorToScrapingTaskIDs.DANA_TOPUP.value
        dana_topup = (
            eval(scraping_params.get("dana_topup"))
            if not isinstance(scraping_params.get("dana_topup"), bool)
            else scraping_params.get("dana_topup")
        )    

    job = KubernetesJobOperator(
        task_id=task_id,
        namespace=NAME,
        image=image,
        image_pull_policy="Always",
        config_file=KUBE_CONFIG_PATH,
        cluster_context=CLUSTER_CONTEXT_EKS,
        in_cluster=False,
        command=[
            "python3",
            "-m",
            "TransactionMatching.dana.main",
            "--offset",
            f"{internal_data_scraping_offset}",
            "--is_buy",
            f"{dana_buy}",
            "--is_sell",
            f"{dana_sell}",
            "--is_topup",
            f"{dana_topup}",
            "--is_withdrawal",
            f"{dana_withdrawal}"
        ],
        delete_policy="Always",
        get_logs=True,
    )
    job.pre_execute(context=context)
    job.execute(context=context)


def run_transaction_matching_engine(**context):
    """Run Transaction Matching Engine

    Args:
        **context: context
    """
    internal_config_data = Variable.get(
        "transaction_matching_config", deserialize_json=True
    )
    internal_transaction_matching_offset = internal_config_data.get(
        "transaction_matching_offset"
    )
    logging.info(f"transaction_matching_offset: {internal_transaction_matching_offset}")
    job = KubernetesJobOperator(
        task_id="Transaction-Matching-Engine-Kubernates-job",
        namespace=NAME,
        image=IMAGES["TRANSACTION_MATCHING_ENGINE"],
        image_pull_policy="Always",
        config_file=KUBE_CONFIG_PATH,
        cluster_context=CLUSTER_CONTEXT_EKS,
        in_cluster=False,
        command=[
            "python3",
            "-m",
            "TransactionMatching.transaction_matching_engine.main",
            "--offset",
            f"{internal_transaction_matching_offset}"
        ],
        trigger_rule=TriggerRule.ALL_DONE,
        delete_policy="Always",
        on_success_callback=slack_alert.slack_alert_success,
        get_logs=True,
        dag=dag,
    )
    job.pre_execute(context=context)
    job.execute(context=context)


DAG_ID = "Transaction-Matching"

# Base Dag Init
transaction_matching_base_dag = BASE_DAG(
    dag_id=DAG_ID,
    default_args=DEFAULT_ARGS,
    schedule_interval="30 05 * * *",  # 05-30 am UTC <-> (12:30 pm Jakarta) <-> (11:00 am IST)
    catchup=False,
    tags=[
        "data-eng",
        "transaction_matching",
        "rpa",
        "dana",
        "blibli",
        "shopee_pay",
        "tokopedia",
        "gopay",
        "goinvest",
        "iris",
        "midtrans",
        "xendit",
        "xero",
    ],
    team="data-eng",
)
DAG = transaction_matching_base_dag.Create_Dag(
    dagrun_timeout=timedelta(minutes=180),
    max_active_runs=1,
    slack_conn_details=(
        SLACK_CONN_ID,
        slack_host_hook,
    ),
    concurrency=2
)

globals()[DAG_ID] = DAG
dag = globals()[DAG_ID]

start = DummyOperator(task_id="Start", dag=dag)

transaction_matching_dana_buy_sell_task = PythonOperator(
    task_id=VendorToScrapingTaskIDs.DANA_BUY_SELL.value,
    python_callable=run_dana_scrapping,
    op_kwargs={'is_buy_sell': True, 'is_topup': False},
    execution_timeout=timedelta(minutes=30),
    provide_context=True,
    retries=3,
    max_retry_delay=pendulum.duration(seconds=300),
    trigger_rule=TriggerRule.ALL_DONE,
    dag=dag,
)

transaction_matching_dana_topup_task = PythonOperator(
    task_id=VendorToScrapingTaskIDs.DANA_TOPUP.value,
    python_callable=run_dana_scrapping,
    op_kwargs={'is_buy_sell': False, 'is_topup': True},
    execution_timeout=timedelta(minutes=30),
    provide_context=True,
    retries=3,
    max_retry_delay=pendulum.duration(seconds=300),
    trigger_rule=TriggerRule.ALL_DONE,
    dag=dag,
)

transaction_matching_blibli_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.BLIBLI.value,
    namespace=NAME,
    image=IMAGES[Vendors.BLIBLI.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        "TransactionMatching.blibli.main",
        "--offset",
        f"{data_scraping_offset}",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

# transaction_matching_tokopedia_task = KubernetesJobOperator(
#     task_id=VendorToScrapingTaskIDs.TOKOPEDIA.value,
#     namespace=NAME,
#     image=IMAGES[Vendors.TOKOPEDIA.value],
#     image_pull_policy="Always",
#     config_file=KUBE_CONFIG_PATH,
#     cluster_context=CLUSTER_CONTEXT_EKS,
#     in_cluster=False,
#     execution_timeout=timedelta(minutes=10),
#     command=[
#         "python3",
#         "-m",
#         "TransactionMatching.tokopedia.main",
#         "--offset",
#         f"{data_scraping_offset}",
#         "--timestamp",
#         f"{OFFSET_TIMESTAMP_DATE}",
#     ],
#     delete_policy="Always",
#     get_logs=True,
#     dag=dag,
# )

transaction_matching_shopee_pay_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.SHOPEE_PAY.value,
    namespace=NAME,
    image=IMAGES[Vendors.SHOPEE_PAY.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        "TransactionMatching.shopee_pay.main",
        "--offset",
        f"{data_scraping_offset}",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_gopay_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.GOPAY.value,
    namespace=NAME,
    image=IMAGES[Vendors.MIDTRANS.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        MIDTRANS_COMMAND,
        "--vendor",
        f"{Vendors.GOPAY.value}",
        "--offset",
        f"{data_scraping_offset}",
        "--ui-offset",
        f"{midtrans_ui_scraping_offset}",
        "--no-dashboard-parsing",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_gopay_mutual_fund_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.GOPAY_MUTUAL_FUND.value,
    namespace=NAME,
    image=IMAGES[Vendors.MIDTRANS.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        MIDTRANS_COMMAND,
        "--vendor",
        f"{Vendors.GOPAY_MUTUAL_FUND.value}",
        "--offset",
        f"{data_scraping_offset}",
        "--ui-offset",
        f"{midtrans_ui_scraping_offset}",
        "--no-dashboard-parsing",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_midtrans_va_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.MIDTRANS_VA.value,
    namespace=NAME,
    image=IMAGES[Vendors.MIDTRANS.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        MIDTRANS_COMMAND,
        "--vendor",
        f"{Vendors.MIDTRANS_VA.value}",
        "--offset",
        f"{data_scraping_offset}",
        "--ui-offset",
        f"{midtrans_ui_scraping_offset}",
        "--no-dashboard-parsing",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_goinvest_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.GOINVEST.value,
    namespace=NAME,
    image=IMAGES[Vendors.MIDTRANS.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        MIDTRANS_COMMAND,
        "--vendor",
        f"{Vendors.GOINVEST.value}",
        "--offset",
        f"{data_scraping_offset}",
        "--ui-offset",
        f"{midtrans_ui_scraping_offset}",
        "--no-dashboard-parsing",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_goinvest_mutual_fund_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.GOINVEST_MUTUAL_FUND.value,
    namespace=NAME,
    image=IMAGES[Vendors.MIDTRANS.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        MIDTRANS_COMMAND,
        "--vendor",
        f"{Vendors.GOINVEST_MUTUAL_FUND.value}",
        "--offset",
        f"{data_scraping_offset}",
        "--ui-offset",
        f"{midtrans_ui_scraping_offset}",
        "--no-dashboard-parsing",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_iris_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.IRIS.value,
    namespace=NAME,
    image=IMAGES[Vendors.MIDTRANS.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        MIDTRANS_COMMAND,
        "--vendor",
        f"{Vendors.IRIS.value}",
        "--offset",
        f"{data_scraping_offset}",
        "--ui-offset",
        f"{midtrans_ui_scraping_offset}",
        "--no-dashboard-parsing",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_xendit_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.XENDIT.value,
    namespace=NAME,
    image=IMAGES[Vendors.XENDIT.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        "TransactionMatching.xendit.main",
        "--no-dashboard-parsing",
        "--email-scraping-offset",
        f"{data_scraping_offset}",
        "--ui-scraping-offset",
        f"{midtrans_ui_scraping_offset}",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_linkaja_task = KubernetesJobOperator(
    task_id=VendorToScrapingTaskIDs.LINKAJA.value,
    namespace=NAME,
    image=IMAGES[Vendors.LINKAJA.value],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=10),
    command=[
        "python3",
        "-m",
        "TransactionMatching.linkaja.main",
        "--offset",
        f"{data_scraping_offset}",
        "--timestamp",
        f"{OFFSET_TIMESTAMP_DATE}",
    ],
    delete_policy="Always",
    get_logs=True,
    dag=dag,
)

transaction_matching_engine_task = PythonOperator(
    task_id="Transaction-Matching-Engine",
    python_callable=run_transaction_matching_engine,
    on_success_callback=slack_alert.slack_alert_success,
    provide_context=True,
    execution_timeout=timedelta(minutes=30),
    trigger_rule=TriggerRule.ALL_DONE,
    dag=dag,
)


transaction_matching_iris_data_validator_task = KubernetesJobOperator(
    task_id="IRIS-Data-Validator",
    namespace=NAME,
    image=IMAGES["IRIS_DATA_VALIDATOR"],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=30),
    delete_policy="Always",
    on_success_callback=slack_alert.slack_alert_success,
    get_logs=True,
    dag=dag,
)

transaction_matching_xendit_data_validator_task = KubernetesJobOperator(
    task_id="XENDIT-Data-Validator",
    namespace=NAME,
    image=IMAGES["XENDIT_DATA_VALIDATOR"],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=30),
    delete_policy="Always",
    on_success_callback=slack_alert.slack_alert_success,
    get_logs=True,
    dag=dag,
)

transaction_matching_gold_loan_summary_task = PythonOperator(
    task_id=VendorToScrapingTaskIDs.BUKALAPAK.value,
    python_callable=run_gold_loan,
    on_success_callback=slack_alert.slack_alert_success,
    execution_timeout=timedelta(minutes=30),
    provide_context=True,
    dag=dag,
)

slack_email_alert_task = KubernetesJobOperator(
    task_id="Slack-Email-Alert",
    namespace=NAME,
    image=IMAGES["ALERTING"],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=30),
    command=[
        "python3",
        "-m",
        "TransactionMatching.alerting.main",
        "--offset",
        f"{transaction_matching_offset}"
    ],
    trigger_rule=TriggerRule.ALL_DONE,
    delete_policy="Always",
    on_success_callback=slack_alert.slack_alert_success,
    get_logs=True,
    dag=dag,
)

xero_file_generation_task = PythonOperator(
    task_id=VendorToScrapingTaskIDs.XERO_FILE.value,
    python_callable=run_xero_file_generation,
    on_success_callback=slack_alert.slack_alert_success,
    execution_timeout=timedelta(minutes=30),
    provide_context=True,
    dag=dag,
)

xero_file_uploading_task = KubernetesJobOperator(
    task_id="Xero-File-Upload",
    namespace=NAME,
    image=IMAGES["XERO_UI"],
    image_pull_policy="Always",
    config_file=KUBE_CONFIG_PATH,
    cluster_context=CLUSTER_CONTEXT_EKS,
    in_cluster=False,
    execution_timeout=timedelta(minutes=30),
    command=[
        "python3",
        "-m",
        "TransactionMatching.xero_uploading_ui_bot.main",
    ],
    delete_policy="Always",
    on_success_callback=slack_alert.slack_alert_success,
    retries=1,
    get_logs=True,
    dag=dag,
)

end = DummyOperator(task_id="End", dag=dag)

# Dependency Chain
start >> transaction_matching_blibli_task
start >> transaction_matching_goinvest_task
start >> transaction_matching_goinvest_mutual_fund_task
start >> transaction_matching_gopay_task
start >> transaction_matching_gopay_mutual_fund_task
start >> transaction_matching_midtrans_va_task
start >> transaction_matching_xendit_task
start >> transaction_matching_iris_task
start >> transaction_matching_linkaja_task
start >> transaction_matching_shopee_pay_task
# start >> transaction_matching_tokopedia_task
start >> transaction_matching_dana_buy_sell_task >> transaction_matching_dana_topup_task
transaction_matching_blibli_task >> transaction_matching_engine_task
transaction_matching_goinvest_task >> transaction_matching_engine_task
transaction_matching_goinvest_mutual_fund_task >> transaction_matching_engine_task
transaction_matching_gopay_task >> transaction_matching_engine_task
transaction_matching_gopay_mutual_fund_task >> transaction_matching_engine_task
transaction_matching_midtrans_va_task >> transaction_matching_engine_task
transaction_matching_xendit_task >> transaction_matching_engine_task
transaction_matching_iris_task >> transaction_matching_engine_task
transaction_matching_linkaja_task >> transaction_matching_engine_task
transaction_matching_shopee_pay_task >> transaction_matching_engine_task
# transaction_matching_tokopedia_task >> transaction_matching_engine_task
transaction_matching_dana_topup_task >> transaction_matching_engine_task
transaction_matching_engine_task >> transaction_matching_iris_data_validator_task
transaction_matching_engine_task >> transaction_matching_xendit_data_validator_task
transaction_matching_engine_task >> transaction_matching_gold_loan_summary_task
transaction_matching_iris_data_validator_task >> xero_file_generation_task
transaction_matching_xendit_data_validator_task >> xero_file_generation_task
transaction_matching_gold_loan_summary_task >> xero_file_generation_task
xero_file_generation_task >> xero_file_uploading_task
xero_file_uploading_task >> slack_email_alert_task
slack_email_alert_task >> end
