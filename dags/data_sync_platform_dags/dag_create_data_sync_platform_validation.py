import sys, os, logging, json
from datetime import timedelta, datetime

import pendulum
from airflow.models import Variable
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.providers.cncf.kubernetes.operators.kubernetes_pod import (
    KubernetesPodOperator,
)
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.hooks.base_hook import BaseHook
from airflow.utils.helpers import chain
from airflow.utils.db import provide_session
from airflow.models import XCom

from base_dags.base_dag import BASE_DAG
from pymongo import MongoClient, ReadPreference

ROOT_DIR = os.path.dirname(os.path.abspath("__file__")).split("dags/")[0]
alerting_path = os.path.join(ROOT_DIR, "dags/alerting")
sys.path.append(alerting_path)
from alerting.dag_alert import DagAlert
opsgenie_config = Variable.get("de_opsgenie_secret", deserialize_json=True)
OPSGENIE_CONN_ID = opsgenie_config["opsgenie_conn_id"]


kubernetes_node_config = Variable.get("de_kubernetes_node_selector", deserialize_json=True)

USERNAME = "airflow"
SLACK_CONN_ID = "slack_default"
ENV = Variable.get("ENV")
IMAGE = Variable.get("ecr_image_data_sync_platform")
NAME = Variable.get("MWAA_NAMESPACE")
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
start_date = Variable.get("default_args", deserialize_json=True).get('start_date').split(',')
year, month, day = int(start_date[0]), int(start_date[1]), int(start_date[2])

dag_alert = DagAlert(USERNAME, SLACK_CONN_ID, OPSGENIE_CONN_ID)
config_file_name = ENV.lower() + "_data_sync_platform"
config = Variable.get(config_file_name, deserialize_json=True)
config = config["services"]


dags_array = []
date_format = "%Y-%m-%dT%H:%M:%S.%fZ"
DEFAULT_ARGS = {
    "owner": 'airflow',
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 1,
    "retry_delay": timedelta(minutes=2)
}

@provide_session
def clean_xcom(session=None, **context):
    dag = context["dag"]
    dag_id = dag._dag_id
    session.query(XCom).filter(XCom.dag_id == dag_id).delete()

def slack_alert(context):
    """
    Slack Alert
    """
    slack_webhook_token = BaseHook.get_connection('slack_default').password
    slack_msg = """
            :red_circle: Task Failed. 
            *Task*: {task}  
            *Dag*: {dag} 
            *Execution Time*: {exec_date}  
            *Log Url*: {log_url} 
            """.format(
            task=context.get('task_instance').task_id,
            dag=context.get('task_instance').dag_id,
            ti=context.get('task_instance'),
            exec_date=context.get('execution_date'),
            log_url=context.get('task_instance').log_url,
    )

    failed_alert = SlackWebhookOperator(
        task_id='slack_alert',
        http_conn_id='slack_default',
        webhook_token=slack_webhook_token,
        message=slack_msg,
        username='airflow')
    return failed_alert.execute(context=context)

try:
    client = MongoClient(config["mongo"]["host"], config["mongo"]["port"],
                         read_preference=ReadPreference.SECONDARY_PREFERRED)
    dags_config = client[config["mongo"]["database"]][config["mongo"]["dag_collection"]]
    table_configs = client[config["mongo"]["database"]][config["mongo"]["table_collection"]]
except Exception as e:
    logging.exception(e)
    raise Exception("Cannot Connect to Mongo")

try:
    filter = {"active" : True, "validation_dag": True}
    dags = dags_config.find(filter, { "_id":0 })
except Exception as e:
    logging.exception(e)
    raise Exception("Mongo Query Error")


for dag in dags:
    dag_id = dag.get("id")
    validation_message = dag.get("validation_message", {"table_not_updated":"Data Sync Platform Validation: Below Bigquery tables are not updated in last 30 min!","missing_or_duplicate":"Data Sync Platform Validation: Below Bigquery tables have dulicates or missed records for the date: "})
    DEFAULT_ARGS['retries'] = int(dag.get("retries", 1))
    DEFAULT_ARGS['retry_delay'] = timedelta(minutes=int(dag.get("retry_delay", 2)))
    BaseDagDataSync = BASE_DAG(
        dag_id=dag_id,
        default_args=DEFAULT_ARGS,
        schedule_interval=dag.get("schedule_interval", "0 */1 * * *"),
        catchup=False,
        tags=dag.get("tags", ["data-eng", "bq", "validation"]),
        team=dag.get("team", "data-eng")
    )

    Dag = BaseDagDataSync.Create_Dag(
        dagrun_timeout=timedelta(minutes=dag.get("timeout", 10)),
        max_active_runs=1,
        on_failure_callback=slack_alert,
    )

    globals()[dag_id] = Dag

    start = DummyOperator(task_id="Start", dag=globals()[dag_id])

    pipelines = []
    try:
        if dag.get('validation_frequency') == "daily_validation":
            filter = {"validation_active": True, "daily_validation": True}
        elif dag.get('validation_frequency') == "weekly_validation":
            filter = {"validation_active": True, "weekly_validation": True}
        pipelines_array = table_configs.find(filter, {"_id": 0, "created_at": 0, "updated_at": 0})
        for pipeline in pipelines_array:
            pipelines.append(pipeline)
    except Exception as e:
        logging.exception(e)
        raise Exception("Mongo Error")

    run_data_validation = []
    validation = KubernetesPodOperator(
        name=f"Validation-for-Data-Sync-Tables",
        image=IMAGE,
        node_selector={kubernetes_node_config.get("node_selector_key"): kubernetes_node_config.get("node_selector_value")},
        config_file=KUBE_CONFIG_PATH,
        task_id=f"Validation-for-Data-Sync-Tables",
        cluster_context=CLUSTER_CONTEXT_EKS,
        in_cluster=False,
        namespace=NAME,
        execution_timeout=timedelta(minutes=30),
        startup_timeout_seconds=600,
        cmds=[
            "python3",
            "-m",
            "main",
            f"{pipelines}",
            f"{dag.get('validation_frequency')}",
            f"{dag.get('validation_offset_in_days')}",
            f"{json.dumps(dag.get('validation_message'))}"
        ],
        get_logs=True,
        dag=globals()[dag_id]
    )
    run_data_validation.append(validation)

    delete_xcom = PythonOperator(
        task_id="delete_xcom", python_callable=clean_xcom, dag=globals()[dag_id]
    )
## End
    end = DummyOperator(task_id="End", dag=globals()[dag_id])

    ## Dependency flow
    chain(start, run_data_validation, delete_xcom, end)

