import ast
import pendulum
import logging
from datetime import timedelta
from airflow.utils.helpers import chain
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from base_dags.base_dag import BASE_DAG
from airflow.models import Variable
from helpers.helper import Helper
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.hooks.base_hook import BaseHook
from airflow_kubernetes_job_operator.kubernetes_job_operator import (
    KubernetesJobOperator,
)  # https://github.com/LamaAni/KubernetesJobOperator

## Variables Init
env = Variable.get("ENV")
helper = Helper(env=env)
config = Variable.get("emr_dag_config", deserialize_json=True)

usd_yield_disbursement_config = Variable.get("usd_yield_disbursement", deserialize_json=True)
KUBE_CONFIG_PATH = Variable.get("kube_config_path")
CLUSTER_CONTEXT_EKS = Variable.get("cluster_context")
IMAGE = usd_yield_disbursement_config.get("image")
NAMESPACE = Variable.get("MWAA_NAMESPACE")
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
USERNAME = "airflow"
SLACK_CONN_ID = "slack_default"
start_date = (
    Variable.get("default_args", deserialize_json=True).get("start_date").split(",")
)
year, month, day = 2025,3,5

# Slack success and fail
def slack_alert(context):
    """
    Slack Alert
    """
    slack_webhook_token = BaseHook.get_connection(config["slack_conn_id"]).password
    slack_msg = """
            :red_circle: Task Failed. 
            *Task*: {task}  
            *Dag*: {dag} 
            *Execution Time*: {exec_date}  
            *Log Url*: {log_url} 
            """.format(
        task=context.get("task_instance").task_id,
        dag=context.get("task_instance").dag_id,
        exec_date=context.get("execution_date"),
        log_url=context.get("task_instance").log_url,
    )

    failed_alert = SlackWebhookOperator(
        task_id="slack_alert",
        http_conn_id=config["slack_conn_id"],
        webhook_token=slack_webhook_token,
        message=slack_msg,
        username=config["username"],
    )
    failed_alert.execute(context=context)


DEFAULT_ARGS = {
    "owner": USERNAME,
    "depends_on_past": False,
    "wait_for_downstream": False,
    "start_date": pendulum.datetime(year, month, day, tz="UTC"),
    "retries": 1,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": slack_alert,
}

# Dag ID name
DAG_ID = "usd-yield-disbursement"

## Base DAG INIT
base_dag = BASE_DAG(
    dag_id=DAG_ID,
    default_args=DEFAULT_ARGS,
    catchup=False,
    schedule_interval="00 01 5 * *",
    tags=["data-eng", "usd_yield", "kafka", "kubernetes"],
    team="data-eng",
)

logging.info("base dag created")
usd_yield_disbursement_dag = base_dag.Create_Dag(
    dagrun_timeout=timedelta(minutes=300),
    max_active_runs=1,
    on_failure_callback=slack_alert,
)

globals()[DAG_ID] = usd_yield_disbursement_dag
dag = globals()[DAG_ID]

########################################################################################################################

def usd_yield_disbursement_k8s_task(configuration, **context):
    configuration = {**ast.literal_eval(configuration), **{}}
    conf_year = configuration.get("year")
    conf_month = configuration.get("month")
    logging.info(configuration)
    logging.info(conf_year)
    logging.info(conf_month)
    if len(configuration) != 0:
        try:
            assert conf_year is not None and isinstance(conf_year, int)
            assert conf_month is not None and isinstance(conf_month, int)
        except AssertionError as asserting_error:
            logging.info(asserting_error)
            raise asserting_error
    task = KubernetesJobOperator(
        image=IMAGE,
        namespace=NAMESPACE,
        image_pull_policy="Always",
        config_file=KUBE_CONFIG_PATH,
        cluster_context=CLUSTER_CONTEXT_EKS,
        in_cluster=False,
        execution_timeout=timedelta(minutes=10),
        command=[
            "python3",
            "-m",
            "usdYieldDisbursement.main",
            "--year",
            f"{conf_year}",
            "--month",
            f"{conf_month}"
        ],
        delete_policy="Always",
        get_logs=True,
        dag=dag,
        task_id=f"Usd-Yield-Disbursement-eks"
    )
    task.pre_execute(context=context)
    task.execute(context=context)

########################################################################################################################

## Start
start = DummyOperator(task_id="Start", dag=dag)

## Mongo to S3 Task python tasks
usd_yield_disbursement = PythonOperator(
    task_id=f"Usd-Yield-Disbursement",
    python_callable=usd_yield_disbursement_k8s_task,
    op_kwargs={
        "configuration": "{{ dag_run.conf }}"
    },
    execution_timeout=timedelta(minutes=30),
    provide_context=True,
    dag=dag,
)

## End
end = DummyOperator(task_id="End", dag=dag)

## Dependency flow
chain(
    start,
    usd_yield_disbursement,
    end,
)
