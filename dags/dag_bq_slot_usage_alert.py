import sys, os
from datetime import timedelta
from airflow.models import Variable
from airflow.utils.helpers import chain
from airflow.operators.python_operator import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from base_dags.base_dag import BASE_DAG

## Variables Init
ROOT_DIR = os.path.dirname(os.path.abspath("__file__"))
Pipeline_Utils_Dir_Path = os.path.join(ROOT_DIR, "dags/pipeline_utils")
sys.path.append(Pipeline_Utils_Dir_Path)
from pipeline_utils.bq_slot_usage import run_bq_slot_usage


# Dag ID name
dag_id = "BQ_SLOT_USAGE"

## Base DAG INIT
base_dag_bigquery_slot_usage = BASE_DAG(
    dag_id=dag_id,
    default_args=run_bq_slot_usage.DEFAULT_ARGS,
    schedule_interval="*/30 * * * *",  # every 30  Minute,
    catchup=False,
    tags=["data-eng", "slack-alert", "bq"],
    team="data-eng"
)

bigquery_performance_dag = base_dag_bigquery_slot_usage.Create_Dag(
    dagrun_timeout=timedelta(minutes=8),
    max_active_runs=1,
    on_failure_callback=run_bq_slot_usage.slack_alert,
)


globals()[dag_id] = bigquery_performance_dag
########################################################################################################################


## Start
start = DummyOperator(task_id="Start", dag=globals()[dag_id])

## Kafka To BQ python tasks
run_bq_performance_report = PythonOperator(
    task_id=f"Run-Bigquery-Performace-Report",
    python_callable=run_bq_slot_usage.main,
    execution_timeout=timedelta(minutes=7),
    dag=globals()[dag_id],
)

end = DummyOperator(task_id="End", dag=globals()[dag_id])

## Dependency flow
chain(
    start,
    run_bq_performance_report,
    end,
)
