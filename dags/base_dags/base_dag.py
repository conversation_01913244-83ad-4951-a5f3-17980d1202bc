import array
import os
import sys
from typing import Dict, Optional, Union, Any, List

from airflow import DAG
from airflow.contrib.sensors.file_sensor import FileSensor
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.providers.amazon.aws.operators.emr_add_steps import EmrAddStepsOperator
from airflow.providers.amazon.aws.operators.emr_create_job_flow import (
    EmrCreateJobFlowOperator,
)
from airflow.providers.amazon.aws.sensors.emr_job_flow import EmrJobFlowSensor
from airflow.providers.amazon.aws.sensors.emr_step import EmrStepSensor
from connections.connect import CreateConnection
from pipeline_utils import EmrBaseDag_Utils

ROOT_DIR = "dags"
PIPELINE_UTILS_DIR_PATH = os.path.join(ROOT_DIR, "pipeline_utils")
CONNECTION_PKG_PATH = os.path.join(ROOT_DIR, "connections")
sys.path.append(PIPELINE_UTILS_DIR_PATH)
sys.path.append(CONNECTION_PKG_PATH)


class BASE_DAG:
    """
    A Base Dag is A wrapper of airflow.DAG which can dynamically
    creates multiple dags for you.
    DAGs essentially act as namespaces for tasks. A task_id can only be
    added once to a DAG.

    :param dag_id: The id of the DAG; must consist exclusively of alphanumeric
        characters, dashes, dots and underscores (all ASCII).
    :param default_args: A dictionary of default parameters to be used
        as constructor keyword parameters when initialising operators.
    :param schedule_interval: Defines how often that DAG runs, this
        timedelta object gets added to your latest task instance's
        execution_date to figure out the next schedule
    :param catchup: Perform scheduler catchup

    **Look up for more parameters**: `Airflow DAG documentation
        <https://airflow.apache.org/docs/apache-airflow/1.10.12/concepts.html>`_
    """

    def __init__(
        self,
        dag_id: str,
        default_args: Optional[Dict],
        schedule_interval: Any,
        catchup: bool,
        tags: Optional[List],
        team: str,
    ) -> None:
        self.dag_id = dag_id
        self.default_args = default_args
        self.schedule_interval = schedule_interval
        self.catchup = catchup
        self.tags = tags
        self.team = team
        self.create_conn = CreateConnection()

    def Create_Dag(self, **kwargs) -> DAG:
        """
        A Create Dag is a method which provides basic dag creation

        :param **kwargs <dict>: Inherit all the dag parameters
        """
        ## Connections INIT
        rpa_alert_str = "rpa_alert"
        if rpa_alert_str in kwargs:
            if kwargs[rpa_alert_str]:
                self.create_conn.rpa_slack_alert()
                del kwargs[rpa_alert_str]
            else:
                self.create_conn.slack_default()
                del kwargs[rpa_alert_str]
        else:
            self.create_conn.slack_default()

        # Custom Slack Alert
        slack_conn_details = "slack_conn_details"
        if slack_conn_details in kwargs:
            conn_id, hook_url = kwargs[slack_conn_details]
            self.create_conn.custom_slack_connection(conn_id=conn_id, hook_url=hook_url)
            del kwargs[slack_conn_details]

        ## DAG INIT
        read_only_role = "{}-read-only-role".format(self.team)
        edit_role = "{}-edit-role".format(self.team)
        dag = DAG(
            dag_id=self.dag_id,
            default_args=self.default_args,
            schedule_interval=self.schedule_interval,
            catchup=self.catchup,
            tags=self.tags,
            access_control={
                edit_role: {"can_read", "can_edit"},
                read_only_role: {"can_read"},
            },
            **kwargs,
        )
        return dag

    def Create_EMR_Dag(self, **kwargs) -> Any:
        """
        A Create EMR Dag is a method  which provides basic dag
        creation with creating a cluster,
        waiting it to up and start hadoop debugging step

        :param **kwargs <dict>: Inherit all the dag parameters
        """
        # assign arguments
        emr_core = kwargs.get("emr_core")
        script_bootstrap_action_file = kwargs.get("script_bootstrap_action_file")
        env = kwargs.get("env")
        region = kwargs.get("region")
        concurrency = kwargs.get("concurrency")
        log_uri_bucket = kwargs.get("log_uri_bucket")
        resource_bucket = kwargs.get("resource_bucket")
        emr_key_pair = kwargs.get("emr_key_pair")
        master_security_group = kwargs.get("master_security_group")
        slave_security_group = kwargs.get("slave_security_group")
        service_access_security_group = kwargs.get("service_access_security_group")
        aws_conn_id = kwargs.get("aws_conn_id")
        emr_conn_id = kwargs.get("emr_conn_id")
        subnet_id = kwargs.get("subnet_id")
        ec2_instance_type_config = kwargs.get("ec2_instance_type_config")
        key_to_delete = [
            "emr_core",
            "script_bootstrap_action_file",
            "env",
            "region",
            "concurrency",
            "log_uri_bucket",
            "resource_bucket",
            "emr_key_pair",
            "master_security_group",
            "slave_security_group",
            "service_access_security_group",
            "subnet_id",
            "aws_conn_id",
            "emr_conn_id",
            "ec2_instance_type_config"
        ]
        for i in key_to_delete:
            if kwargs[i]:
                del kwargs[i]

        ## Connections INIT
        self.create_conn.aws_default(region, aws_conn_id)
        self.create_conn.emr_default(emr_conn_id)
        self.create_conn.slack_default()

        ## DAG INIT
        read_only_role = "{}-read-only-role".format(self.team)
        edit_role = "{}-edit-role".format(self.team)
        dag = DAG(
            dag_id=self.dag_id,
            default_args=self.default_args,
            schedule_interval=self.schedule_interval,
            catchup=self.catchup,
            tags=self.tags,
            access_control={
                edit_role: {"can_read", "can_edit"},
                read_only_role: {"can_read"},
            },
            **kwargs,
        )

        # Create an EMR cluster
        Create_EMR_Cluster = EmrCreateJobFlowOperator(
            task_id="Create-EMR-Cluster",
            job_flow_overrides=EmrBaseDag_Utils.CREATE_CLUSTER(
                self.dag_id,
                concurrency,
                log_uri_bucket,
                resource_bucket,
                emr_key_pair,
                subnet_id,
                ec2_instance_type_config,
                master_security_group,
                slave_security_group,
                service_access_security_group,
                script_bootstrap_action_file,
                env,
                emr_core,
            ),
            aws_conn_id=aws_conn_id,
            emr_conn_id=emr_conn_id,
            dag=dag,
        )

        # IS Cluster Active
        is_cluster_active = EmrJobFlowSensor(
            task_id="IS-CLUSTER-ACTIVE",
            job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
            target_states=["WAITING"],
            aws_conn_id=aws_conn_id,
            dag=dag,
        )

        # Add step Setup_Hadoop_Debugging
        Setup_Hadoop_Debugging = EmrAddStepsOperator(
            task_id="Setup-Hadoop-Debugging",
            job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
            aws_conn_id=aws_conn_id,
            steps=EmrBaseDag_Utils.Setup_Hadoop_Debugging(),
            dag=dag,
        )

        # Wait for the steps to complete
        SENSOR_Setup_Hadoop_Debugging = EmrStepSensor(
            task_id="SENSOR-Hadoop-Debugging",
            job_flow_id="{{ task_instance.xcom_pull(task_ids='Create-EMR-Cluster', key='return_value')}}",
            step_id="{{ task_instance.xcom_pull(task_ids='Setup-Hadoop-Debugging', key='return_value')["
            + str(len(EmrBaseDag_Utils.Setup_Hadoop_Debugging()) - 1)
            + "] }}",
            aws_conn_id=aws_conn_id,
            dag=dag,
        )

        return (
            dag,
            Create_EMR_Cluster,
            is_cluster_active,
            Setup_Hadoop_Debugging,
            SENSOR_Setup_Hadoop_Debugging,
        )