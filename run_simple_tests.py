#!/usr/bin/env python3
"""
Simple test runner for FlashGamesPnL tests that don't require Spark.

This script runs only the tests that don't need a Spark session,
which is useful for quick validation and CI environments where
setting up Spark might be complex.
"""

import sys
import os
import subprocess
from pathlib import Path

def setup_environment():
    """Setup the test environment."""
    # Add src to Python path
    src_path = Path(__file__).parent / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    # Set environment variables for testing
    os.environ["PYTHONPATH"] = str(src_path)

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running {description}:")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

def install_basic_dependencies():
    """Install basic test dependencies (no PySpark)."""
    print("Installing basic test dependencies...")
    cmd = [sys.executable, "-m", "pip", "install", "pytest", "pytest-mock", "python-dateutil", "pytz"]
    return run_command(cmd, "Installing basic test dependencies")

def run_non_spark_tests():
    """Run tests that don't require Spark."""
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/test_flash_games_pnl_no_spark.py",
        "-v", "--tb=short"
    ]
    return run_command(cmd, "Non-Spark tests")

def main():
    """Main function."""
    print("FlashGamesPnL Simple Test Runner")
    print("Running tests that don't require Spark...")
    
    # Setup environment
    setup_environment()
    
    success = True
    
    # Install basic dependencies
    success = install_basic_dependencies() and success
    
    if success:
        # Run non-Spark tests
        success = run_non_spark_tests() and success
    
    if success:
        print("\n✅ All simple tests completed successfully!")
        print("\nTo run the full test suite with Spark integration:")
        print("1. Install Java 11 or compatible version")
        print("2. Run: python run_tests.py --install-deps")
        print("3. Run: python run_tests.py --all")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
