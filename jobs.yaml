JobParameter:
  - key: snapGoldReturns
    value: snapGoldReturns
  - key: kafkaBatchConsumer
    value: kafkaBatchConsumer
  - key: snapBappebtiWallet
    value: snapBappebtiWallet
  - key: snapCashIn
    value: snapCashIn
  - key: snapCashouts
    value: snapCashouts
  - key: cryptoCurrencyPriceConsumer
    value: cryptoCurrencyPriceConsumer
  - key: snapCryptoReturns
    value: snapCryptoReturns
  - key: snapForexReturns
    value: snapForexReturns
  - key: snapFundReturns
    value: snapFundReturns
  - key: snapGlobalStocks
    value: snapGlobalStocks
  - key: globalStockPriceConsumer
    value: globalStockPriceConsumer
  - key: snapAUMAndCashDiff
    value: snapAUMAndCashDiff
  - key: snapIndoStocks
    value: snapIndoStocks
  - key: profitAndLoss
    value: profitAndLoss
  - key: snapPortfolio
    value: snapPortfolio
  - key: structs
    value: structs
  - key: common
    value: common
  - key: asset_mongo
    value: asset_mongo
  - key: masterTransaction
    value: masterTransaction
  - key: cleverTapProfile
    value: cleverTapProfile
  - key: pluangPlusMemberValidity
    value: pluangPlusMemberValidity
  - key: eventsConsumer
    value: eventsConsumer
  - key: yieldCrypto
    value: yieldCrypto
  - key: snapPocketReturns
    value: snapPocketReturns
  - key: recurringOrder
    value: recurringOrder
  - key: aumCalculation
    value: aumCalculation
  - key: gtvCalculation
    value: gtvCalculation
  - key: config
    value: config
  - key: dataValidation
    value: dataValidation
  - key: snapAsset
    value: snapAsset
  - key: pluangMission
    value: pluangMission
  - key: snapLeverage
    value: snapLeverage
  - key : userProfile
    value: userProfile
  - key: yieldForex
    value: yieldForex
  - key: snapAccounts
    value: snapAccounts
  - key: snapMissionFeesReversal
    value: snapMissionFeesReversal
  - key: userDailyStatement
    value: userDailyStatement
  - key: snapPrice
    value: snapPrice
  - key: preDefineWatchLists
    value: preDefineWatchLists
  - key: newrelic
    value: newrelic
  - key: utils
    value: utils
  - key: taxReports
    value: taxReports
  - key: userPriceTiering
    value: userPriceTiering
  - key: copyToHdfs
    value: copyToHdfs
  - key: tokopediaUsersGoldMigration
    value: tokopediaUsersGoldMigration
  - key: cryptoFutures
    value: cryptoFutures
  - key: tradingCompetition
    value: tradingCompetition
  - key: option_fee_waiver
    value: option_fee_waiver
  - key: customer_risk_rating
    value: customer_risk_rating