JobParameter:
  - key: requirements
    value: requirements
  - key: BaseDags
    value: BaseDags
  - key: Helpers
    value: Helpers
  - key: Connections
    value: Connections
  - key: PipelineUtils
    value: PipelineUtils
  - key: KubernatesCompetitorExchange
    value: KubernatesCompetitorExchange
  - key: KubernetesGetStreamToBq
    value: KubernetesGetStreamToBq
  - key: KubernatesCryptoTracker
    value: KubernatesCryptoTracker
  - key: S3ToBq
    value: S3ToBq
  - key: MasterTransaction
    value: MasterTransaction
  - key: KubernatesFundingReport
    value: KubernatesFundingReport
  - key: KubernatesCompetitorExchangeHistorical
    value: KubernatesCompetitorExchangeHistorical
  - key: KubernatesP2PReport
    value: KubernatesP2PReport
  - key: big_query
    value: big_query
  - key: json_logger
    value: json_logger
  - key: rpa_gold_vendor_price
    value: rpa_gold_vendor_price
  - key: rpa_voucher_report
    value: rpa_voucher_report
  - key: alerting
    value: alerting
  - key: rpa_forex_prices_daily
    value: rpa_forex_prices_daily
  - key: yield_calculation
    value: yield_calculation
  - key: rpa_akl_report
    value: rpa_akl_report
  - key: rpa_balance_checker
    value: rpa_balance_checker
  - key: rpa_dana_sell_settlement
    value: rpa_dana_sell_settlement
  - key: pluang_data_sync_platform
    value: pluang_data_sync_platform
  - key: frequency_based_recurring_orders_notification_dag
    value: frequency_based_recurring_orders_notification_dag
  - key: portfolio_emr_dag
    value: portfolio_emr_dag
  - key: global_stock_dividend_emr_dag
    value: global_stock_dividend_emr_dag
  - key : dag_pluang_plus_member_calculation
    value: dag_pluang_plus_member_calculation
  - key: gold_aum
    value: gold_aum
  - key: bq_slot_usage
    value: bq_slot_usage
  - key: rpa_transaction_matching
    value: rpa_transaction_matching
  - key: rpa_midtrans_ui_scraping
    value: rpa_midtrans_ui_scraping
  - key: appsflyer
    value: appsflyer
  - key: appsflyer_transaction_sync
    value: appsflyer_transaction_sync
  - key: firebase_transaction_sync
    value: firebase_transaction_sync
  - key: crypto_market_pairs_volume
    value: crypto_market_pairs_volume
  - key: ktpocr_kyc_log_sync
    value: ktpocr_kyc_log_sync
  - key: usd_yield_disbursement
    value: usd_yield_disbursement
  - key: dag_usd_yield_calculation
    value: dag_usd_yield_calculation
  - key: alpaca_yield_mtd
    value : alpaca_yield_mtd
  - key: financial_content_es_to_bq
    value: financial_content_es_to_bq
  - key: rdk_transaction_matching
    value: rdk_transaction_matching
  - key: regulatory_reporting
    value: regulatory_reporting
  - key: tax_report
    value: tax_report
  - key : crypto_sync_advanced_orders
    value : crypto_sync_advanced_orders
  - key: crypto_migration_script
    value: crypto_migration_script
  - key: app_store_review
    value: app_store_review
  - key: ohlc_price_stats_sync_mongo_to_s3
    value: ohlc_price_stats_sync_mongo_to_s3
  - key: elastic_log_snapshot
    value: elastic_log_snapshot
  - key: krisflyer_automation
    value: krisflyer_automation
  - key: pep_checking_automation
    value: pep_checking_automation
  - key: trading_competition_emr_dag
    value: trading_competition_emr_dag
  - key: pluang_bq_asset_analyser
    value: pluang_bq_asset_analyser
  - key: crypto_futures_funding_time_validation
    value: crypto_futures_funding_time_validation
  - key: customer_risk_rating
    value: customer_risk_rating
  - key: pluang_custom_operators
    value: pluang_custom_operators
  - key: kafka_batch_producer
    value: kafka_batch_producer