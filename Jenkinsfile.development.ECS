import groovy.json.*
import java.net.URL
@Library('pluang-shared-lib')_

def environ = "development"

//def jobMappingJsonUrl = "https://raw.githubusercontent.com/emasdigi/pluang-airflow-cron-jobs/6b310138accc332f138549b2e57f77ffb8a33f29/job_mapping.json?token=GHSAT0AAAAAAB6WXD7RBGC4NF5E357I5PCIZAITDYQ"
//def jobMappingJson = new groovy.json.JsonSlurperClassic().parseText(new URL(jobMappingJsonUrl).text)

pipeline {
    environment {
        Repo_name = "${DeploymentName}-${environ}"
        environ = "development"
        DeploymentName = "pluang-portfolio-snapshot-jobs"
        ProjectName = "${DeploymentName}"
        S3AppConfig = "${DeploymentBucket}/appconfig/env/${environ}/${DeploymentName}"
        S3GlobalConfig = "${DeploymentBucket}/jenkins"
        ScriptPath = ""
        ConfigDestination = ""
        VALUES = ""
        ChartName = "core_backend"
        ChartGlobalValues = "${ChartName}/values.${environ}.yaml"
        ChartAppValues = "values/${environ}/${DeploymentName}/values.${environ}.${DeploymentName}.yaml"
        EcrPolicyFile = "file://${ToolsRepoName}/${DeploymentBucket}/global/image-policy.json"
        REPOSITORY = "${ECRURL}/${Repo_name}"
        ImageTag = "${ECRURL}/${Repo_name}:${env.BUILD_ID}"
        BuildArgs = "--build-arg ENVIRONMENT=${environ}"
        git_url = "https://github.com/emasdigi/pluang-spark-jobs.git"
        BucketName = "de-pluang-snapshot-resources-dev-jkt"
    }
    options {
        ansiColor('xterm')
        buildDiscarder logRotator(
            daysToKeepStr: "5",
            numToKeepStr: "5"
        )
        preserveStashes()
        timestamps()
    }
    agent {
        ecs {
            label 'node-agent'
            inheritFrom 'node-agent'
            }
    }
    parameters {
        gitParameter (
            name: 'BRANCH_TAG',
            type: 'PT_BRANCH_TAG',
            branchFilter: 'origin/(.*)',
            defaultValue: "${environ}",
            quickFilterEnabled: true
        )
        booleanParam(
            defaultValue: false,
            description: 'For testing env',
            name: 'Test'
        )
        choice( name: 'helmDeploy', choices: 'no\nyes', description: 'choose the asset class' )
    }
    stages {
        stage ('Checkout') {
            steps {
                script {

                        git branch: "${params.BRANCH_TAG}",
                            credentialsId: 'GitCreds',
                            url: "${git_url}"

                }
            }
        }
        stage ('Cloning Tools') {
            steps {
                script {
                        getVersions()
                        sh """
                            rm -rf devops-tools*
                        """

                }
                dir ('devops-tools') {

                        git branch: 'master',
                            credentialsId: 'GitCreds',
                            url: "${ToolsRepo}"
                        sh """
                            mkdir ${DeploymentBucket}
                            aws s3 sync s3://${S3GlobalConfig}/ ${DeploymentBucket}/
                            aws s3 sync s3://${S3AppConfig}/ ${DeploymentBucket}/
                        """

                }
            }
        }
        stage('CreateDynamicParameter') {
          steps {
            script {
              def props = readJSON file: 'job_mapping.json'
              def jobNames = props.keySet().toList().join(',')
              echo "${jobNames}"
              def jobNameParam = extendedChoice(
                name: 'JOB_NAME',
                description: 'Select the job name to run',
                multiSelectDelimiter: ',',
                quoteValue: false,
                visibleItemCount: 5,
                type: 'PT_CHECKBOX',
                value: jobNames
              )
              properties([
                parameters([
                  jobNameParam,
                  gitParameter (
                    name: 'BRANCH_TAG',
                    type: 'PT_BRANCH_TAG',
                    branchFilter: 'origin/(.*)',
                    defaultValue: "${environ}",
                    quickFilterEnabled: true
                ),
                booleanParam(
                    defaultValue: false,
                    description: 'For testing env',
                    name: 'Test'
                ),
                choice( name: 'helmDeploy', choices: 'no\nyes', description: 'choose the asset class' )
                ])
              ])
            }
          }
        }
        stage('Upload Files To S3') {
            when {
                expression { params.JOB_NAME }
            }
            steps {
                script {
                    def props = readJSON file: 'job_mapping.json'
                    params['JOB_NAME'].split(",").each { job ->
                        echo job
                        def job_prop =  props[job]
                        echo job_prop.toString()
                        job_prop['files'].each { file ->
                            if ( job_prop['destFolder'] == '') {
                                sh "aws s3 cp ${job_prop['srcFolder']}/${file} s3://${BucketName}/dev-pipeline/${file}"
                            }
                            else
                            {
                                sh "aws s3 cp ${job_prop['srcFolder']}/${file} s3://${BucketName}/dev-pipeline/${job_prop['destFolder']}/${file}"
                            }
                        }
                    }
                }
            }
        }
    }
    post {
        success {
            sendNotifications "SUCCESS", "${environ}", 'NO', params.Test
        }
        failure {
            sendNotifications "FAILED", "${environ}", 'NO', params.Test
        }
    }
}