import uuid
from common import *
from config import *
from typing import Dict, List, Optional, Set
from google.cloud import bigquery
from google.oauth2 import service_account
class CustomerRiskRating:
    """Class to process Customer Risk Rating."""

    ASSET_CLASSES_DICT: Dict[str, List[str]] = {
        "WALLET": ["CASHIN", "CASHOUT"],
        "CRYPTO": ["CRYPTO"],
        "FOREX": ["FOREX", "TOPUP", "CASHOUT"],
        "FUND": ["FUND"],
        "GOLD": ["GOLD"],
        "GSS": ["GSS", "OPTIONS"]
    }

    def __init__(self):
        logging.info("Initializing CustomerRiskRating class")
        self.offset = config_data["offset"]
        self.base_url: str = f"s3a://{config_data['bucket']}"
        self.bq_table_id = f"{config_data['bq_project']}.{config_data['customer_risk_rating']['bq_dataset']}.{job_config_data['customer_risk_rating']['bq_table_name']}"
        self.bq_client = self._create_bigquery_client()
        self.last_day_last_month = get_date_for_query(self.offset).date()
        self.last_day_last_month_date: str = f"dt={self.last_day_last_month}"
        self.directories: Dict[str, str] = self._initialize_directories()
        self.asset_amount_columns: Set[str] = set()
        self.first_day_last_month = str(self.last_day_last_month.replace(day=1))
        self.last_day_last_month = str(self.last_day_last_month)
        logging.info(f"Filtering data from {self.first_day_last_month} and to {self.last_day_last_month}")
        self.spark: SparkSession = spark_session_create("CustomerRiskRating")

    

    def _create_bigquery_client(self) -> bigquery.Client:
        """
        Create and return a BigQuery client.

        Returns:
            bigquery.Client: A BigQuery client object.
        """
        logging.info("Creating BigQuery client")
        try:
            service_account_file_path: str = config_data["bq_service_account_file_path"]
            with open(service_account_file_path, 'r') as f:
                service_account_info: Dict = json.load(f)
            google_credentials = service_account.Credentials.from_service_account_info(service_account_info)
            bq_client = bigquery.Client(credentials=google_credentials)
            logging.info("BigQuery client created successfully")
            return bq_client
        except Exception as e:
            logging.error(f"Error creating BigQuery client: {e}")
            raise


    def _initialize_directories(self) -> Dict[str, str]:
        """Initialize directories for asset classes."""
        logging.info("Initializing directories for asset classes")
        dirs: Dict[str, str] = {}
        asset_classes = [
            "cashin_folder", "cashouts_folder",  
            "aum_calculation_assets", "pluang_users_ojk_kyc_documents_snapshot", 
            "customer_risk_rating_folder"
        ]
        for asset_class in asset_classes:
            if asset_class == "aum_calculation_assets":
                for key in ["global_stock_options", "global_stock", "crypto_currency", "forex", "forex_topup",
                "forex_cashout", "fund", "gold"]:
                    dirs[key] = os.path.join(self.base_url, "aum_calculation", job_config_data["customer_risk_rating"][asset_class][key], self.last_day_last_month_date)
            else:
                dirs[asset_class] = os.path.join(self.base_url, job_config_data["customer_risk_rating"][asset_class], self.last_day_last_month_date)
        logging.info(f"Directories initialized: {dirs}")
        return dirs

    def generate_asset_class_columns(self) -> Set[str]:
        """Generate asset class columns."""
        logging.info("Generating asset class columns")
        if not self.asset_amount_columns:
            for k, v in self.ASSET_CLASSES_DICT.items():
                for i in v:
                    k_lower, i_lower = k.lower(), i.lower()
                    if k != i:
                        if k == "WALLET":
                            self.asset_amount_columns.add(f"{k_lower}_{i_lower}_amount")
                        else:
                            self.asset_amount_columns.add(f"{k_lower}_{i_lower}_buy_amount")
                            self.asset_amount_columns.add(f"{k_lower}_{i_lower}_sell_amount")
                    else:
                        self.asset_amount_columns.add(f"{i_lower}_buy_amount")
                        self.asset_amount_columns.add(f"{i_lower}_sell_amount")
        logging.info(f"Asset class columns generated: {self.asset_amount_columns}")
        return self.asset_amount_columns

    def load_transactions(self, dir_path: str, asset_class: str, asset_sub_class: Optional[str] = None, transaction_types: Optional[List[str]] = None) -> DataFrame:
        """Load and process transactions."""
        logging.info(f"Loading transactions from {dir_path} for asset class {asset_class}")
        df = read_csv_file(path=dir_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None)
        
        date_column = "transaction_time" if "transaction_time" in df.columns else "updated"
        df = df.filter((col(date_column) >= self.first_day_last_month) & (col(date_column) <= self.last_day_last_month))
        logging.info(f"Filtering transactions for asset class {asset_class} and asset sub-class {asset_sub_class} from {self.first_day_last_month} to {self.last_day_last_month}")
        
        # Common transformations
        df = df.withColumn("asset_class", lit(asset_class).cast("string"))
        df = df.withColumn("buy_amount", lit(0.0).cast("double"))
        df = df.withColumn("sell_amount", lit(0.0).cast("double"))
        df = df.withColumn("cashin_amount", lit(0.0).cast("double"))
        df = df.withColumn("cashout_amount", lit(0.0).cast("double"))

        asset_class_columns = list(self.generate_asset_class_columns())
        for column in asset_class_columns:
            df = df.withColumn(column, lit(0.0).cast("double"))

        status_filter = col("status").isin(["SUCCESS", "PARTIALLY_FILLED", "COMPLETED"])
        if asset_class == self.ASSET_CLASSES_DICT["GSS"][0] and asset_sub_class == self.ASSET_CLASSES_DICT["GSS"][1] and transaction_types == ["LONG_OPEN", "LONG_CLOSE"]:
            df = df.filter(col("transaction_type").isin(transaction_types) & status_filter)
            df = df.withColumn("buy_amount", when(col("transaction_type") == "LONG_OPEN", col("executed_total_price").cast("double")).otherwise(0.0))
            df = df.withColumn("sell_amount", when(col("transaction_type") == "LONG_CLOSE", col("executed_total_price").cast("double")).otherwise(0.0))
            df = df.withColumn("buy_amount", round(col("buy_amount") * col("usd_to_idr"), 8))
            df = df.withColumn("sell_amount", round(col("sell_amount") * col("usd_to_idr"), 8))
        elif asset_class == self.ASSET_CLASSES_DICT["CRYPTO"][0]:
            df = df.filter(status_filter)
            df = df.withColumn("buy_amount", when(col("transaction_type") == "BUY", col("executed_quantity") * col("executed_unit_price")).otherwise(0.0))
            df = df.withColumn("sell_amount", when(col("transaction_type") == "SELL", col("executed_quantity") * col("executed_unit_price")).otherwise(0.0))
        elif asset_class == self.ASSET_CLASSES_DICT["GSS"][0] and not asset_sub_class:
            df = df.filter(status_filter)
            df = df.withColumn("leverage", when((col("stock_type") == "CFD_LEVERAGE") & (col("trading_hours") == "INTRADAY"), 4). \
                when(col("stock_type") == "CFD_LEVERAGE", 2). \
                otherwise(0).cast("double"))
            df = df.withColumn("buy_amount", when(col("transaction_type") == "BUY", col("executed_quantity") * col("executed_unit_price") / col("leverage")).otherwise(0.0))
            df = df.withColumn("sell_amount", when(col("transaction_type") == "SELL", col("executed_quantity") * col("executed_unit_price") / col("leverage")).otherwise(0.0))
            df = df.withColumn("buy_amount", round(col("buy_amount") * col("usd_to_idr"), 8))
            df = df.withColumn("sell_amount", round(col("sell_amount") * col("usd_to_idr"), 8))
        elif asset_class == self.ASSET_CLASSES_DICT["FOREX"][0] and not asset_sub_class:
            df = df.filter(status_filter)
            df = df.withColumn("buy_amount", when(col("transaction_type") == "BUY", col("quantity") * col("unit_price")).otherwise(0.0))
            df = df.withColumn("sell_amount", when(col("transaction_type") == "SELL", col("quantity") * col("unit_price")).otherwise(0.0))
        elif asset_class == self.ASSET_CLASSES_DICT["FOREX"][0] and asset_sub_class == self.ASSET_CLASSES_DICT["FOREX"][1]:
            df = df.filter(status_filter)
            df = df.withColumn("cashin_amount", col("final_amount").cast("double"))
        elif asset_class == self.ASSET_CLASSES_DICT["FOREX"][0] and asset_sub_class == self.ASSET_CLASSES_DICT["FOREX"][2]:
            df = df.filter(status_filter)
            df = df.withColumn("cashout_amount", col("withdrawal_amount").cast("double"))
        elif asset_class == self.ASSET_CLASSES_DICT["FUND"][0]:
            df = df.filter(status_filter)
            df = df.withColumn("buy_amount", when(col("transaction_type") == "BUY", col("quantity") * col("unit_price")).otherwise(0.0))
            df = df.withColumn("sell_amount", when(col("transaction_type") == "SELL", col("quantity") * col("unit_price")).otherwise(0.0))
        elif asset_class == self.ASSET_CLASSES_DICT["GOLD"][0]:
            df = df.filter(status_filter)
            df = df.withColumn("buy_amount", when(col("transaction_type") == "BUY", col("quantity") * col("unit_price")).otherwise(0.0))
            df = df.withColumn("sell_amount", when(col("transaction_type") == "SELL", col("quantity") * col("unit_price")).otherwise(0.0))
        elif asset_class == "WALLET":
            df = df.filter((col("wallet_type") != "OJK") & status_filter)
            if asset_sub_class == self.ASSET_CLASSES_DICT["WALLET"][0]:
                df = df.withColumn("cashin_amount", col("amount").cast("double"))
            elif asset_sub_class == self.ASSET_CLASSES_DICT["WALLET"][1]:
                df = df.withColumn("cashout_amount", col("amount").cast("double"))

        # Add asset class amount columns to DataFrame
        if asset_class == "WALLET":
            if self.ASSET_CLASSES_DICT["WALLET"][0] == asset_sub_class:
                df = df.withColumn(f"{asset_class.lower()}_{asset_sub_class.lower()}_amount", col("cashin_amount"))
            elif self.ASSET_CLASSES_DICT["WALLET"][1] == asset_sub_class:
                df = df.withColumn(f"{asset_class.lower()}_{asset_sub_class.lower()}_amount", col("cashout_amount"))
        elif not asset_sub_class:
            df = df.withColumn(f"{asset_class.lower()}_buy_amount", col("buy_amount"))
            df = df.withColumn(f"{asset_class.lower()}_sell_amount", col("sell_amount"))
        else:
            df = df.withColumn(f"{asset_class.lower()}_{asset_sub_class.lower()}_buy_amount", col("buy_amount"))
            df = df.withColumn(f"{asset_class.lower()}_{asset_sub_class.lower()}_sell_amount", col("sell_amount"))

        selection_list_of_columns = ["user_id", "account_id", "asset_class", "buy_amount", "sell_amount", "cashin_amount", "cashout_amount"] + list(self.asset_amount_columns)
        logging.info(f"Columns to be selected: {selection_list_of_columns}")
        return df.select(*selection_list_of_columns)

    def map_income_values(self, df: DataFrame) -> DataFrame:
        """Map income levels to start and end values."""
        logging.info("Mapping income levels to start and end values")
        income_start_expr = None
        income_end_expr = None

        for level, (start_value, end_value) in job_config_data["customer_risk_rating"]["income_mapping"].items():
            start_value = lit(str(start_value)).cast(DecimalType(38, 0))
            end_value = lit(str(end_value)).cast(DecimalType(38, 0))
            if income_start_expr is None:
                income_start_expr = when(col("income_level") == level, start_value)
                income_end_expr = when(col("income_level") == level, end_value)
            else:
                income_start_expr = income_start_expr.when(col("income_level") == level, start_value)
                income_end_expr = income_end_expr.when(col("income_level") == level, end_value)
        income_start_expr = income_start_expr.otherwise(None)
        income_end_expr = income_end_expr.otherwise(None)
        df = df.withColumn("income_start", income_start_expr)
        df = df.withColumn("income_end", income_end_expr)
        logging.info("Income levels mapped")
        return df

    def load_ojk_kyc_documents(self) -> DataFrame:
        """Load and process OJK KYC documents."""
        logging.info("Loading OJK KYC documents")
        user_income_df = (
            self.spark.read.csv(self.directories["pluang_users_ojk_kyc_documents_snapshot"], header=True)
            .select("user_id", "income_level")
        )
        default_income_level = "> 10 – 50 MILLION/YEAR"
        user_income_df = user_income_df.withColumn(
            "income_level", when(col("income_level").isNull(), default_income_level).otherwise(col("income_level"))
        )
        user_income_df = self.map_income_values(user_income_df)
        logging.info(f"Loaded {user_income_df.count()} OJK KYC documents")
        return user_income_df

    def combine_all_assets(self, *dataframes: DataFrame) -> DataFrame:
        """Combine all asset transactions."""
        logging.info("Combining all asset transactions")
        combined_df = reduce(lambda df1, df2: df1.union(df2), dataframes)
        logging.info("All asset transactions combined")
        return combined_df

    def post_processing_for_kafka(self, df: DataFrame) -> DataFrame:
        """
        Post-process the DataFrame by filtering and transforming it for Kafka push.

        Args:
            df (DataFrame): Input DataFrame to be processed.

        Returns:
            DataFrame: Processed DataFrame.
        """
        logging.info("Starting post-processing of DataFrame")

        # Filter the account ids whose income is more than income level
        df = df.filter((col("is_topup_cashout_more_than_income") | col("is_buy_sell_more_than_income")))

        all_amount_columns = [f"total_{col}" for col in list(self.asset_amount_columns) + ["buy_amount", "sell_amount", "cashin_amount", "cashout_amount"]]
        amount_details = f.struct(*[col(column).alias(column) for column in all_amount_columns]).alias("amount_details")
        df = df.withColumn("amount_details", amount_details)

        # Transform DataFrame for Kafka push
        df = df.withColumn(
            "actor", f.struct(lit("admin").alias("type"), lit(1002).alias("id"))
        ).withColumn(
            "data", f.struct(
                col("user_id").cast("int").alias("user_id"), col("account_id").cast("int").alias("account_id"), col("income_level"), 
                col("is_topup_cashout_more_than_income"), col("is_buy_sell_more_than_income"), col("amount_details")
            )
        ).withColumn(
            "object", f.struct(
                lit("income_level_case").alias("type"), col("data")
            )
        ).withColumn("target", f.struct(lit("user").alias("type"), col("user_id").cast("int").alias("id"))
        ).withColumn("verb", lit("update")).withColumn("x-request-id", f.expr("uuid()"))
        df = df.withColumn("loggerContext", f.struct(col("x-request-id")))
        return df.select("user_id", "actor", "object", "target", "verb", "loggerContext")


    def execute(self):
        """Process assets and join with OJK KYC Documents."""
        logging.info("Executing..")

        # Load all asset data
        asset_dataframes = self.load_all_assets()

        # Combine all asset data
        all_asset_df = self.combine_all_assets(*asset_dataframes)

        # Aggregate data
        aggregated_df = self.aggregate_asset_data(all_asset_df)

        # Calculate amount differences
        amount_difference_df = self.calculate_amount_differences(aggregated_df)

        # Load user income data
        user_income_df = self.load_ojk_kyc_documents()

        # Join and process data
        master_df = self.join_and_process_data(amount_difference_df, user_income_df)

        # Post-process data for Kafka and write to S3
        s3_file_path = self.post_process_and_write_to_s3(master_df)

        # List files in S3
        files_list = self.list_s3_files(s3_path=s3_file_path)

        # Insert execution history into BigQuery
        self.insert_execution_history(s3_file_list=files_list)

    def load_all_assets(self) -> List[DataFrame]:
        """Load all asset data."""
        logging.info("Loading all asset data")
        return [
            self.load_transactions(dir_path=self.directories["cashin_folder"], asset_class="WALLET", asset_sub_class=self.ASSET_CLASSES_DICT["WALLET"][0]),
            self.load_transactions(dir_path=self.directories["cashouts_folder"], asset_class="WALLET", asset_sub_class=self.ASSET_CLASSES_DICT["WALLET"][1]),
            self.load_transactions(dir_path=self.directories["crypto_currency"], asset_class=self.ASSET_CLASSES_DICT["CRYPTO"][0]),
            self.load_transactions(dir_path=self.directories["forex"], asset_class=self.ASSET_CLASSES_DICT["FOREX"][0]),
            self.load_transactions(dir_path=self.directories["forex_topup"], asset_class=self.ASSET_CLASSES_DICT["FOREX"][0], asset_sub_class=self.ASSET_CLASSES_DICT["FOREX"][1]),
            self.load_transactions(dir_path=self.directories["forex_cashout"], asset_class=self.ASSET_CLASSES_DICT["FOREX"][0], asset_sub_class=self.ASSET_CLASSES_DICT["FOREX"][2]),
            self.load_transactions(dir_path=self.directories["fund"], asset_class=self.ASSET_CLASSES_DICT["FUND"][0]),
            self.load_transactions(dir_path=self.directories["gold"], asset_class=self.ASSET_CLASSES_DICT["GOLD"][0]),
            self.load_transactions(dir_path=self.directories["global_stock"], asset_class=self.ASSET_CLASSES_DICT["GSS"][0]),
            self.load_transactions(dir_path=self.directories["global_stock_options"], asset_class=self.ASSET_CLASSES_DICT["GSS"][0], asset_sub_class=self.ASSET_CLASSES_DICT["GSS"][1], transaction_types=["LONG_OPEN", "LONG_CLOSE"])
        ]

    def aggregate_asset_data(self, df: DataFrame) -> DataFrame:
        """Aggregate asset data."""
        logging.info("Aggregating asset data")
        grouping_columns = ["user_id", "account_id", "asset_class"]
        columns_to_aggregate = list(self.asset_amount_columns) + ["buy_amount", "sell_amount", "cashin_amount", "cashout_amount"]
        sum_expressions = [round(sum(col(column)), 2).alias(f"total_{column}") for column in columns_to_aggregate]
        return df.groupBy(*grouping_columns).agg(*sum_expressions)

    def calculate_amount_differences(self, df: DataFrame) -> DataFrame:
        """Calculate amount differences."""
        logging.info("Calculating amount differences")
        return df.withColumn("difference_buy_sell", col("total_buy_amount") - col("total_sell_amount")).withColumn("difference_topup_cashout", col("total_cashin_amount") - col("total_cashout_amount"))

    def join_and_process_data(self, asset_df: DataFrame, income_df: DataFrame) -> DataFrame:
        """Join and process asset and income data."""
        logging.info("Joining and processing asset and income data")
        joined_df = asset_df.join(income_df, on=["user_id"], how="left")
        return self.add_income_code_column(joined_df)

    def post_process_and_write_to_s3(self, df: DataFrame) -> str:
        """Post-process data for Kafka and write to S3."""
        logging.info("Post-processing data for Kafka and writing to S3")
        processed_df = self.post_processing_for_kafka(df)
        return self.push_to_s3(processed_df)

    def is_first_of_month_jakarta(self) -> bool:
        """Check if today is the first of the month in Jakarta timezone."""
        jakarta_now = datetime.now(tz=pytz.timezone("Asia/Jakarta"))
        return jakarta_now.day == 1

    def push_to_s3(self, df: DataFrame) -> str:
        """
        Push DataFrame to S3 and update tracking file.

        Args:
            df (DataFrame): DataFrame to be written to S3.
        """
        logging.info("Pushing DataFrame to S3")
        try:
            data_count = df.count()
            logging.info(f"Data count: {data_count}")
            part_file_batch_size = job_config_data["customer_risk_rating"]["part_file_batch_size"]
            num_partitions = max(1, data_count // part_file_batch_size)  # Creates partitions based on every 15K records
            logging.info(f"Number of partitions: {num_partitions}, Batch Size per Partition: {part_file_batch_size}")
            s3_path = self.directories['customer_risk_rating_folder']
            df.repartition(num_partitions).write.mode("overwrite").json(s3_path)
            logging.info(f"DataFrame successfully written to S3 at {s3_path} with {num_partitions} partitions")
            return s3_path
        except Exception as e:
            logging.error(f"Error pushing DataFrame to S3: {e}")
            raise

    def list_s3_files(self, s3_path: str) -> List[str]:
        """
        List all files in the given S3 path.

        Args:
            s3_path (str): The S3 path to list files from.

        Returns:
            List[str]: A list of file paths.
        """
        logging.info(f"Listing files in S3 path: {s3_path}")
        files_df = self.spark.read.format("csv").load(s3_path)
        files_list = files_df.inputFiles()
        logging.info(f"Files located in {s3_path}: {files_list}")
        return files_list

    def get_forex_usd_prices(self) -> float:
        """Get Forex Prices for USD Price Rate."""
        logging.info("Fetching Forex USD Prices")
        current_forex_price = get_current_forex_price(
            config_data["forex"]["partner_id"],
            config_data["forex"]["forex_id"],
            config_data["forex"]["asset_name"]
        )
        partner_mid_price = current_forex_price.collect()[0]["mid_price"]
        logging.info(f"Forex USD Price: {partner_mid_price}")
        if not partner_mid_price:
            logging.exception("USD Price not found!")
            raise Exception("USD Price not found")
        return partner_mid_price

    def add_income_code_column(self, df: DataFrame) -> DataFrame:
        """Add income code column based on conditions."""
        logging.info("Adding income code column based on conditions")
        income_multiplier = lit(job_config_data["customer_risk_rating"]["income_multiplier"])
        df = df.withColumn(
            "is_topup_cashout_more_than_income",
            when(col("income_end").isNotNull(), (col("difference_topup_cashout") > (income_multiplier * col("income_end")))).otherwise(False).cast("boolean")
        ).withColumn(
            "is_buy_sell_more_than_income",
            when(col("income_end").isNotNull(), (col("difference_buy_sell") > (income_multiplier * col("income_end")))).otherwise(False).cast("boolean")
        )
        return df

    def insert_execution_history(self, s3_file_list: List[str]) -> None:
        """
        Insert execution history record into BigQuery.

        Args:
            s3_file_list (List[str]): List of S3 file paths to be processed.
        """
        execution_id_str = str(uuid.uuid4())
        record = {
            "execution_id": execution_id_str,
            "execution_type": CustomerRiskRating.__name__,
            "transaction_date": str(self.last_day_last_month),
            "execution_time": datetime.utcnow().isoformat(),
            "s3_files_to_process": str(s3_file_list),
            "s3_files_processed": str([]),
            "crr_module": True,
            "crr_producer_runs": 0,
            "crr_producer_module": False
        }
        query = f"""
        MERGE INTO `{self.bq_table_id}` AS target
        USING (
        SELECT
            "{record['execution_id']}" AS execution_id,
            "{record['execution_type']}" AS execution_type,
            DATE("{record['transaction_date']}") AS transaction_date,
            TIMESTAMP("{record['execution_time']}") AS execution_time,
            "{record['s3_files_to_process']}" AS s3_files_to_process,
            "{record['s3_files_processed']}" AS s3_files_processed,
            {record['crr_module']} AS crr_module,
            {record['crr_producer_runs']} AS crr_producer_runs,
            {record['crr_producer_module']} AS crr_producer_module
        ) AS source
        ON target.execution_id = source.execution_id
        WHEN MATCHED THEN
        UPDATE SET
            execution_type = source.execution_type,
            transaction_date = source.transaction_date,
            execution_time = source.execution_time,
            s3_files_to_process = source.s3_files_to_process,
            s3_files_processed = source.s3_files_processed,
            crr_module = source.crr_module,
            crr_producer_runs = source.crr_producer_runs,
            crr_producer_module = source.crr_producer_module
        WHEN NOT MATCHED THEN
        INSERT (
            execution_id,
            execution_type,
            transaction_date,
            execution_time,
            s3_files_to_process,
            s3_files_processed,
            crr_module,
            crr_producer_runs,
            crr_producer_module
        ) VALUES (
            source.execution_id,
            source.execution_type,
            source.transaction_date,
            source.execution_time,
            source.s3_files_to_process,
            source.s3_files_processed,
            source.crr_module,
            source.crr_producer_runs,
            source.crr_producer_module
        );
        """
        try:
            self.bq_client.query(query).result()
            logging.info("Execution history inserted successfully")
        except Exception as e:
            logging.error(f"Exception occurred while inserting execution history: {e}")
            raise


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job", default=1)
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data["offset"] = int(offset)
    logging.info("Starting main function")
    customer_risk_rating = CustomerRiskRating()
    customer_risk_rating.execute()
    logging.info("Done[+]")
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "Customer-Risk-Rating")
