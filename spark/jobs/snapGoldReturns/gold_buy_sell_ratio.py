from common import *
from structs import *

spark = spark_session_create("gold_buy_sell_ratio")
def get_gold_loan_transactions(t_1):
    df_1 = read_json_data("{}{}/dt={}/".format(config_data["gold_metrics"]["raw_bucket"], config_data["gold_metrics"]["gold_loan_raw_bucket_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_gold_loans)
    df_1 = df_1.filter(col("status") == "PAID_OFF")
    df_1 = df_1.withColumn("transaction_type", lit("BUY"))
    df_1 = df_1.select(["id","partner_id","account_id", "gold_loan_amount", "issued_price", "updated", "status", "transaction_type"])

    df_1 = df_1.withColumnRenamed("gold_loan_amount", "quantity")
    df_1 = df_1.withColumnRenamed("issued_price", "unit_price")
    return df_1


def get_gold_gift_transactions(t_1):
    df_1 = read_json_data("{}{}/dt={}/".format(config_data["gold_metrics"]["raw_bucket"], config_data["gold_metrics"]["gold_gift_raw_bucket_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_gold_gift_transactions)
    df_1 = df_1.withColumn("partner_id", lit(1000002))
    df_1 = df_1.filter(col("status") == "ACCEPTED")
    df_1 = df_1.withColumn("transaction_type", when(col("transaction_type") == "SEND", "SELL").otherwise("BUY"))
    df_1 = df_1.select(["id","partner_id","account_id", "quantity", "unit_price", "updated", "status", "transaction_type"])
    return df_1


def get_gold_withdrawal_transactions(t_1):
    df_1 = read_json_data("{}{}/dt={}/".format(config_data["gold_metrics"]["raw_bucket"], config_data["gold_metrics"]["gold_withdrawals_raw_bucket_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_gold_withdrawals)
    df_1 = df_1.withColumn("partner_id", lit(1000002))
    df_1 = df_1.filter(col("status") == "DELIVERED")
    df_1 = df_1.withColumn("transaction_type", lit("SELL"))
    df_1 = df_1.drop("unit_price")
    df_1 = df_1.withColumnRenamed("sell_price", "unit_price")
    df_1 = df_1.select(["id","partner_id","account_id", "net_amount", "unit_price", "updated", "status", "transaction_type"])
    df_1 = df_1.withColumnRenamed("net_amount", "quantity")
    return df_1


def get_gold_transactions(t_1):
    df_1 = read_json_data("{}{}/dt={}/".format(config_data["gold_metrics"]["raw_bucket"], config_data["gold_metrics"]["gold_transactions_raw_bucket_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_gold_transactions)
    df_1 = df_1.filter(col("status") == "SUCCESS")
    df_1 = df_1.select(["id","partner_id","account_id", "quantity", "unit_price", "updated", "status", "transaction_type"])
    return df_1


def get_percentage(df):
    df = df.withColumn("buy_amount_percentage",
                       col("buy_amount") * 100 / (col("buy_amount") + col("sell_amount")))
    df = df.withColumn("sell_amount_percentage",
                       col("sell_amount") * 100 / (col("buy_amount") + col("sell_amount")))
    df = df.withColumn("buy_quantity_percentage",
                       col("buy_quantity") * 100 / (col("buy_quantity") + col("sell_quantity")))
    df = df.withColumn("sell_quantity_percentage",
                       col("sell_quantity") * 100 / (col("buy_quantity") + col("sell_quantity")))
    df = df.withColumn("buy_amount_percentage", round(col("buy_amount_percentage"), 2))
    df = df.withColumn("sell_amount_percentage", round(col("sell_amount_percentage"), 2))
    df = df.withColumn("buy_quantity_percentage", round(col("buy_quantity_percentage"), 2))
    df = df.withColumn("sell_quantity_percentage", round(col("sell_quantity_percentage"), 2))
    return df


def start_processing():
    zone = pytz.timezone("Asia/Jakarta")
    spark.conf.set("spark.sql.shuffle.partitions",5)
    t_1 = get_date(config_data["offset"])
    df_1_gold_loan = get_gold_loan_transactions(t_1)
    df_1_gold_gift = get_gold_gift_transactions(t_1)
    df_1_gold_withdrawal = get_gold_withdrawal_transactions(t_1)
    df_1_gold_transactions = get_gold_transactions(t_1)
    df_1 = df_1_gold_transactions.union(df_1_gold_loan).union(df_1_gold_gift).union(df_1_gold_withdrawal).coalesce(5)
    primary_keys = ["account_id", "id"]
    window = Window.partitionBy([col(x) for x in primary_keys]).orderBy(col("updated").desc())
    df_1 = df_1.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
    df_1_buy = df_1.filter(col("transaction_type") == "BUY").withColumn("buy_amount",
                                                                        col("quantity") * col("unit_price")).groupBy(
        ["account_id", "partner_id"]).agg(sum("buy_amount").cast(LongType()).alias("buy_amount"),
                                          sum("quantity").alias("buy_quantity"))
    df_1_sell = df_1.filter(col("transaction_type") == "SELL").withColumn("sell_amount",
                                                                          col("quantity") * col("unit_price")).groupBy(
        ["account_id", "partner_id"]).agg(sum("sell_amount").cast(LongType()).alias("sell_amount"),
                                          sum("quantity").alias("sell_quantity"))
    df_1_gtv = df_1_buy.join(df_1_sell, on=["account_id", "partner_id"], how="full")
    df_1_gtv = df_1_gtv.fillna(0)
    df_1_gtv = get_percentage(df_1_gtv)

    created = datetime.strptime(str(t_1), '%Y-%m-%d').replace(hour=00, minute=00, second=00, microsecond=000000)
    df_1_gtv = df_1_gtv.withColumn("created", lit(created))
    df_1_gtv = df_1_gtv.withColumn("buy_quantity", round(col("buy_quantity"), 8))
    df_1_gtv = df_1_gtv.withColumn("sell_quantity", round(col("sell_quantity"), 8))
    df_1_gtv.coalesce(1).write.mode('overwrite').save(config_data["gold_metrics"]["metric_bucket"]+config_data["gold_metrics"]["metric_folder"]+"/"+config_data["gold_metrics"]["buy_sell_ratio_folder"]+"/dt="+str(t_1)+"/",format="csv", header=True)
    df_1_gtv = df_1_gtv.withColumn("asset_id", lit(1))
    df_1_gtv = df_1_gtv.select("asset_id", "partner_id", "buy_amount", "sell_amount", "buy_quantity",
                               "sell_quantity").groupBy(["asset_id", "partner_id"]).agg(
        sum("buy_amount").alias("buy_amount"), sum("buy_quantity").alias("buy_quantity"),
        sum("sell_amount").alias("sell_amount"), sum("sell_quantity").alias("sell_quantity"))
    df_1_gtv = get_percentage(df_1_gtv)
    df_1_gtv = df_1_gtv.select("asset_id", "partner_id", "buy_amount_percentage", "sell_amount_percentage","buy_quantity_percentage", "sell_quantity_percentage")
    df_gtv = df_1_gtv
    for i in range(2, 8):
        t_2 = get_date(i)
        df_2_gtv = spark.read.csv(config_data["gold_metrics"]["metric_bucket"]+config_data["gold_metrics"]["metric_folder"]+"/"+config_data["gold_metrics"]["buy_sell_ratio_folder"]+"/dt="+str(t_2)+"/", header=True,inferSchema=True, quote='"', escape='"', multiLine=True)
        df_2_gtv = df_2_gtv.withColumn("asset_id", lit(1))
        df_2_gtv = df_2_gtv.select("asset_id", "partner_id", "buy_amount", "sell_amount", "buy_quantity","sell_quantity").groupBy(["asset_id", "partner_id"]).agg(sum("buy_amount").alias("buy_amount"), sum("buy_quantity").alias("buy_quantity"),sum("sell_amount").alias("sell_amount"), sum("sell_quantity").alias("sell_quantity"))
        df_2_gtv = get_percentage(df_2_gtv)
        df_2_gtv = df_2_gtv.select("asset_id", "partner_id", "buy_amount_percentage", "sell_amount_percentage","buy_quantity_percentage", "sell_quantity_percentage")
        df_gtv = df_gtv.union(df_2_gtv)
    df_gtv = df_gtv.groupBy(["asset_id", "partner_id"]).agg(sum("buy_amount_percentage").alias("buy_amount_percentage"),
                                                            sum("sell_amount_percentage").alias("sell_amount_percentage"),
                                                            sum("buy_quantity_percentage").alias("buy_quantity_percentage"),
                                                            sum("sell_quantity_percentage").alias("sell_quantity_percentage"),
                                                            count("buy_amount_percentage").alias("cnt"))
    df_gtv = df_gtv.withColumn("buy_amount_percentage_rolling", round(col("buy_amount_percentage") / col("cnt"), 2))
    df_gtv = df_gtv.withColumn("sell_amount_percentage_rolling", round(col("sell_amount_percentage") / col("cnt"), 2))
    df_gtv = df_gtv.withColumn("buy_quantity_percentage_rolling", round(col("buy_quantity_percentage") / col("cnt"), 2))
    df_gtv = df_gtv.withColumn("sell_quantity_percentage_rolling", round(col("sell_quantity_percentage") / col("cnt"),2))
    df_gtv = df_gtv.select("asset_id", "partner_id", "buy_amount_percentage_rolling", "sell_amount_percentage_rolling","buy_quantity_percentage_rolling", "sell_quantity_percentage_rolling")
    df_gtv = df_gtv.join(df_1_gtv, on=["asset_id", "partner_id"], how="full")
    df_gtv = df_gtv.fillna(0)
    df_gtv = df_gtv.filter(col("partner_id") == 1000002)
    df_gtv = df_gtv.withColumn("created", lit(created))
    df_gtv = df_gtv.drop("partner_id", "buy_amount_percentage", "sell_amount_percentage", "buy_amount_percentage_rolling","sell_amount_percentage_rolling")
    df_gtv = df_gtv.withColumn("asset_category", lit("GOLD"))
    no_of_invalid_percentage = df_gtv.filter(((col("buy_quantity_percentage")+col("sell_quantity_percentage")) != 0) & (((col("buy_quantity_percentage")+col("sell_quantity_percentage")) <= 99) | ((col("buy_quantity_percentage")+col("sell_quantity_percentage")) >= 101))).count()
    no_of_invalid_percentage_rolling = df_gtv.filter(((col("buy_quantity_percentage_rolling")+col("sell_quantity_percentage_rolling")) <= 99) | ((col("buy_quantity_percentage_rolling")+col("sell_quantity_percentage_rolling")) >= 101)).count()
    if (no_of_invalid_percentage == 0) and (no_of_invalid_percentage_rolling == 0):
        write_asset_returns_to_mongo(df_gtv, config_data["gold_metrics"]["mongo_buy_sell_ratio"], "Gold Buy Sell Ratio", config_data["gold_metrics"]["mongo_buy_sell_ratio"]["write_format"], config_data["gold_metrics"]["mongo_buy_sell_ratio"]["shardkey"])
        txt = config_data["gold_metrics"]["mongo_buy_sell_ratio"]["uri"]
        config_data["gold_metrics"]["mongo_buy_sell_ratio"]["uri"] = txt.replace("asset_trading_activity", "asset_metrics")
        write_asset_returns_to_mongo(df_gtv, config_data["gold_metrics"]["mongo_buy_sell_ratio"], "Gold Buy Sell Ratio", "update", config_data["gold_metrics"]["mongo_buy_sell_ratio"]["shardkey"])
    else:
        raise Exception("Sorry, number are not adding to 100")


if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"gold_buy_sell_ratio")
