import argparse
from common import *
import threading


spark = spark_session_create("events_batch_consumer")


def read_stream_from_kafka_topic(bootstrap_servers, topics, is_ssl_kafka):
    try:
        logging.info("reading event data from {} kafka topic/topics".format(topics))
        consumer_object = get_kafka_consumer_object(bootstrap_servers, topics, is_ssl_kafka=is_ssl_kafka)
        df = consumer_object.load()
        logging.info("Successfully read event data")
        return df
    except Exception as e:
        logging.exception(e)
        raise e


def transform_raw_stream(df):
    try:
        logging.info("transforming event raw stream data")
        df = df.selectExpr("CAST(value AS STRING)", "topic", "partition", "offset", "timestamp", "CAST(key AS STRING)")
        df = df.withColumn("date", col("timestamp").cast("timestamp"))
        df = df.withColumn("date", from_utc_timestamp(col("date"), "Asia/Jakarta"))
        df = df.withColumn("date", col("date").cast("date"))
        logging.info("successfully transformed event raw stream data")
        return df
    except Exception as e:
        logging.exception(e)
        raise e


def write_raw_stream_to_s3(df, raw_df_s3_path, raw_df_s3_checkpoint_location, asset):
    try:
        logging.info("writing event raw stream data to s3 {} path".format(raw_df_s3_path))
        raw_query = df.writeStream.outputMode("append") \
            .queryName(asset) \
            .format("json") \
            .trigger(once=True) \
            .partitionBy("date").option("header", "true") \
            .option("startingOffsets", "earliest").option("truncate", "false") \
            .option("path", raw_df_s3_path) \
            .option("checkpointLocation", raw_df_s3_checkpoint_location) \
            .option("inferSchema", "true").start()
        raw_query.awaitTermination()
        logging.info("Successfully written event raw stream data to s3")
    except Exception as e:
        logging.exception(e)
        raise e


def write_stream_to_s3(topics, assets, i, bootstrap_servers, bucket, is_ssl_kafka):
    df = read_stream_from_kafka_topic(bootstrap_servers, topics[i], is_ssl_kafka)
    df = transform_raw_stream(df)
    output_path = "s3a://{}/{}/{}/".format(bucket, assets[i], "raw_event_data")
    checkpoint_path = "s3a://{}/{}/{}/".format(bucket, assets[i], "raw_event_data_checkpoint")
    write_raw_stream_to_s3(df, output_path, checkpoint_path, assets[i])


if __name__ == "__main__":
    start_time = datetime.now()
    logging.info("Starting Events Batch Consumer")
    consumers = ["events_batch_consumer"]
    parser = argparse.ArgumentParser()
    parser.add_argument("--consumer_name", help="name of the consumer")
    args = parser.parse_args()
    if args.consumer_name:
        consumers = args.consumer_name.split(",")

    for consumer in consumers:
        topics = config_data[consumer]["topics"]
        if len(topics) == 0:
            continue
        bootstrap_servers = config_data["bootstrap_servers"]
        bucket = config_data["events_batch_consumer"]["bucket"]
        is_multithreading_allowed = 1
        is_ssl_kafka = 1
        assets = config_data[consumer]["output_paths"]
        if config_data[consumer].get("bootstrap_servers") is not None:
            bootstrap_servers = config_data[consumer].get("bootstrap_servers")
        if config_data[consumer].get("bucket") is not None:
            bucket = config_data[consumer].get("bucket")
        if config_data[consumer].get("is_multithreading_allowed") is not None:
            is_multithreading_allowed = config_data[consumer].get("is_multithreading_allowed")
        if config_data[consumer].get("is_ssl_kafka") is not None:
            is_ssl_kafka = config_data[consumer].get("is_ssl_kafka")
        if is_multithreading_allowed == 1:
            threads = []
            for i in range(len(topics)):
                t = threading.Thread(target=write_stream_to_s3, args=(topics, assets, i, bootstrap_servers, bucket, is_ssl_kafka))
                threads.append(t)
                t.start()
            for t in threads:
                t.join()
        else:
            for i in range(len(topics)):
                write_stream_to_s3(topics, assets, i, bootstrap_servers, bucket, is_ssl_kafka)
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "events_batch_consumer")
