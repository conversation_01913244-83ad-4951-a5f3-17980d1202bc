import argparse
from common import *
from config import *
from pyspark.sql.functions import *


class PluangOrderBookProcessor(object):
    
    def __init__(self):
        self.spark = spark_session_create("pluang_crypto_order_book_processor")
        self.job_config = job_config_data["pluang_crypto_order_book_processor"]
    
    def read_raw_data_from_s3(self,offset):
        dt = get_date(offset)
        raw_data_path ="s3a://{}/{}/date={}/*".format(config_data["events_batch_consumer"]["bucket"],self.job_config["read_folder_name"],str(dt))
        try:
            logging.info("reading raw data from path {} inside read_json_data".format(raw_data_path))
            df = self.spark.read.option("recursiveFileLookup", "true").json(raw_data_path)
            return df
        except Exception as e:
            logging.warn("An Error occured while reading data: {}".format(raw_data_path),e)
            return None
    
    def extract_order_book_data(self,df):
        logging.info("Extracting order book data")
        df = df.select(["key","value","timestamp"])
        return df
    
    def write_order_book_data(self,df,offset):
        dt = get_date(offset)
        processed_data_path = "s3a://{}/{}/dt={}/".format(config_data["events_batch_consumer"]["bucket"],self.job_config["processed_data_folder_name"],str(dt))
        df = df.repartition("key")
        df.write.mode("overwrite").partitionBy("key").json(processed_data_path)

    def execute_processor(self,offset):
        raw_data = self.read_raw_data_from_s3(offset)
        if raw_data is not None:    
            order_book_data = self.extract_order_book_data(raw_data)
            self.write_order_book_data(order_book_data,offset)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    start_time = datetime.now()
    offset = config_data["offset"]
    obj = PluangOrderBookProcessor()
    if args.offset:
        offset = int(args.offset)
        obj.execute_processor(offset)
    else:
        obj.execute_processor(offset)
        obj.execute_processor(offset-1)
    
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"pluang_crypto_order_book_processor")
    