from config import *
from common import *


config = job_config_data["user_portfolio_validation"]
spark = spark_session_create("user_current_portfolio_validation")


def custom_transformation(df):
    cols = df.columns
    df = df.withColumn("originalAssetQuantity", col("assetQuantity"))
    if "pendingRewardBalance" in cols:
        df = df.withColumn("assetQuantity", round(col("assetQuantity") + col("pendingRewardBalance"), 9))
    else:
        df = df.withColumn("assetQuantity", round(col("assetQuantity"), 9))
    if "pending_reward_balance" in cols:
        df = df.withColumn("total_quantity", round(col("total_quantity") + col("pending_reward_balance"), 9))
    else:
        df = df.withColumn("total_quantity", round(col("total_quantity"), 9))
    if "weightedCostIdr" in cols:
        df = df.withColumn("calculatedCost", round(col("assetQuantity") * col("weightedCostIdr"), 0))
    else:
        df = df.withColumn("calculatedCost", round(col("assetQuantity") * col("weightedCost"), 0))

    if "weighted_cost_in_idr" in cols:
        df = df.withColumn("calculated_cost", round(col("total_quantity") * col("weighted_cost_in_idr"), 0))
    elif "weighted_cost_idr" in cols:
        df = df.withColumn("calculated_cost", round(col("total_quantity") * col("weighted_cost_idr"), 0))
    else:
        df = df.withColumn("calculated_cost", round(col("total_quantity") * col("weighted_cost"), 0))
    return df


def match_data(df, rtrn, t_1):
    logging.info("reading returns file for the asset to match the data for asset {}".format(rtrn))
    df_returns = spark.read.csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["assets"][rtrn]["snapshot_folder"], t_1), header=True,
                                inferSchema=True)
    join_keys = config["assets"][rtrn]["join_on"]
    de_dupe_keys = config["assets"][rtrn]["de_dupe_on"]
    for i in range(len(join_keys)):
        df = df.withColumnRenamed(de_dupe_keys[i], join_keys[i])
    df_joined = df_returns.join(df, join_keys, how="full")
    df_joined = df_joined.fillna(0)
    if rtrn == "fundReturns":
        df_joined = df_joined.withColumnRenamed("weighted_cost_idr", "weighted_cost_idr_excluded")
        df_joined = df_joined.withColumnRenamed("weightedCostIdr", "weightedCostIdrExcluded")
    if rtrn == "goldReturns":
        df_account = spark.read.csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["assets"][rtrn]["account_folder"], t_1), header=True, inferSchema=True).select(col("account_id"), col("partner_id"), col("gold_balance"))
        df_joined = df_joined.join(df_account, join_keys, how="full").filter(col("total_quantity").isNotNull()).filter(col("partner_id") == config["assets"][rtrn]["partner_id"])
        df_joined = df_joined.fillna(0)
        df_joined = df_joined.withColumnRenamed("total_quantity", "quantity_in_returns").withColumn("total_quantity", col("gold_balance"))
    if rtrn == "cryptoReturns":
        df_account = spark.read.csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["assets"][rtrn]["account_folder"], t_1), header=True, inferSchema=True).select(col("account_id"), col("crypto_currency_id"), col("balance"), col("pending_reward_balance"),col("locked_balance"))
        df_joined = df_joined.join(df_account, join_keys, how="full").filter(col("total_quantity").isNotNull()).fillna({'locked_balance': 0})
        df_joined = df_joined.fillna(0)
        df_joined = df_joined.withColumnRenamed("total_quantity", "quantity_in_returns").withColumn("total_quantity", (col("balance") + col("locked_balance")))
    if rtrn == "usStockReturns":
        df_account = spark.read.csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["assets"][rtrn]["account_folder"], t_1), header=True, inferSchema=True).select(col("account_id"), col("global_stock_id"), col("pending_reward_balance"))
        df_joined = df_joined.join(df_account, join_keys, how="full").filter(col("total_quantity").isNotNull())
        df_joined = df_joined.fillna(0)
        df_joined = df_joined.withColumnRenamed("pending_reward_balance", "pendingRewardBalance")
    cols_to_match_in_portfolio = config["assets"][rtrn]["cols_to_match_in_portfolio"]
    cols_to_match_in_asset_return = config["assets"][rtrn]["cols_to_match_in_asset_return"]
    margin = config["assets"][rtrn]["margin"]
    rounding = config["assets"][rtrn]["rounding"]
    for i in range(len(cols_to_match_in_portfolio)):
        df_joined = df_joined.withColumn(cols_to_match_in_asset_return[i], round(cols_to_match_in_asset_return[i], rounding[i]))
        df_joined = df_joined.withColumn(cols_to_match_in_portfolio[i], round(cols_to_match_in_portfolio[i], rounding[i]))
    df_joined = custom_transformation(df_joined)
    cost_limit = 100
    if rtrn == "goldReturns":
        cost_limit = 1500
        df_joined = df_joined.withColumn("calculated_cost", round(col("quantity_in_returns") * col("weighted_cost"), 0))
    df_joined = df_joined.withColumn("calculated_cost_status", when((f.abs(col("calculated_cost")-col("calculatedCost"))) > cost_limit, "UNMATCHED").otherwise("MATCHED"))
    df_joined = df_joined.withColumn("negative_qty", when(((col("total_quantity") < 0) | (col("assetQuantity") < 0)), "YES").otherwise("NO"))
    for i in range(len(cols_to_match_in_portfolio)):
        df_joined = df_joined.withColumn(cols_to_match_in_asset_return[i]+"_status", when(((f.abs(col(cols_to_match_in_asset_return[i]) - col(cols_to_match_in_portfolio[i]))) > margin[i]), "UNMATCHED").otherwise("MATCHED"))
    filter_str = "calculated_cost_status == 'UNMATCHED' or negative_qty == 'YES'"
    for i in range(len(cols_to_match_in_portfolio)):
        filter_str = "{} or {}_status == 'UNMATCHED'".format(filter_str, cols_to_match_in_asset_return[i])
    df_unmatched = df_joined.filter(expr(filter_str))
    if config["assets"][rtrn].get("cols_to_drop") is not None:
        df_unmatched = df_unmatched.drop(config["assets"][rtrn].get("cols_to_drop"))
    df_unmatched.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], config["unmatched_output_path"],config["assets"][rtrn]["current_snapshot_folder"], t_1), header=True)


def extract_data_from_portfolio_for_asset(portfolio_returns, return_col_in_portfolio):
    if portfolio_returns is None:
        return None
    portfolio_returns = portfolio_returns.select("value.*")
    portfolio_returns_for_asset = portfolio_returns.select(col("accountId"), return_col_in_portfolio, "updatedAt", "createdAt")
    portfolio_returns_for_asset = portfolio_returns_for_asset.filter(col(return_col_in_portfolio).isNotNull())
    if portfolio_returns_for_asset.count() == 0:
        return None
    array_item_schema = spark.read.json(portfolio_returns_for_asset.rdd.map(lambda row: row[return_col_in_portfolio])).schema
    json_array_schema = ArrayType(array_item_schema, True)
    portfolio_returns_for_asset = portfolio_returns_for_asset.select(f.from_json(return_col_in_portfolio, json_array_schema).alias(return_col_in_portfolio), col("accountId"), col("updatedAt"), col("createdAt"))
    portfolio_returns_for_asset = portfolio_returns_for_asset.select(f.explode(return_col_in_portfolio).alias(return_col_in_portfolio), col("accountId"), col("updatedAt"), col("createdAt"))
    portfolio_returns_for_asset = portfolio_returns_for_asset.select("accountId", return_col_in_portfolio+".*", "updatedAt", "createdAt")
    portfolio_returns_for_asset = portfolio_returns_for_asset.withColumn('updatedAt', f.to_timestamp((col("updatedAt")/1000).cast(TimestampType()), "UTC"))
    portfolio_returns_for_asset = portfolio_returns_for_asset.withColumn('createdAt', col("createdAt").cast(TimestampType()))
    return portfolio_returns_for_asset


def snapshot_assets_and_match(df_delta, rtrn, t_1, t_2):
    logging.info("extracting delta data for asset {}".format(rtrn))
    df_asset_delta = extract_data_from_portfolio_for_asset(df_delta, rtrn)
    asset_return_snapshot_path = "s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], config["current_returns_snapshot_folder"], config["assets"][rtrn]["current_snapshot_folder"], t_2)
    logging.info("reading user current portfolio returns snapshot for asset {} from path {}".format(rtrn, asset_return_snapshot_path))
    df_asset = spark.read.csv(asset_return_snapshot_path, header=True, inferSchema=True)
    if (df_asset_delta is not None) and (df_asset_delta.count() > 0):
        logging.info("delta data found for the asset {}".format(rtrn))
        asset_cols = df_asset.columns
        df_asset_delta = df_asset_delta.select(asset_cols)
        df_asset_union = df_asset.union(df_asset_delta)
    else:
        logging.info("no delta file found for the asset {}".format(rtrn))
        df_asset_union = df_asset

    df_asset_dedupe = de_dupe_dataframe(df_asset_union, config["assets"][rtrn]["de_dupe_on"], "updatedAt")
    asset_return_snapshot_path_t_1 = "s3a://{}/{}/{}/dt={}/".format(config_data["bucket"],
                                                                    config["current_returns_snapshot_folder"],
                                                                    config["assets"][rtrn][
                                                                        "current_snapshot_folder"], t_1)
    logging.info("writing data in the path {}".format(asset_return_snapshot_path_t_1))
    df_asset_dedupe.coalesce(1).write.mode("overwrite").csv(asset_return_snapshot_path_t_1, header=True)
    return df_asset_dedupe


def start_processing():
    logging.info("Starting snapshot of the assets")
    t_1 = get_date(config_data["offset"])
    t_2 = get_date(config_data["offset"] + 1)
    delta_file_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config["user_current_portfolio_delta_folder"], t_1)
    df_delta = read_json_data(delta_file_path)
    all_returns = config["assets"].keys()
    for rtrn in all_returns:
        if rtrn != "ussCFDLeverageReturns":
            df_asset_dedupe = snapshot_assets_and_match(df_delta, rtrn, t_1, t_2)
            if rtrn == "usStockReturns":
                df_asset_cfd_dedupe = snapshot_assets_and_match(df_delta, "ussCFDLeverageReturns", t_1, t_2)
                df_asset_dedupe = df_asset_dedupe.withColumn("totalTradingMarginUsed", lit(0.0))
                cols_of_us_stocks = df_asset_dedupe.columns
                df_asset_cfd_dedupe = df_asset_cfd_dedupe.select(cols_of_us_stocks)
                df_asset_dedupe = df_asset_dedupe.union(df_asset_cfd_dedupe)
            match_data(df_asset_dedupe, rtrn, t_1)


if __name__ == "__main__":
    start_processing()

