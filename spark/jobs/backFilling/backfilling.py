import logging
import argparse
from common import *
from datetime import date, timedelta, datetime, timezone

spark = spark_session_create("backfilling")
def daterange(start_date, end_date):
    for n in range(int((end_date - start_date).days)+1):
        yield start_date + timedelta(n)

def start_main_process(asset):
    if asset == "bappebti_wallet":
        import snap_bappebti_wallet
        snap_bappebti_wallet.start_processing()
    elif asset == "cashin":
        import snap_cashin
        snap_cashin.start_processing()
    elif asset == "cashouts":
        import snap_cashouts
        snap_cashouts.start_processing()
    elif asset == "crypto_returns":
        import snap_crypto_currency_returns
        snap_crypto_currency_returns.start_processing()
    elif asset == "crypto_avg_hold_time":
        import crypto_avg_holding_time
        crypto_avg_holding_time.start_processing()
    elif asset == "crypto_buy_sell_ratio":
        import crypto_buy_sell_ratio
        crypto_buy_sell_ratio.start_processing()
    elif asset == "snap_gold_gift_and_withdrawal":
        import snap_gold_gift_and_withdrawal
        snap_gold_gift_and_withdrawal.start_processing()
    elif asset == "daily_aggregate_profit_loss_value":
        import daily_aggregate_profit_loss_value
        daily_aggregate_profit_loss_value.start_processing()
    elif asset == "daily_pnl":
        import daily_pnl
        daily_pnl.start_processing()
    elif asset == "monthly_pnl":
        import monthly_pnl
        monthly_pnl.start_processing()
    elif asset == "weekly_pnl":
        import weekly_pnl
        weekly_pnl.start_processing()
    elif asset == "quarterly_pnl":
        import quarterly_pnl
        quarterly_pnl.start_processing()
    elif asset == "yearly_pnl":
        import yearly_pnl
        yearly_pnl.start_processing()
    elif asset == "forex_returns":
        import snap_forex_returns
        snap_forex_returns.start_processing()
    elif asset == "fund_returns":
        import snap_fund_returns_usd
        snap_fund_returns_usd.start_processing()
    elif asset == "global_stocks_returns":
        import snap_global_stock_returns
        snap_global_stock_returns.start_processing()
    elif asset == "global_stocks_dividend":
        import snap_global_stock_dividend
        snap_global_stock_dividend.start_processing()
    elif asset == "gold_returns":
        import snap_gold_returns
        snap_gold_returns.start_processing()
    elif asset == "indo_stocks_returns":
        import snap_indo_stock_returns
        snap_indo_stock_returns.start_processing()
    elif asset == "indo_stocks_dividend":
        import snap_indo_stock_dividend
        snap_indo_stock_dividend.start_processing()
    elif asset == "portfolio":
        import snap_portfolio
        snap_portfolio.start_processing()
    else:
        logging.info("Asset Name is incorrect")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--start_date", help="date from which this job should run.")
    parser.add_argument("--end_date", help="date till which this job should run.")
    parser.add_argument("--asset", help="asset for which this job should run.")
    args = parser.parse_args()
    current_date = get_date(0)
    logging.info("Current Date Is : "+str(current_date))
    logging.info("Start Date Is : "+str(args.start_date))
    logging.info("End Date Is : "+str(args.end_date))
    if (args.start_date is not None) & (args.end_date is not None) & (args.start_date != "NA") & (args.end_date != "NA"):
        start_date = datetime.strptime(str(args.start_date), '%Y-%m-%d').date()
        end_date = datetime.strptime(str(args.end_date), '%Y-%m-%d').date()
        asset = args.asset
        for dt in daterange(start_date, end_date):
            execute_date = datetime.strptime(str(dt), '%Y-%m-%d').date()
            logging.info("Execute Date Is : "+str(execute_date))
            offset = int((current_date - execute_date).days)
            logging.info("Offset Is : "+str(offset))
            config_data['offset'] = offset
            start_main_process(asset)
    elif (args.asset is not None) & (args.asset != ""):
        asset = args.asset
        logging.info("Skipping "+asset+" run.")
    else:
        logging.info("Some parameters are missing.")
