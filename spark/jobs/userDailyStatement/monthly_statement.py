from common import *
from config import *

spark = spark_session_create("user_monthly_statement")
config = job_config_data["user_daily_statement"]
t_0 = get_date(config_data["offset"] - 1)
t_1 = get_date(config_data["offset"])


def round_udf(value, precision):
    return f.round(value * pow(10, precision)) / f.pow(10, precision)


def get_currency_to_idr():
    current_forex_price = get_current_forex_price(config_data["forex"]["partner_id"], config_data["forex"]["forex_id"], config_data["forex"]["asset_name"])
    current_forex_price = current_forex_price.select(col("forex_id").alias("partner_forex_id"),
                                                     col("mid_price").alias("partner_mid_price"))
    currency_to_idr = current_forex_price.collect()[0]["partner_mid_price"]
    return currency_to_idr


def get_crypto_codes():
    '''
    :return: returns crypto_currency_id as code and symbol as asset_code
    '''
    logging.info("Reading Crypto codes from kafka")
    df_crypto_code = read_from_kafka_in_memory(config_data["bootstrap_servers"], config_data["kafka_topics"]["crypto_currencies_topic"])
    df_crypto_code = df_crypto_code.select(col("value.id").alias("code"), col("value.symbol").alias("asset_code"), col("value.price_precision").alias("price_precision"), col("value.active").alias("active"), col("value.safety_label").alias("safety_label"), col("value.enable_sell").alias("enable_sell"), col("value.enable_withdrawal").alias("enable_withdrawal"), col("value.updated").alias("updated"))
    df_crypto_code = de_dupe_dataframe(df_crypto_code, ["code"], "updated").drop("updated")
    logging.info("Reading Crypto codes from kafka is successful")
    return df_crypto_code


def get_fund_codes():
    '''
    :return: returns fund_id as code and fund_code as asset_code
    '''
    logging.info("reading fund codes from kafka topic")
    df_fund_code = read_from_kafka_in_memory(config_data["bootstrap_servers"], config_data["kafka_topics"]["funds_topic"])
    df_fund_code = df_fund_code.select(col("value.id").alias("id"), col("value.code").alias("asset_code"), col("value.__source_ts_ms").cast(LongType()).alias("__source_ts_ms"))
    df_fund_code = de_dupe_dataframe(df_fund_code, ["id"], "__source_ts_ms")
    df_fund_code = df_fund_code.withColumnRenamed("id", "code") \
        .select("code", "asset_code")
    logging.info("Successfully read fund codes from kafka topic")
    return df_fund_code


def check_if_already_published():
    '''
    :return: True/False based on if the data for execution date is already published, it is being checked if there is published data in s3 folder
    '''
    flag = False
    try:
        published_events_s3_path = "s3a://{}/{}/dt={}/".format(config_data['bucket'], config['monthly_statement_output_path'], t_1)
        logging.info("reading published data from path {}".format(published_events_s3_path))
        df = spark.read.json(published_events_s3_path)
        cnt = df.count()
        logging.info("already published {} msgs".format(cnt))
        if cnt > 0:
            logging.info("msgs are already published")
            flag = True
    except Exception as e:
        logging.info("msgs are not published yet")
    return flag


def get_asset_statement(df_balance, asset_key_str):
    '''
    :param df_balance: balance of a asset (fetched from asset return snapshot)
    :param asset_key_str: asset key for the asset (usd/fund/gold/crypto_currency)
    :return: returns asset balance in struct format with column ["account_id", "code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl"]
    '''
    # generate monthly statement
    logging.info("Starting the generation of monthly statement for {}".format(asset_key_str))

    cols_for_balance_summary = ["code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl"]
    if asset_key_str == "usd":
        cols_for_balance_summary = ["code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl", "type"]
    df_balance = df_balance.filter((col("code") != "") & (col("code").isNotNull())).withColumn("balance_summary", f.struct(cols_for_balance_summary)) \
        .withColumn("total_equity", col("market_value")) \
        .drop("code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl", "type") \
        .groupBy(["account_id"]).agg(f.collect_list("balance_summary").alias("balance_summary"), f.sum("total_equity").alias("total_equity"))

    # round total equity based on asset type
    if asset_key_str == "usd":
        df_usd_balance = read_csv_file("hdfs:///usd_balance/", None, False, None)
        df_balance = df_balance.join(df_usd_balance, on=["account_id"], how="full") \
            .fillna({'total_usd_margin': 0, 'pending_usd_margin': 0, 'total_usd_cash': 0, 'pending_usd_cash': 0})
        df_balance = df_balance.withColumn("total_equity", round(col("total_equity"), 2))
    else:
        df_balance = df_balance.withColumn("total_equity", col("total_equity").cast(LongType()))
    df_balance = df_balance.withColumn("balance_summary", when(col("balance_summary").isNull(), f.array()).otherwise(col("balance_summary"))) \
        .fillna({'total_equity': 0})
    if asset_key_str == "usd":
        df_balance = df_balance.select("account_id", f.struct(["balance_summary", "total_equity", "total_usd_margin", "pending_usd_margin", "total_usd_cash", "pending_usd_cash"]).alias(asset_key_str))
    else:
        df_balance = df_balance.select("account_id", f.struct(["balance_summary", "total_equity"]).alias(asset_key_str))
    logging.info("successfully generated statement data")
    return df_balance


def validate_and_publish_data(df_statement):
    '''
    :param df_statement: final statement data in json format which will be published to kafka
    :return: None
    '''
    logging.info("Started writing data into kafka topic and s3")

    # filter pluang only users
    df_accounts = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["accounts_path"], t_0), None, False, None) \
        .filter(col("partner_id").isin(config["accounts_partner_id"])) \
        .withColumn("account_id", col("id")) \
        .select(col("account_id").cast(LongType()), col("user_id").cast(LongType()))
    df_statement = df_statement.withColumn("account_id", col("account_id").cast(LongType()))
    df_statement = df_statement.join(df_accounts, on=["account_id"], how="left").filter(col("user_id").isNotNull())

    # add execution date and time for the period
    df_statement = df_statement.withColumn("execution_date", lit(t_1))
    execution_time = get_date_for_query(config_data["offset"] - 1)
    df_statement = df_statement.withColumn("execution_time", lit(execution_time))

    # publish the data into kafka if not already published
    is_already_published = check_if_already_published()
    if not is_already_published:
        # write the data being published into s3
        df_statement = df_statement.withColumn("is_daily_report", lit(False))
        df_statement = df_statement.withColumn("is_monthly_report", lit(True))
        df_statement.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["monthly_statement_output_path"], t_1))
        logging.info("successfully written statement data into s3")

        # check the whitelisted and blacklisted users and filter the users
        logging.info("started writing statement data into kafka topic")
        df_whitelisted_users = read_csv_file("s3a://{}/{}/".format(config_data["bucket"], config["monthly_whitelisted_users"]), None, False, None)
        df_whitelisted_users = df_whitelisted_users.filter(col("is_whitelisted_user") == True)
        if df_whitelisted_users.count() > 0:
            logging.info("whitelisted user list found")
            df_statement = df_statement.join(df_whitelisted_users, on=["user_id"], how="full").filter((col("is_whitelisted_user") == True) & (col("account_id").isNotNull())).drop("is_whitelisted_user")
        else:
            logging.info("whitelisted user list not found")
            df_blacklisted_users = read_csv_file("s3a://{}/{}/".format(config_data["bucket"], config["monthly_blacklisted_users"]), None, False, None)
            df_blacklisted_users = df_blacklisted_users.filter(col("is_blacklisted_user") == True)
            df_statement = df_statement.join(df_blacklisted_users, on=["user_id"], how="full").filter(((col("is_blacklisted_user") == False) | col("is_blacklisted_user").isNull()) & (col("account_id").isNotNull())).drop("is_blacklisted_user")

        # write statement data into kafka
        df_statement = df_statement.withColumn("user_id", col("user_id").cast(LongType()))
        df_statement = df_statement.select(col("user_id").cast(StringType()).alias("key"), f.to_json(f.struct(df_statement.columns)).alias("value"), f.array(f.struct(lit("x-request-id").alias("key"), f.expr("uuid()").cast("binary").alias("value"))).alias("headers"))
        write_data_in_kafka_topic(df_statement, config_data["bootstrap_servers"], config_data["kafka_topics"]["monthly_statement_topic"])
        logging.info("successfully written statement data into kafka")
    else:
        logging.info("Statement data will not be written into kafka")


def get_gold_balance():
    '''
    :return: gold balance obtained from gold return snapshot and price at cutoff time
    '''
    # Gold Balance
    logging.info("Started Calculation of gold balance")
    df_gold_balance = read_csv_file("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], config_data["gold"]["snap"]["asset_folder"], config_data["gold"]["snap"]["files_folder"], t_1), None, False, None)
    df_gold_balance = df_gold_balance.select(col("account_id").cast(LongType()), lit("GOLD").alias("code"),
                                             round(col("total_quantity"), 6).alias("quantity"),
                                             col("weighted_cost").cast(LongType()).alias("avg_buy_price"),
                                             col("unit_price").cast(LongType()).alias("closing_price")) \
        .filter(col("quantity") > 0) \
        .withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType()).alias("market_value")) \
        .withColumn("unrealized_pnl", (col("quantity") * (col("closing_price") - col("avg_buy_price"))).cast(LongType()))
    logging.info("Successfully calculated for gold balance")
    return df_gold_balance


def get_crypto_currency_balance():
    '''
    :return: crypto currency balance obtained from crypto currency return snapshot and price at cutoff time
    '''
    # Crypto Currency Balance
    logging.info("starting calculation of crypto balance")
    df_crypto_snapshot = read_csv_file("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], config_data["crypto_currency"]["snap"]["asset_folder"], config_data["crypto_currency"]["snap"]["files_folder"], t_1), None, False, None)
    df_crypto_snapshot = df_crypto_snapshot.withColumn("total_qty", col("total_quantity") + col("locked_balance"))
    df_crypto_snapshot = df_crypto_snapshot.select(col("account_id").cast(LongType()), col("crypto_currency_id").alias("code"), round(col("total_qty"), 8).alias("quantity"), col("weighted_cost").alias("avg_buy_price"), col("unit_price").alias("closing_price")).filter(col("quantity") > 0)

    df_crypto_pocket_snapshot = read_csv_file("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], job_config_data["crypto_currency_pocket"]["crypto_currency_pocket_returns"]["asset_folder"], job_config_data["crypto_currency_pocket"]["snap_folder"], t_1), None, False, None)
    df_crypto_pocket_snapshot = df_crypto_pocket_snapshot.select(col("account_id").cast(LongType()), col("crypto_currency_id").alias("code"), round(col("total_quantity"), 8).alias("quantity"), col("weighted_cost").alias("avg_buy_price"), col("unit_price").alias("closing_price")).filter(col("quantity") > 0)

    df_crypto_balance = df_crypto_snapshot.union(df_crypto_pocket_snapshot)
    df_crypto_balance = df_crypto_balance.withColumn("avg_buy_price", col("quantity") * col("avg_buy_price"))
    df_crypto_balance = df_crypto_balance.groupBy(["account_id", "code"]).agg(sum("quantity").alias("quantity"), sum("avg_buy_price").alias("avg_buy_price"), count("closing_price").alias("cnt"), sum("closing_price").alias("closing_price"))

    df_crypto_currency_codes = get_crypto_codes()
    df_crypto_balance = df_crypto_balance.join(df_crypto_currency_codes, on=["code"], how="left")

    df_crypto_balance = df_crypto_balance.select("account_id", "code", "active", "safety_label", "enable_sell", "enable_withdrawal", round(col("quantity"), 8).alias("quantity"), (col("avg_buy_price") / col("quantity")).alias("avg_buy_price"), (col("closing_price") / col("cnt")).alias("closing_price"), col("price_precision"), col("asset_code"))
    df_crypto_balance = df_crypto_balance.withColumn("avg_buy_price", round_udf(col("avg_buy_price"), col("price_precision"))) \
        .withColumn("closing_price", round_udf(col("closing_price"), col("price_precision"))) \
        .withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType()).alias("market_value")) \
        .withColumn("unrealized_pnl", (col("quantity")*(col("closing_price") - col("avg_buy_price"))).cast(LongType()))

    count_of_delisted_coin_balances = df_crypto_balance.filter((col("active").isNull()) | (col("active") == False) | ((col("safety_label") == "DELISTED") & (col("enable_sell") == False) & (col("enable_withdrawal") == False))).count()
    if count_of_delisted_coin_balances > 0:
        logging.warning("DE_DATA_VALIDATION_WARNING: number of accounts having balance in delisted coin: {}".format(count_of_delisted_coin_balances))
        df_crypto_balance = df_crypto_balance.filter(~((col("active").isNull()) | (col("active") == False) | ((col("safety_label") == "DELISTED") & (col("enable_sell") == False) & (col("enable_withdrawal") == False))))

    df_crypto_balance = df_crypto_balance.withColumn("code", col("asset_code")).drop("asset_code", "price_precision", "active", "safety_label", "enable_sell", "enable_withdrawal")
    logging.info("Successfully calculated crypto balance")
    return df_crypto_balance


def get_fund_balance():
    '''
    :return: fund balance obtained from fund return snapshot and price at cutoff time
    '''
    # Fund Balance
    logging.info("starting calculation of fund balance")
    currency_to_idr = get_currency_to_idr()
    df_fund_snapshot = read_csv_file("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], config_data["fund"]["snap"]["asset_folder"], config_data["fund"]["snap"]["files_folder"], t_1), None, False, None)
    df_fund_snapshot = df_fund_snapshot.withColumn("unit_price", when(col("currency") == "USD", col("unit_price") * currency_to_idr).otherwise(col("unit_price")))
    df_fund_balance = df_fund_snapshot.select(col("account_id").cast(LongType()), col("fund_id").alias("code"), round(col("total_quantity"), 4).alias("quantity"), col("weighted_cost_idr").cast(LongType()).alias("avg_buy_price"), col("unit_price").cast(LongType()).alias("closing_price")) \
        .filter(col("quantity") > 0) \
        .withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType()).alias("market_value")) \
        .withColumn("unrealized_pnl", (col("quantity")*(col("closing_price") - col("avg_buy_price"))).cast(LongType()))
    df_fund_codes = get_fund_codes()
    df_fund_balance = df_fund_balance.join(df_fund_codes, on=["code"], how="left")
    df_fund_balance = df_fund_balance.withColumn("code", col("asset_code")).drop("asset_code")
    logging.info("Successfully calculated fund balance")
    return df_fund_balance


def get_usd_balance():
    '''
    :return: usd balance obtained from usd return snapshot and price at cutoff time
    '''
    # Global Stock Balance
    logging.info("starting calculation of usd balance")
    df_gss_balance = read_csv_file("hdfs:///global_stock_balance/", None, False, None)
    logging.info("Successfully calculated usd balance")
    return df_gss_balance


def start_processing():

    # Get All Asset Balance
    df_gold_balance = get_gold_balance()
    df_crypto_balance = get_crypto_currency_balance()
    df_fund_balance = get_fund_balance()
    df_usd_balance = get_usd_balance()

    df_gold_balance = cast_to_bigdecimal(df_gold_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True)
    df_crypto_balance = cast_to_bigdecimal(df_crypto_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True)
    df_fund_balance = cast_to_bigdecimal(df_fund_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True)
    df_usd_balance = cast_to_bigdecimal(df_usd_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True)

    # Generate Statements
    df_usd_statement = get_asset_statement(df_usd_balance, "usd")
    df_gold_statement = get_asset_statement(df_gold_balance, "gold")
    df_crypto_currency_statement = get_asset_statement(df_crypto_balance, "crypto_currency")
    df_fund_statement = get_asset_statement(df_fund_balance, "fund")

    # Generate combined statement of all asset
    df_statement = df_usd_statement.join(df_gold_statement, on=["account_id"], how="full") \
        .join(df_crypto_currency_statement, on=["account_id"], how="full") \
        .join(df_fund_statement, on=["account_id"], how="full")
    validate_and_publish_data(df_statement)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    if t_1.month != t_0.month:
        start_processing()
    else:
        logging.info("{} is not last day of the month".format(t_1))
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"user_monthly_statement")

