from common import *
from config import *

spark = spark_session_create("user_daily_statement")
config = job_config_data["user_daily_statement"]
t_0 = get_date(config_data["offset"] - 1)
t_1 = get_date(config_data["offset"])
t_2 = get_date(config_data["offset"] + 1)


def check_if_already_published():
    flag = False
    try:
        published_events_s3_path = "s3a://{}/{}/dt={}/".format(config_data['bucket'], config['daily_statement_output_path'], t_1)
        logging.info("reading published data from path {}".format(published_events_s3_path))
        df = spark.read.json(published_events_s3_path)
        cnt = df.count()
        logging.info("already published {} msgs".format(cnt))
        if cnt > 0:
            logging.info("msgs are already published")
            flag = True
    except Exception as e:
        logging.info("msgs are not published yet")
    return flag


def write_data_to_s3_and_kafka(df_statement):
    is_already_published = check_if_already_published()
    if not is_already_published:
        df_statement = df_statement.withColumn("is_daily_report", lit(True))
        df_statement = df_statement.withColumn("is_monthly_report", lit(False))
        df_statement.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["daily_statement_output_path"], t_1))
        logging.info("successfully written statement data into s3")
        logging.info("started writing statement data into kafka topic")
        df_whitelisted_users = read_csv_file("s3a://{}/{}/".format(config_data["bucket"], config["daily_whitelisted_users"]), None, False, None)
        df_whitelisted_users = df_whitelisted_users.filter(col("is_whitelisted_user") == True)
        if df_whitelisted_users.count() > 0:
            logging.info("whitelisted user list found")
            df_statement = df_statement.join(df_whitelisted_users, on=["user_id"], how="full").filter((col("is_whitelisted_user") == True) & (col("account_id").isNotNull())).drop("is_whitelisted_user")
        else:
            logging.info("whitelisted user list not found")
            df_blacklisted_users = read_csv_file("s3a://{}/{}/".format(config_data["bucket"], config["daily_blacklisted_users"]), None, False, None)
            df_blacklisted_users = df_blacklisted_users.filter(col("is_blacklisted_user") == True)
            df_statement = df_statement.join(df_blacklisted_users, on=["user_id"], how="full").filter(((col("is_blacklisted_user") == False) | col("is_blacklisted_user").isNull()) & (col("account_id").isNotNull())).drop("is_blacklisted_user")
        gss_kyc_t_2_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"],config["snap_gss_kyc_information_path"], str(t_0))
        logging.info("read gss kyc verfied data")
        snap_gss_kyc_information = spark.read.csv(gss_kyc_t_2_path,header=True,inferSchema=True)
        snap_gss_kyc_information = snap_gss_kyc_information.filter(col("to_state")=="VERIFIED").select("user_id")
        df_statement = df_statement.join(snap_gss_kyc_information,on=["user_id"],how="inner")
        df_statement = df_statement.withColumn("user_id", col("user_id").cast(LongType()))
        df_statement = df_statement.select(col("user_id").cast(StringType()).alias("key"), f.to_json(f.struct(df_statement.columns)).alias("value"), f.array(f.struct(lit("x-request-id").alias("key"), f.expr("uuid()").cast("binary").alias("value"))).alias("headers"))
        write_data_in_kafka_topic(df_statement, config_data["bootstrap_servers"], config_data["kafka_topics"]["daily_statement_topic"])
        logging.info("successfully written statement data into kafka")
    else:
        logging.info("Statement data will not be written into kafka")


def get_asset_statement(df_txn, df_balance, asset_key_str):
    # generate statement
    logging.info("Starting the generation of daily statement")
    cols_of_txn = ["account_id", "order_number", "order_type", "code", "quantity", "price", "fee", "total", "type"]
    df_buy_txn = df_txn.filter(col("transaction_type") == "BUY").select(cols_of_txn)
    df_sell_txn = df_txn.filter(col("transaction_type") == "SELL").select(cols_of_txn)
    df_other_txn = df_txn.filter(col("transaction_type") == "OTHER").select(cols_of_txn)

    df_buy_txn = df_buy_txn.filter((col("code") != "") & (col("code").isNotNull())).withColumn("buy_orders", f.struct(["order_number", "order_type", "code", "quantity", "price", "fee", "total", "type"])) \
        .groupBy(["account_id"]).agg(f.collect_list("buy_orders").alias("buy_orders"), f.sum("total").alias("buy_total"))

    df_sell_txn = df_sell_txn.filter((col("code") != "") & (col("code").isNotNull())).withColumn("sell_orders", f.struct(["order_number", "order_type", "code", "quantity", "price", "fee", "total", "type"])) \
        .groupBy(["account_id"]).agg(f.collect_list("sell_orders").alias("sell_orders"), f.sum("total").alias("sell_total"))

    df_other_txn = df_other_txn.filter((col("code") != "") & (col("code").isNotNull())).withColumn("other_orders", f.struct(["order_number", "order_type", "code", "quantity", "price", "fee", "total", "type"])) \
        .groupBy(["account_id"]).agg(f.collect_list("other_orders").alias("other_orders"), f.sum("total").alias("other_total"))

    df_balance = df_balance.filter((col("code") != "") & (col("code").isNotNull())).withColumn("balance_summary", f.struct(["code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl", "type"])) \
        .withColumn("total_equity", col("market_value")) \
        .drop("code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl", "type") \
        .groupBy(["account_id"]).agg(f.collect_list("balance_summary").alias("balance_summary"), f.sum("total_equity").alias("total_equity"))
    if asset_key_str == "usd":
        df_usd_balance = read_csv_file("hdfs:///usd_balance/", None, False, None)
        df_balance = df_balance.join(df_usd_balance, on=["account_id"], how="full") \
            .fillna({'total_usd_margin': 0, 'pending_usd_margin': 0, 'total_usd_cash': 0, 'pending_usd_cash': 0})
        df_balance = df_balance.withColumn("total_equity", round(col("total_equity"), 2))
        df_buy_txn = df_buy_txn.withColumn("buy_total", round(col("buy_total"), 2))
        df_sell_txn = df_sell_txn.withColumn("sell_total", round(col("sell_total"), 2))
        df_other_txn = df_other_txn.withColumn("other_total", round(col("other_total"), 2))
    else:
        df_balance = df_balance.withColumn("total_equity", col("total_equity").cast(LongType()))
        df_buy_txn = df_buy_txn.withColumn("buy_total", col("buy_total").cast(LongType()))
        df_sell_txn = df_sell_txn.withColumn("sell_total", col("sell_total").cast(LongType()))
        df_other_txn = df_other_txn.withColumn("other_total", col("other_total").cast(LongType()))
    df_statement = df_balance.join(df_buy_txn, on=["account_id"], how="full") \
        .join(df_sell_txn, on=["account_id"], how="full") \
        .join(df_other_txn, on=["account_id"], how="full")
    df_statement = df_statement.withColumn("balance_summary", when(col("balance_summary").isNull(), f.array()).otherwise(col("balance_summary"))) \
        .withColumn("buy_orders", when(col("buy_orders").isNull(), f.array()).otherwise(col("buy_orders"))) \
        .withColumn("sell_orders", when(col("sell_orders").isNull(), f.array()).otherwise(col("sell_orders"))) \
        .withColumn("other_orders", when(col("other_orders").isNull(), f.array()).otherwise(col("other_orders"))) \
        .fillna({'buy_total': 0, 'sell_total': 0, 'other_total': 0, 'total_equity': 0})
    df_statement = df_statement.select("account_id", f.struct(["balance_summary", "buy_orders", "sell_orders", "other_orders", "buy_total", "sell_total", "other_total", "total_equity", "total_usd_margin", "pending_usd_margin", "total_usd_cash", "pending_usd_cash"]).alias(asset_key_str))
    logging.info("successfully generated statement data")
    return df_statement


def start_processing():
    df_accounts = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["accounts_path"], t_0), None, False, None) \
        .filter(col("partner_id").isin(config["accounts_partner_id"])) \
        .withColumn("account_id", col("id")) \
        .select(col("account_id").cast(LongType()), col("user_id").cast(LongType()))

    df_gss_txn = read_csv_file("hdfs:///global_stock_transactions/", None, False, None)
    df_success_txn_id = df_gss_txn.select(col("order_number").alias("id"), col("asset_type"), col("asset_sub_type"), col("status"))
    df_gss_balance = read_csv_file("hdfs:///global_stock_balance/", None, False, None)

    df_gss_balance = cast_to_bigdecimal(df_gss_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True)
    df_gss_txn = cast_to_bigdecimal(df_gss_txn, ["quantity", "price"], remove_trailing_zero=True)

    df_gss_statement = get_asset_statement(df_gss_txn, df_gss_balance, "usd")
    df_old_success_txn_id = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config_data["user_daily_statement"]["success_txn_path"], t_2), None, False, None)
    df_success_txn_id = df_old_success_txn_id.union(df_success_txn_id)
    df_success_txn_id.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["success_txn_path"], t_1), header=True)

    df_statement = df_gss_statement.withColumn("account_id", col("account_id").cast(LongType()))
    df_statement = df_statement.join(df_accounts, on=["account_id"], how="left").filter(col("user_id").isNotNull())
    df_statement = df_statement.withColumn("execution_date", lit(t_1))
    execution_time = get_date_for_query(config_data["offset"]-1)
    df_statement = df_statement.withColumn("execution_time", lit(execution_time))
    write_data_to_s3_and_kafka(df_statement)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"user_daily_statement")


