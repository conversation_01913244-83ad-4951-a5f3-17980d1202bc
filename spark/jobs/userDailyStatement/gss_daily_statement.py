from common import *
from config import *
from structs import *

spark = spark_session_create("gss_daily_statement")
raw_bucket = config_data["forex"]["raw_bucket"]
t_0 = get_date(config_data["offset"] - 1)
t_1 = get_date(config_data["offset"])
t_2 = get_date(config_data["offset"] + 1)
ts_0 = get_date_for_query(config_data["offset"] - 1).replace(hour=8, minute=0, second=0, microsecond=0)
ts_1 = get_date_for_query(config_data["offset"]).replace(hour=8, minute=0, second=0, microsecond=0)

config = job_config_data["user_daily_statement"]["global_stocks"]
partner_id = job_config_data["user_daily_statement"]["accounts_partner_id"]


def get_forex_balance():
    df_forex_accounts = read_csv_file("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], config_data["forex"]["t2"]["asset_accounts_folder"], config_data["forex"]["t2"]["files_folder"], t_1), None, False, None)
    df_forex_accounts_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["forex"]["t1"]["asset_accounts_folder"], t_0), is_raw=True, schema_for_empty_df=schema_for_forex_accounts)
    if df_forex_accounts_t_1 is not None:
        df_forex_accounts_t_1 = df_forex_accounts_t_1.withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("account_id", "balance", "blocked_balance", "partner_id", "updated")
        df_forex_accounts = df_forex_accounts.select("account_id", "balance", "blocked_balance", "partner_id", "updated")
        df_forex_accounts = df_forex_accounts.union(df_forex_accounts_t_1)
    else:
        df_forex_accounts = df_forex_accounts.select("account_id", "balance", "blocked_balance", "partner_id", "updated")
    df_forex_accounts = df_forex_accounts.filter(col("partner_id").isin(partner_id)).drop("partner_id")
    df_forex_accounts = de_dupe_dataframe(df_forex_accounts, ["account_id"], "updated").drop("updated")
    df_forex_accounts = df_forex_accounts.groupBy(["account_id"]).agg(sum("balance").alias("balance"), sum("blocked_balance").alias("blocked_balance"))
    df_forex_accounts = df_forex_accounts.withColumn("total_usd_cash", f.round(col("balance")+col("blocked_balance"), 2))
    df_forex_accounts = df_forex_accounts.withColumn("pending_usd_cash", f.round(col("blocked_balance"), 2)).select("account_id", "total_usd_cash", "pending_usd_cash")
    df_forex_accounts = df_forex_accounts.filter((col("total_usd_cash") > 0) | (col("pending_usd_cash") > 0))
    return df_forex_accounts


def get_leverage_wallet_balance(df_gss_balance, df_gss_codes):
    t_2_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config_data["leverage_wallet_accounts"]["t_2_files_folder"], t_2)
    df_leverage_wallet_t_2 = read_csv_file(t_2_path, None, False, None)
    df_leverage_wallet_t_2 = df_leverage_wallet_t_2.select("account_id", "partner_id", "balance", "locked_margin", "trading_margin", "updated")
    t_1_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config_data["leverage_wallet_accounts"]["t_1_files_folder"], t_1)
    df_leverage_wallet_t_1 = read_json_data(t_1_path, is_raw=True)
    if df_leverage_wallet_t_1 is not None:
        df_leverage_wallet = get_union_and_de_dupe(df_leverage_wallet_t_1.select(df_leverage_wallet_t_2.columns), df_leverage_wallet_t_2, ["account_id"], "updated")
    else:
        df_leverage_wallet = df_leverage_wallet_t_2
    df_leverage_wallet = df_leverage_wallet.drop("partner_id", "updated")
    df_gss_balance = df_gss_balance.select("account_id", "code", "unrealized_pnl")
    df_gss_codes = df_gss_codes.select("code", "stock_type")
    df_gss_balance = df_gss_balance.join(df_gss_codes, on=["code"], how="left").filter(col("stock_type") == "CFD_LEVERAGE")
    df_gss_balance = df_gss_balance.groupBy(["account_id"]).agg(sum("unrealized_pnl").alias("unrealized_pnl"))
    df_leverage_wallet = df_leverage_wallet.join(df_gss_balance, on=["account_id"], how="full").fillna(0)
    df_leverage_wallet = df_leverage_wallet.groupBy(["account_id"]).agg(sum("balance").alias("balance"), sum("locked_margin").alias("locked_margin"), sum("trading_margin").alias("trading_margin"), sum("unrealized_pnl").alias("unrealized_pnl"))
    df_leverage_wallet = df_leverage_wallet.withColumn("total_usd_margin", f.round(col("balance") + col("unrealized_pnl") - col("locked_margin") - col("trading_margin"), 2))
    df_leverage_wallet = df_leverage_wallet.withColumn("pending_usd_margin", f.round(col("locked_margin"), 2))
    df_leverage_wallet = df_leverage_wallet.select("account_id", "total_usd_margin", "pending_usd_margin")
    df_leverage_wallet = df_leverage_wallet.filter((col("total_usd_margin") > 0) | (col("pending_usd_margin") > 0))
    return df_leverage_wallet


def get_gss_success_txn():
    df_success_txn = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config_data["user_daily_statement"]["success_txn_path"], t_2), None, False, None)
    df_success_txn = df_success_txn.filter(col("asset_type") == "global_stocks")
    return df_success_txn


def get_global_stock_prices():
    logging.info("reading global stock prices from s3 for the date {}".format(t_0))
    df_price = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_price"]["s3_path"], t_0), None, False, None)
    df_price = df_price.withColumnRenamed("_id", "code").withColumnRenamed("mid_price", "closing_price").select("code", "closing_price")
    logging.info("successfully fetched global stock prices from s3")
    return df_price


def get_option_trade_price():
    try:
        s3_path = "s3a://{}/{}/{}/dt=".format(config_data["bucket"], job_config_data["global_stock_options"]["price_folder"], job_config_data["global_stock_options"]["price_snapshot_folder"])
        data = spark.read.csv("{}{}/".format(s3_path, t_0), header=True, inferSchema=True)
        global_stock_options_prices = data.withColumn("closing_price", round(col("price"), 2))
        global_stock_options_prices = global_stock_options_prices.select(col("globalStockId").alias("code"), col("optionsContractId").alias("options_contract_id"), "closing_price")
        logging.info("Successfully loaded global stock options trade prices")
        return global_stock_options_prices
    except Exception as e:
        logging.error("An error has occurred while loading global stock partner_prices: {}".format(repr(e)))
        logging.exception(e)
        raise e


def get_global_stock_codes():
    logging.info("reading global stock codes from kafka topic")
    df_gss_code = read_from_kafka_in_memory(config_data["bootstrap_servers"], config_data["kafka_topics"]["global_stock_topic"])
    df_gss_code = df_gss_code.select(col("value.id").alias("id"), col("value.pluang_company_code").alias("pluang_company_code"), col("value.stock_type").alias("stock_type"), col("value.__source_ts_ms").cast(LongType()).alias("__source_ts_ms"))
    df_gss_code = de_dupe_dataframe(df_gss_code, ["id"], "__source_ts_ms")
    df_gss_code = df_gss_code.withColumnRenamed("id", "code") \
        .select("code", "pluang_company_code", "stock_type")
    logging.info("successfully read global stock codes from kafka")
    return df_gss_code


def get_global_stock_stock_merger_transactions(df_gss_success_txn):
    # Global Stock Merger Transactions
    logging.info("Starting calculation of global stock merger raw data")
    df_gss_success_txn = df_gss_success_txn.filter((col("asset_sub_type").isin(config["global_stock_merger_transactions"]["asset_sub_type"])) & (col("status").isin(config["global_stock_merger_transactions"]["status"])))

    df_delta_gss_merger_txn_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_merger_transactions"]["delta_file"], t_0))
    df_delta_gss_merger_txn_t_2 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_merger_transactions"]["delta_file"], t_1))

    merger_txn_cols = ["id", "account_id", "partner_id", "source_stock_id", "destination_stock_id", "source_stock_quantity", "destination_stock_quantity", "source_stock_merger_price", "destination_stock_merger_price", "updated", "status"]
    if df_delta_gss_merger_txn_t_1 is not None:
        df_delta_gss_merger_txn_t_1 = df_delta_gss_merger_txn_t_1.select("value.*") \
            .select(merger_txn_cols)

    if df_delta_gss_merger_txn_t_2 is not None:
        df_delta_gss_merger_txn_t_2 = df_delta_gss_merger_txn_t_2.select("value.*") \
            .select(merger_txn_cols)

    df_delta_gss_merger_txn = get_union(df_delta_gss_merger_txn_t_1, df_delta_gss_merger_txn_t_2)
    if df_delta_gss_merger_txn is not None:
        df_delta_gss_merger_txn = df_delta_gss_merger_txn.withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter((col("ts") > ts_1) & (col("ts") <= ts_0)).drop("ts")
        df_delta_gss_merger_txn = substract_dataframe(df_delta_gss_merger_txn, df_gss_success_txn, "id")
        df_delta_gss_merger_txn = de_dupe_dataframe(df_delta_gss_merger_txn, ["id"], "updated") \
            .filter(col("status").isin(config["global_stock_merger_transactions"]["status"])) \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id", "updated")
        df_delta_gss_merger_txn_source = df_delta_gss_merger_txn.select(col("account_id").cast(LongType()), col("id").cast(LongType()).alias("order_number"), lit("Corporate Action").alias("order_type"), col("source_stock_id").alias("code"), round(col("source_stock_quantity"), 10).alias("quantity"), round(col("source_stock_merger_price"), 10).alias("price"), lit(0.0).alias("fee"), col("status"))
        df_delta_gss_merger_txn_dest = df_delta_gss_merger_txn.select(col("account_id").cast(LongType()), col("id").cast(LongType()).alias("order_number"), lit("Corporate Action").alias("order_type"), col("destination_stock_id").alias("code"), round(col("destination_stock_quantity"), 10).alias("quantity"), round(col("destination_stock_merger_price"), 10).alias("price"), lit(0.0).alias("fee"), col("status"))
        df_delta_gss_merger_txn_source = df_delta_gss_merger_txn_source.withColumn("total", round(col("quantity")*col("price"), 2))
        df_delta_gss_merger_txn_dest = df_delta_gss_merger_txn_dest.withColumn("total", round(col("quantity")*col("price"), 2))
        df_delta_gss_merger_txn_all = get_union(df_delta_gss_merger_txn_source, df_delta_gss_merger_txn_dest)
    else:
        df_delta_gss_merger_txn_all = create_empty_df(schema_for_statement_transactions)
    df_delta_gss_merger_txn_all = df_delta_gss_merger_txn_all.withColumn("leverage", lit(""))
    logging.info("Successfully calculated global stock merger transactions")
    return df_delta_gss_merger_txn_all


def get_global_stock_mission_rewards(df_gss_success_txn):
    # Global Stock Mission Rewards
    logging.info("Starting calculation of global stock mission_rewards")
    df_gss_success_txn = df_gss_success_txn.filter((col("asset_sub_type").isin(config["global_stock_mission_rewards"]["asset_sub_type"])) & (col("status").isin(config["global_stock_mission_rewards"]["status"])))

    df_delta_gss_rewards_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_mission_rewards"]["delta_file"], t_0))
    df_delta_gss_rewards_t_2 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_mission_rewards"]["delta_file"], t_1))

    if df_delta_gss_rewards_t_1 is not None:
        df_delta_gss_rewards_t_1 = df_delta_gss_rewards_t_1.select("value.*") \
            .select("account_id", "global_stock_id", "status", "partner_id", "id", "executed_quantity", "executed_unit_price", "updated", "total_price")

    if df_delta_gss_rewards_t_2 is not None:
        df_delta_gss_rewards_t_2 = df_delta_gss_rewards_t_2.select("value.*") \
            .select("account_id", "global_stock_id", "status", "partner_id", "id", "executed_quantity", "executed_unit_price", "updated", "total_price")

    df_delta_gss_rewards = get_union(df_delta_gss_rewards_t_1, df_delta_gss_rewards_t_2)
    if df_delta_gss_rewards is not None:
        df_delta_gss_rewards = df_delta_gss_rewards.withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter((col("ts") > ts_1) & (col("ts") <= ts_0)).drop("ts")
        df_delta_gss_rewards = substract_dataframe(df_delta_gss_rewards, df_gss_success_txn, "id")
        df_delta_gss_rewards = de_dupe_dataframe(df_delta_gss_rewards, ["id"], "updated") \
            .filter(col("status").isin(config["global_stock_mission_rewards"]["status"])) \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id", "updated") \
            .select(col("account_id").cast(LongType()), col("id").cast(LongType()).alias("order_number"), lit("Reward").alias("order_type"), col("global_stock_id").alias("code"), round(col("executed_quantity"), 10).alias("quantity"), round(col("executed_unit_price"), 10).alias("price"), lit(0.0).alias("fee"), col("status"), col("total_price").alias("total"))
    else:
        df_delta_gss_rewards = create_empty_df(schema_for_statement_transactions)
    df_delta_gss_rewards = df_delta_gss_rewards.withColumn("leverage", lit(""))
    logging.info("Successfully calculated global stock rewards")
    return df_delta_gss_rewards


def get_global_stock_txn(df_gss_success_txn):
    # Global Stock Transactions
    logging.info("Starting calculation of global stock transactions")
    df_gss_success_txn = df_gss_success_txn.filter((col("asset_sub_type").isin(config["global_stock_transactions"]["asset_sub_type"])) & (col("status").isin(config["global_stock_transactions"]["status"])))

    df_delta_gss_transactions_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_transactions"]["delta_file"], t_0))
    df_delta_gss_transactions_t_2 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_transactions"]["delta_file"], t_1))

    if df_delta_gss_transactions_t_1 is not None:
        df_delta_gss_transactions_t_1 = df_delta_gss_transactions_t_1.select("value.*") \
            .select("account_id", "global_stock_id", "status", "partner_id", "id", "order_type", "executed_quantity", "unit_price", "transaction_type", "transaction_fee", "updated", "stock_type", "trading_hours", "total_price")

    if df_delta_gss_transactions_t_2 is not None:
        df_delta_gss_transactions_t_2 = df_delta_gss_transactions_t_2.select("value.*") \
            .select("account_id", "global_stock_id", "status", "partner_id", "id", "order_type", "executed_quantity", "unit_price", "transaction_type", "transaction_fee", "updated", "stock_type", "trading_hours", "total_price")

    df_delta_gss_txn = get_union(df_delta_gss_transactions_t_1, df_delta_gss_transactions_t_2)

    if df_delta_gss_txn is not None:
        df_delta_gss_txn = df_delta_gss_txn.withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter((col("ts") > ts_1) & (col("ts") <= ts_0)).drop("ts")
        df_delta_gss_txn = substract_dataframe(df_delta_gss_txn, df_gss_success_txn, "id")
        df_delta_gss_txn = de_dupe_dataframe(df_delta_gss_txn, ["id"], "updated") \
            .filter(col("status").isin(config["global_stock_transactions"]["status"])) \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id", "updated") \
            .select(col("account_id").cast(LongType()), col("id").cast(LongType()).alias("order_number"), col("order_type"), col("global_stock_id").alias("code"), round(col("executed_quantity"), 10).alias("quantity"), round(col("unit_price"), 10).alias("price"), round(col("transaction_fee"), 10).alias("fee"), col("transaction_type"), col("stock_type"), col("trading_hours"), col("status"), col("total_price").alias("total"))
        df_delta_gss_txn = df_delta_gss_txn.withColumn("leverage", when(col("stock_type") == "CFD_LEVERAGE", "2X").otherwise(""))
        df_delta_gss_txn = df_delta_gss_txn.withColumn("leverage", when((col("stock_type") == "CFD_LEVERAGE") & (col("trading_hours") == "INTRADAY"), "4X").otherwise(col("leverage"))).drop("stock_type", "trading_hours")
        df_delta_gss_buy_txn = df_delta_gss_txn.filter(col("transaction_type").isin(["BUY"])).drop("transaction_type")
        df_delta_gss_sell_txn = df_delta_gss_txn.filter(col("transaction_type").isin(["SELL"])).drop("transaction_type")
    else:
        df_delta_gss_buy_txn = create_empty_df(schema_for_statement_transactions)
        df_delta_gss_sell_txn = create_empty_df(schema_for_statement_transactions)
    cols = df_delta_gss_buy_txn.columns
    if "leverage" not in cols:
        df_delta_gss_buy_txn = df_delta_gss_buy_txn.withColumn("leverage", lit(""))
        df_delta_gss_sell_txn = df_delta_gss_sell_txn.withColumn("leverage", lit(""))
    logging.info("Successfully calculated global stock transactions")
    return df_delta_gss_buy_txn, df_delta_gss_sell_txn


def get_global_stock_options_txn(df_gss_options_success_txn):
    # Global Stock Options Transactions
    logging.info("Starting calculation of global stock options transactions")
    df_gss_options_success_txn = df_gss_options_success_txn.filter((col("asset_sub_type").isin(config["global_stock_options_transactions"]["asset_sub_type"])) & (col("status").isin(config["global_stock_options_transactions"]["status"])))

    df_delta_gss_options_transactions_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_options_transactions"]["delta_file"], t_0))
    df_delta_gss_options_transactions_t_2 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_options_transactions"]["delta_file"], t_1))

    if df_delta_gss_options_transactions_t_1 is not None:
        df_delta_gss_options_transactions_t_1 = df_delta_gss_options_transactions_t_1.select("value.*") \
            .select("account_id", "global_stock_id", "options_contract_id", "status", "partner_id", "id", "order_type", "executed_quantity", "unit_price", "transaction_type", "transaction_fee", "updated", "total_price")

    if df_delta_gss_options_transactions_t_2 is not None:
        df_delta_gss_options_transactions_t_2 = df_delta_gss_options_transactions_t_2.select("value.*") \
            .select("account_id", "global_stock_id", "options_contract_id", "status", "partner_id", "id", "order_type", "executed_quantity", "unit_price", "transaction_type", "transaction_fee", "updated", "total_price")

    df_delta_gss_options_txn = get_union(df_delta_gss_options_transactions_t_1, df_delta_gss_options_transactions_t_2)

    if df_delta_gss_options_txn is not None:
        df_delta_gss_options_txn = df_delta_gss_options_txn.withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter((col("ts") > ts_1) & (col("ts") <= ts_0)).drop("ts")
        df_delta_gss_options_txn = substract_dataframe(df_delta_gss_options_txn, df_gss_options_success_txn, "id")
        df_delta_gss_options_txn = de_dupe_dataframe(df_delta_gss_options_txn, ["id"], "updated") \
            .filter(col("status").isin(config["global_stock_options_transactions"]["status"])) \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id", "updated") \
            .select(col("account_id").cast(LongType()), col("id").cast(LongType()).alias("order_number"), col("order_type"), col("global_stock_id").alias("code"), round(col("executed_quantity"), 10).alias("quantity"), round(col("unit_price"), 10).alias("price"), round(col("transaction_fee"), 10).alias("fee"), col("transaction_type"), col("options_contract_id"), col("status"), col("total_price").alias("total"))
        df_delta_gss_options_buy_options_txn = df_delta_gss_options_txn.filter(col("transaction_type").isin("LONG_OPEN")).drop("transaction_type")
        df_delta_gss_options_sell_options_txn = df_delta_gss_options_txn.filter(col("transaction_type").isin("LONG_CLOSE")).drop("transaction_type")
    else:
        df_delta_gss_options_buy_options_txn = create_empty_df(schema_for_statement_transactions).withColumn("options_contract_id", lit(None).cast("long"))
        df_delta_gss_options_sell_options_txn = create_empty_df(schema_for_statement_transactions).withColumn("options_contract_id", lit(None).cast("long"))
    df_delta_gss_options_buy_options_txn = df_delta_gss_options_buy_options_txn.withColumn("leverage", lit(""))
    df_delta_gss_options_sell_options_txn = df_delta_gss_options_sell_options_txn.withColumn("leverage", lit(""))
    logging.info("Successfully calculated global stock transactions")
    return df_delta_gss_options_buy_options_txn, df_delta_gss_options_sell_options_txn


def get_global_stock_options_contracts():
    # global stock options contracts
    logging.info("Starting calculation of global stock options contracts")
    df_options_contracts_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_options_contracts"]["delta_file"], t_0))
    if df_options_contracts_t_1 is not None:
        df_options_contracts_t_1 = df_options_contracts_t_1.select("value.*") \
            .withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("id", "global_stock_id", "contract_type", "expiration_date", "strike_price", "updated", "shares_per_contract")
        df_options_contracts_t_1 = df_options_contracts_t_1.withColumn("expiration_date", col("expiration_date").cast(IntegerType()))
        df_options_contracts_t_1 = df_options_contracts_t_1.withColumn("expiration_date", f.expr("date_add(to_date('1970-01-01'), expiration_date)").cast(DateType()))
    df_options_contracts = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_options_contracts"]["t_2_file"], t_1), None, False, None) \
        .select("id", "global_stock_id", "contract_type", "expiration_date", "strike_price", "updated", "shares_per_contract")
    df_options_contracts = get_union(df_options_contracts, df_options_contracts_t_1).withColumnRenamed("id", "options_contract_id")
    df_options_contracts = de_dupe_dataframe(df_options_contracts, ["options_contract_id", "global_stock_id"], "updated").drop("updated")
    return df_options_contracts


def get_global_stock_options_accounts():
    # global stock options accounts
    logging.info("Starting calculation of global stock options accounts")
    df_gss_options_accunts_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_options_accounts"]["delta_file"], t_0))
    if df_gss_options_accunts_t_1 is not None:
        df_gss_options_accunts_t_1 = df_gss_options_accunts_t_1.select("value.*") \
            .withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("account_id", "options_contract_id","global_stock_id", "total_quantity", "partner_id", "updated","weighted_cost") \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id")
    df_gss_options_accounts = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_options_accounts"]["t_2_file"], t_1), None, False, None) \
        .select("account_id", "options_contract_id","global_stock_id", "total_quantity", "partner_id", "updated","weighted_cost") \
        .filter(col("partner_id").isin(partner_id)) \
        .drop("partner_id")
    df_gss_options_accounts = get_union(df_gss_options_accunts_t_1, df_gss_options_accounts)
    df_gss_options_accounts = de_dupe_dataframe(df_gss_options_accounts, ["account_id", "global_stock_id","options_contract_id"], "updated").drop("updated")
    logging.info("Successfully calculated global stock accounts")
    return df_gss_options_accounts


def get_global_stock_accounts():
    # global stock accounts
    logging.info("Starting calculation of global stock accounts")
    df_gss_accunts_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_accounts"]["delta_file"], t_0))
    if df_gss_accunts_t_1 is not None:
        df_gss_accunts_t_1 = df_gss_accunts_t_1.select("value.*") \
            .withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("account_id", "global_stock_id", "balance", "partner_id", "updated") \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id")
    df_gss_accounts = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_accounts"]["t_2_file"], t_1), None, False, None) \
        .select("account_id", "global_stock_id", "balance", "partner_id", "updated") \
        .filter(col("partner_id").isin(partner_id)) \
        .drop("partner_id")
    df_gss_accounts = get_union(df_gss_accunts_t_1, df_gss_accounts)
    df_gss_accounts = de_dupe_dataframe(df_gss_accounts, ["account_id", "global_stock_id"], "updated").drop("updated")
    # global stock pocket accounts
    df_gss_pocket_accunts_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_pocket_accounts"]["delta_file"], t_0))
    if df_gss_pocket_accunts_t_1 is not None:
        df_gss_pocket_accunts_t_1 = df_gss_pocket_accunts_t_1.select("value.*") \
            .withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("account_id", "global_stock_id", "user_pocket_id", "balance", "partner_id", "updated") \
            .filter(col("partner_id").isin(partner_id)) \
            .drop("partner_id")
    df_gss_pocket_accounts = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_pocket_accounts"]["t_2_file"], t_1), None, False, None) \
        .select("account_id", "global_stock_id", "user_pocket_id", "balance", "partner_id", "updated") \
        .filter(col("partner_id").isin(partner_id)) \
        .drop("partner_id")
    df_gss_pocket_accounts = get_union(df_gss_pocket_accunts_t_1, df_gss_pocket_accounts)
    df_gss_pocket_accounts = de_dupe_dataframe(df_gss_pocket_accounts, ["account_id", "global_stock_id", "user_pocket_id"], "updated").drop("updated", "user_pocket_id")
    df_gss_accounts = get_union(df_gss_accounts, df_gss_pocket_accounts)
    df_gss_accounts = df_gss_accounts.groupBy(["account_id", "global_stock_id"]).agg(f.sum("balance").alias("balance"))
    logging.info("Successfully calculated global stock accounts")
    return df_gss_accounts


def get_global_stock_returns():
    # Global stock returns
    logging.info("Starting calculation of global stock returns")
    df_gss_returns_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_returns"]["delta_file"], t_0))
    if df_gss_returns_t_1 is not None:
        df_gss_returns_t_1 = df_gss_returns_t_1.select("value.*") \
            .withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("account_id", "global_stock_id", "total_quantity", "weighted_cost", "updated")
    df_gss_returns = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_returns"]["t_2_file"], t_1), None, False, None) \
        .select("account_id", "global_stock_id", "total_quantity", "weighted_cost", "updated")
    df_gss_returns = get_union(df_gss_returns, df_gss_returns_t_1)
    df_gss_returns = de_dupe_dataframe(df_gss_returns, ["account_id", "global_stock_id"], "updated").drop("updated")
    # Global stock pocket returns
    df_gss_pocket_returns_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_pocket_returns"]["delta_file"], t_0))
    if df_gss_pocket_returns_t_1 is not None:
        df_gss_pocket_returns_t_1 = df_gss_pocket_returns_t_1.select("value.*") \
            .withColumn("ts", f.from_utc_timestamp(f.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= ts_0).drop("ts") \
            .select("account_id", "global_stock_id", "user_pocket_id", "total_quantity", "weighted_cost", "updated")
    df_gss_pocket_returns = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["global_stock_pocket_returns"]["t_2_file"], t_1), None, False, None) \
        .select("account_id", "global_stock_id", "user_pocket_id", "total_quantity", "weighted_cost", "updated")
    df_gss_pocket_returns = get_union(df_gss_pocket_returns, df_gss_pocket_returns_t_1)
    df_gss_pocket_returns = de_dupe_dataframe(df_gss_pocket_returns, ["account_id", "global_stock_id", "user_pocket_id"], "updated").drop("updated", "user_pocket_id")
    df_gss_returns = get_union(df_gss_returns, df_gss_pocket_returns)
    df_gss_returns = df_gss_returns.withColumn("weighted_cost", round(col("weighted_cost") * col("total_quantity"), 10))
    df_gss_returns = df_gss_returns.groupBy(["account_id", "global_stock_id"]).agg(f.sum("weighted_cost").alias("weighted_cost"), f.sum("total_quantity").alias("total_quantity"))
    df_gss_returns = df_gss_returns.withColumn("weighted_cost", round(col("weighted_cost") / col("total_quantity"), 10)).fillna(0)
    logging.info("Successfully calculated global stock returns")
    return df_gss_returns


def get_global_stock_balance():
    # Merge global stock accounts and returns and price
    logging.info("Starting calculation of global stock balance")
    df_gss_accounts = get_global_stock_accounts()
    df_gss_returns = get_global_stock_returns()
    df_gss_balance = df_gss_accounts.join(df_gss_returns, on=["account_id", "global_stock_id"], how="left") \
        .select(col("account_id").cast(LongType()), col("global_stock_id").alias("code"), round(col("balance"), 10).alias("quantity"), round(col("weighted_cost"), 6).alias("avg_buy_price")) \
        .filter(col("quantity") > 0)
    df_price = get_global_stock_prices()
    df_gss_balance = df_gss_balance.join(df_price, on=["code"], how="left") \
        .fillna({'closing_price': 0}) \
        .withColumn("market_value", round(col("quantity") * col("closing_price"), 2).alias("market_value")) \
        .withColumn("unrealized_pnl", round(col("quantity")*(col("closing_price") - col("avg_buy_price")), 2))
    logging.info("Successfully calculated global stock balance")
    return df_gss_balance


def get_global_stock_options_balance():
    # global stock options balance
    logging.info("Starting calculation of global stock options balance")
    df_gss_options_balance = get_global_stock_options_accounts()

    df_gss_options_balance = df_gss_options_balance.select(col("account_id").cast(LongType()), col("options_contract_id"),col("global_stock_id").alias("code"), round(col("total_quantity"), 10).alias("quantity"), round(col("weighted_cost"), 6).alias("avg_buy_price")) \
        .filter(col("quantity") > 0)
    df_price = get_option_trade_price()
    df_gss_options_balance = df_gss_options_balance.join(df_price, on=["code", "options_contract_id"], how="left") \
        .fillna({'closing_price': 0}) \
        .withColumn("market_value", round(col("quantity") * col("closing_price"), 2).alias("market_value")) \
        .withColumn("unrealized_pnl", round(col("quantity")*(col("closing_price") - col("avg_buy_price")), 2))
    logging.info("Successfully calculated global stock balance")
    return df_gss_options_balance


def start_processing():
    df_gss_success_txn = get_gss_success_txn()
    df_gss_options_contracts = get_global_stock_options_contracts()

    df_delta_gss_buy_txn, df_delta_gss_sell_txn = get_global_stock_txn(df_gss_success_txn)
    df_delta_gss_buy_txn = df_delta_gss_buy_txn.withColumn("transaction_type", lit("BUY")).withColumn("asset_type", lit("global_stocks")).withColumn("asset_sub_type", lit("global_stock_transactions")).select(schema_for_statement_transactions.fieldNames() + ["leverage"])
    df_delta_gss_sell_txn = df_delta_gss_sell_txn.withColumn("transaction_type", lit("SELL")).withColumn("asset_type", lit("global_stocks")).withColumn("asset_sub_type", lit("global_stock_transactions")).select(schema_for_statement_transactions.fieldNames() + ["leverage"])

    df_delta_gss_buy_options_txn, df_delta_gss_sell_options_txn = get_global_stock_options_txn(df_gss_success_txn)
    df_delta_gss_buy_options_txn = df_delta_gss_buy_options_txn.withColumn("transaction_type", lit("BUY")).withColumn("asset_type", lit("global_stocks")).withColumn("asset_sub_type", lit("options_contract_transactions")).select(schema_for_statement_transactions.fieldNames() + ["leverage", "options_contract_id"])
    df_delta_gss_sell_options_txn = df_delta_gss_sell_options_txn.withColumn("transaction_type", lit("SELL")).withColumn("asset_type", lit("global_stocks")).withColumn("asset_sub_type", lit("options_contract_transactions")).select(schema_for_statement_transactions.fieldNames() + ["leverage", "options_contract_id"])
    df_delta_gss_options_txn = df_delta_gss_buy_options_txn.union(df_delta_gss_sell_options_txn)
    df_delta_gss_options_txn = df_delta_gss_options_txn.join(df_gss_options_contracts, on=["options_contract_id"], how="left")
    df_delta_gss_options_txn = df_delta_gss_options_txn.withColumn("expiration_date", (f.date_format(col("expiration_date"), "yyMMdd")))
    df_delta_gss_options_txn = df_delta_gss_options_txn.withColumn("strike_price_symbol", lit("$"))
    df_delta_gss_options_txn = df_delta_gss_options_txn.withColumn("strike_price", f.concat("strike_price_symbol", "strike_price")).drop("strike_price_symbol")
    df_delta_gss_options_txn = df_delta_gss_options_txn.withColumn("contract_code", f.concat(col("expiration_date"), lit(" "), col("strike_price"), lit(" "), col("contract_type"))).drop("expiration_date", "strike_price", "contract_type", "options_contract_id")

    df_delta_gss_mission_rewards = get_global_stock_mission_rewards(df_gss_success_txn)
    df_delta_gss_mission_rewards = df_delta_gss_mission_rewards.withColumn("transaction_type", lit("OTHER")).withColumn("asset_type", lit("global_stocks")).withColumn("asset_sub_type", lit("global_stock_mission_rewards")).select(schema_for_statement_transactions.fieldNames() + ["leverage"])

    df_delta_gss_merger_transactions = get_global_stock_stock_merger_transactions(df_gss_success_txn)
    df_delta_gss_merger_transactions = df_delta_gss_merger_transactions.withColumn("transaction_type", lit("OTHER")).withColumn("asset_type", lit("global_stocks")).withColumn("asset_sub_type", lit("global_stock_merger_transactions")).select(schema_for_statement_transactions.fieldNames() + ["leverage"])

    df_delta_gss_txn = df_delta_gss_buy_txn.union(df_delta_gss_sell_txn).union(df_delta_gss_mission_rewards).union(df_delta_gss_merger_transactions)
    df_delta_gss_txn = df_delta_gss_txn.withColumn("type", lit("globalStock")).withColumn("contract_code", lit(""))
    df_delta_gss_options_txn = df_delta_gss_options_txn.withColumn("type", lit("globalStockOptions"))
    df_delta_gss_txn = get_union(df_delta_gss_txn, df_delta_gss_options_txn)

    df_gss_codes = get_global_stock_codes()
    df_delta_gss_txn = df_delta_gss_txn.join(df_gss_codes, on=["code"], how="left").drop("stock_type")
    df_delta_gss_txn = df_delta_gss_txn.withColumn("code", col("pluang_company_code")).drop("pluang_company_code")
    df_delta_gss_txn = df_delta_gss_txn.withColumn("code", when(((col("leverage") == "") | (col("leverage").isNull())), col("code")).otherwise(f.concat(col("code"), lit(" ("), col("leverage"), lit(")"))))
    df_delta_gss_txn = df_delta_gss_txn.withColumn("code", when((col("type") == "globalStock"), col("code")).otherwise(f.concat(col("code"), lit(" "), col("contract_code")))).drop("contract_code")
    df_delta_gss_txn.coalesce(1).write.mode("overwrite").csv("hdfs:///global_stock_transactions/", header=True)

    df_gss_balance = get_global_stock_balance()
    df_gss_balance = df_gss_balance.withColumn("type", lit("globalStock")).withColumn("contract_code", lit(""))

    df_gss_options_balance = get_global_stock_options_balance()
    df_gss_options_balance = df_gss_options_balance.join(df_gss_options_contracts, on=["options_contract_id"], how="left")
    gss_options_balance_with_zero_shares = df_gss_options_balance.filter((col("shares_per_contract").isNull()) | (col("shares_per_contract") == 0)).count()
    if gss_options_balance_with_zero_shares > 0:
        logging.warn("count of gss options contract accounts which has 0 shares per contract : {}".format(gss_options_balance_with_zero_shares))
    df_gss_options_balance = df_gss_options_balance.withColumn("market_value", round(col("market_value") * col("shares_per_contract"), 2)) \
        .withColumn("unrealized_pnl", round(col("unrealized_pnl") * col("shares_per_contract"), 2))
    df_gss_options_balance = df_gss_options_balance.withColumn("expiration_date", f.date_format(col("expiration_date"), "yyMMdd"))
    df_gss_options_balance = df_gss_options_balance.withColumn("strike_price_symbol", lit("$"))
    df_gss_options_balance = df_gss_options_balance.withColumn("strike_price", f.concat("strike_price_symbol", "strike_price")).drop("strike_price_symbol")
    df_gss_options_balance = df_gss_options_balance.withColumn("contract_code", f.concat(col("expiration_date"), lit(" "), col("strike_price"), lit(" "), col("contract_type"))).drop("expiration_date", "strike_price", "contract_type", "options_contract_id")
    df_gss_options_balance = df_gss_options_balance.withColumn("type", lit("globalStockOptions"))
    df_gss_balance = get_union(df_gss_balance, df_gss_options_balance)

    df_forex_balance = get_forex_balance()
    df_leverage_balance = get_leverage_wallet_balance(df_gss_balance, df_gss_codes)
    df_usd_balance = df_forex_balance.join(df_leverage_balance, on=["account_id"], how="full").fillna(0)
    df_usd_balance.coalesce(1).write.mode("overwrite").csv("hdfs:///usd_balance/", header=True)

    df_gss_balance = df_gss_balance.join(df_gss_codes, on=["code"], how="left").drop("stock_type")
    df_gss_balance = df_gss_balance.withColumn("code", col("pluang_company_code")).drop("pluang_company_code")
    df_gss_balance = df_gss_balance.withColumn("code", when((col("type") == "globalStock"), col("code")).otherwise(f.concat(col("code"), lit(" "), col("contract_code")))).drop("contract_code")
    df_gss_balance.coalesce(1).write.mode("overwrite").csv("hdfs:///global_stock_balance/", header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"gss_daily_statement")

