from common import *
from structs import *
from config import *

global_stock_ph_flag = False
bucket_name = None
raw_bucket = None
job_config = job_config_data["global_stock_dividend"]
spark = spark_session_create("snap_global_stock_dividend")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()


def get_global_stock_de_dupe_data(raw_bucket, bucket, asset_folder, files_folder, schema, asset_name, column_order,
                                  primary_keys):
    logging.info("Reading t1 files from Raw Bucket for {}".format(asset_name))
    dt_time = get_date_for_query(config_data["offset"] - 1)
    dt = dt_time.date()
    hour= job_config["filter_time"]["hour"]
    minute= job_config["filter_time"]["minute"]
    second= job_config["filter_time"]["second"]
    microsecond = job_config["filter_time"]["microsecond"]
    dt_utc =dt_time.replace(hour=hour,minute=minute,second=second,microsecond=microsecond).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    global_stock_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, asset_folder, dt), is_raw=True, schema_for_empty_df=schema)
    global_stock_t_1 = global_stock_t_1.filter(col("updated")<= dt_utc )
    logging.info("Count for {} : {}".format(asset_name, global_stock_t_1.count()))
    logging.info("Completed spark read for t1 files")
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,asset_folder),"id",lowerbound_ts,upperbound_ts)
    global_stock_t_1 = global_stock_t_1.filter(~col("id").isin(delete_record))
    logging.info("Reading t2 file")
    global_stock_t_2 = get_asset_t_2(bucket, asset_folder, files_folder, get_date(config_data["offset"]), schema,
                                     asset_name)
    global_stock_t_2 = global_stock_t_2.filter(~col("id").isin(delete_record))
    logging.info("Merging to get latest data")
    global_stock_t_0 = get_asset_t_0(global_stock_t_1, global_stock_t_2, primary_keys, asset_name, column_order)
    return global_stock_t_0


def read_from_kafka_topic_stock_list(bootstrap_servers, topics):
    logging.info("reading data from {} kafka topic/topics".format(topics))
    df = read_from_kafka_in_memory(bootstrap_servers, topics)
    stock_list = df.select("value.id").distinct()
    logging.info("Successfully read {} data".format(topics))
    return stock_list


def calculate_global_stock_dividend():
    logging.info("Starting execution for global stocks dividend Snapshotting")
    global_stock_accounts_t_0 = get_global_stock_de_dupe_data(raw_bucket,
                                                              bucket_name,
                                                              config_data["global_stock"]["snap"]["asset_accounts_folder"],
                                                              config_data["global_stock"]["t2"]["files_folder"],
                                                              schema_for_global_stock_accounts,
                                                              config_data["global_stock"]["asset_name"],
                                                              config_data["global_stock"]["accounts_column_order"],
                                                              config_data["global_stock"]["primary_keys"])
    global_stock_returns_t_0 = get_global_stock_de_dupe_data(raw_bucket,
                                                             bucket_name,
                                                             config_data["global_stock"]["snap"]["asset_folder"],
                                                             config_data["global_stock"]["t2"]["files_folder"],
                                                             schema_for_global_stock_returns,
                                                             config_data["global_stock"]["asset_name"],
                                                             config_data["global_stock"]["column_order"],
                                                             config_data["global_stock"]["primary_keys"])

    global_stock = global_stock_returns_t_0.join(global_stock_accounts_t_0, (
            (global_stock_accounts_t_0.account_id == global_stock_returns_t_0.account_id) & (
            global_stock_accounts_t_0.global_stock_id == global_stock_returns_t_0.global_stock_id)),"inner") \
        .drop(global_stock_accounts_t_0.account_id).drop(global_stock_accounts_t_0.created).drop(global_stock_accounts_t_0.updated).drop(global_stock_accounts_t_0.id).drop(global_stock_accounts_t_0.global_stock_id).drop(global_stock_accounts_t_0.user_id)
    logging.info("Filtering data to get accounts with total value > 0")
    global_stock = global_stock.withColumn("total_quantity", col("total_quantity").cast("double"))
    global_stock = global_stock.where((global_stock.total_quantity > 0))
    return global_stock


def calculate_global_stock_pocket_dividend():
    logging.info("Starting execution for global stocks pocket dividend Snapshotting")
    global_stock_accounts_t_0 = get_global_stock_de_dupe_data(raw_bucket,
                                                              bucket_name,
                                                              config_data["global_stock_pocket"]["snap"]["asset_accounts_folder"],
                                                              config_data["global_stock_pocket"]["t2"]["files_folder"],
                                                              schema_for_global_stock_pocket_accounts,
                                                              config_data["global_stock_pocket"]["asset_name"],
                                                              config_data["global_stock_pocket"]["accounts_column_order"],
                                                              config_data["global_stock_pocket"]["primary_keys"])
    global_stock_returns_t_0 = get_global_stock_de_dupe_data(raw_bucket,
                                                             bucket_name,
                                                             config_data["global_stock_pocket"]["snap"]["asset_folder"],
                                                             config_data["global_stock_pocket"]["t2"]["files_folder"],
                                                             schema_for_global_stock_pocket_returns,
                                                             config_data["global_stock_pocket"]["asset_name"],
                                                             config_data["global_stock_pocket"]["column_order"],
                                                             config_data["global_stock_pocket"]["primary_keys"])

    global_stock = global_stock_returns_t_0.join(global_stock_accounts_t_0, (
            (global_stock_accounts_t_0.account_id == global_stock_returns_t_0.account_id) & (
            global_stock_accounts_t_0.global_stock_id == global_stock_returns_t_0.global_stock_id) & (
                    global_stock_accounts_t_0.user_pocket_id == global_stock_returns_t_0.user_pocket_id)),"inner") \
        .drop(global_stock_accounts_t_0.account_id).drop(global_stock_accounts_t_0.created).drop(global_stock_accounts_t_0.updated).drop(global_stock_accounts_t_0.id).drop(global_stock_accounts_t_0.global_stock_id).drop(global_stock_accounts_t_0.user_id).drop(global_stock_accounts_t_0.user_pocket_id)
    logging.info("Filtering data to get accounts with total value > 0")
    global_stock = global_stock.withColumn("total_quantity", col("total_quantity").cast("double"))
    global_stock = global_stock.where((global_stock.total_quantity > 0))
    return global_stock


def start_processing():
    global_stock = calculate_global_stock_dividend()
    global_stock = global_stock.withColumn("pocket_return_details", lit(None)).withColumn("adp_return_quantity", col("total_quantity")).withColumn("pending_reward_balance",round(col("pending_reward_balance").cast("double"),10)).withColumn("total_reward_balance",round(col("total_reward_balance").cast("double"),10)).withColumn("reward_balance",round(col("reward_balance").cast("double"),10))
    global_stock = global_stock.fillna({'pending_reward_balance': 0.0}).fillna({'reward_balance': 0.0}).fillna({'total_reward_balance': 0})
    cols = ["account_id", "user_id", "global_stock_id", "total_quantity", "batch_quantity", "pocket_return_details", "adp_return_quantity", "partner_id", "balance","pending_reward_balance","reward_balance","total_reward_balance"]
    bootstrap_servers = config_data["bootstrap_servers"]
    if global_stock_ph_flag == False:
        global_stock_pocket = calculate_global_stock_pocket_dividend()
        global_stock_pocket = global_stock_pocket.withColumn("pocket_return_details", f.struct(["user_pocket_id", "total_quantity"])).withColumn("adp_return_quantity", lit(0.0)).withColumn("pending_reward_balance",lit(0.0)).withColumn("total_reward_balance",lit(0.0)).withColumn("reward_balance",lit(0.0))
        global_stock_dividend = global_stock.select(cols).union(global_stock_pocket.select(cols))
        topics = config_data["global_stock"]["global_stock_list_kafka_topic"]
    else:
        global_stock_dividend = global_stock.select(cols)
        topics = config_data["global_stock_ph"]["global_stock_list_kafka_topic"]
    global_stock_list = read_from_kafka_topic_stock_list(bootstrap_servers, topics)
    global_stock_dividend = global_stock_dividend.groupBy(["account_id", "user_id", "global_stock_id"]).agg(round(sum("total_quantity"), 10).alias("total_quantity"), round(sum("batch_quantity"), 10).alias("batch_quantity"), f.collect_list("pocket_return_details").alias("pocket_return_details"), round(sum("adp_return_quantity"), 10).alias("adp_return_quantity"), round(sum("balance"), 10).alias("balance"), round(sum("pending_reward_balance"), 10).alias("pending_reward_balance"), round(sum("reward_balance"), 10).alias("reward_balance"), round(sum("total_reward_balance"), 10).alias("total_reward_balance"))
    current_timestamp = get_date_for_query(config_data["offset"] - 1)
    global_stock_dividend = global_stock_dividend.withColumn("_global_stock_id", col("global_stock_id")).withColumn("created",
                                                                                                                    lit(current_timestamp))
    # global_stock_list_api = config_data["global_stock"]["global_stock_list_api"]
    global_stock_list = global_stock_list.drop(col("status")).withColumnRenamed("id", "_global_stock_id")
    global_stock_dividend = global_stock_dividend.join(global_stock_list, "_global_stock_id", "full")
    global_stock_dividend.coalesce(1).write.mode('overwrite').partitionBy("_global_stock_id").json(
        "s3a://{}/{}/{}/dt={}".format(bucket_name, config_data["global_stock"]["snap"]["divided_asset_folder"], config_data["global_stock"]["snap"]["files_folder"], get_date(config_data["offset"])))
    count_for_multiple_users = global_stock_dividend.select(["account_id", "user_id"]).groupBy(["account_id"]).agg(f.countDistinct("user_id").alias("user_count")).filter(col("user_count") > 1).count()
    if count_for_multiple_users > 0:
        raise Exception("There are accounts which has multiple user ids. count of such accounts are : {}".format(count_for_multiple_users))
    else:
        logging.info("No accounts found which has multiple user ids")
    logging.info("Completed global stock snapshotting")


if __name__ == "__main__":
    start_time = datetime.now()
    bucket_name = config_data["bucket"]
    raw_bucket = "s3a://{}".format(config_data["raw_bucket_name"])
    parser = argparse.ArgumentParser()
    parser.add_argument("--global_stock_ph",help = "flag for ph data")
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)+1
    if args.global_stock_ph:
        raw_bucket =config_data["global_stock_ph"]["raw_bucket"]
        bucket_name =config_data["global_stock_ph"]["bucket_name"]
        global_stock_ph_flag = args.global_stock_ph
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_global_stock_dividend")

