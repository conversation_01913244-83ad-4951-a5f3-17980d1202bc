from common import *
from structs import *

spark = spark_session_create("gss_avg_holding_time")

def get_gss_transactions(t_1):
    df_1 = read_json_data("{}{}/dt={}/".format(config_data["global_stock_metrics"]["raw_bucket"], config_data["global_stock_metrics"]["global_stock_transactions_raw_bucket_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_global_stock_transactions)
    df_1 = df_1.filter(col("status") == "SUCCESS")
    df_1 = df_1.select(["id","global_stock_id","account_id", "quantity", "unit_price", "updated", "status", "transaction_type"])
    return df_1


def start_processing():
    zone = pytz.timezone("Asia/Jakarta")
    t_1 = get_date(config_data["offset"])
    df_1 = get_gss_transactions(t_1)
    primary_keys = ["account_id", "global_stock_id"]
    window = Window.partitionBy([col(x) for x in primary_keys]).orderBy(col("updated").asc())
    df_1_buy = df_1.filter(col("transaction_type") == "BUY")
    df_1_buy = df_1_buy.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
    df_1_buy = df_1_buy.withColumn("buy_ts", col("updated"))

    df_1_sell = df_1.filter(col("transaction_type") == "SELL")
    df_1_sell = df_1_sell.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
    df_1_sell = df_1_sell.withColumn("sell_ts", col("updated"))
    t_2 = get_date(config_data["offset"]+1)


    df_2 = spark.read.csv(config_data["global_stock_metrics"]["metric_bucket"]+config_data["global_stock_metrics"]["metric_folder"]+"/"+config_data["global_stock_metrics"]["avg_hold_time_folder"]+"/dt="+str(t_2)+"/", header=True,inferSchema=True, quote='"', escape='"', multiLine=True)

    df = df_2.join(df_1_buy, on=["account_id", "global_stock_id"], how="full")
    df = df.withColumn("first_buy_ts", when((col("first_buy_ts").isNull()) | (col("first_buy_ts") > col("buy_ts")), col("buy_ts")).otherwise(col("first_buy_ts")).cast(TimestampType()))
    df = df.select("account_id", "global_stock_id", "first_buy_ts", "first_sell_ts", "buy_ts")

    df = df.join(df_1_sell, on=["account_id", "global_stock_id"], how="full")
    df = df.withColumn("first_sell_ts", when(((col("first_buy_ts").isNotNull()) & (col("first_buy_ts") <= col("sell_ts")) & ((col("first_sell_ts").isNull()) | (col("first_sell_ts") > col("sell_ts")))), col("sell_ts")).otherwise(col("first_sell_ts")).cast(TimestampType()))
    df = df.select("account_id", "global_stock_id", "first_buy_ts", "first_sell_ts")

    df = df.withColumn("first_buy", from_utc_timestamp(to_timestamp("first_buy_ts"), "Asia/Jakarta").cast("date"))
    df = df.withColumn("first_sell", from_utc_timestamp(to_timestamp("first_sell_ts"), "Asia/Jakarta").cast("date"))

    first_sell_ts_temp = datetime.strptime(str(t_1), '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)
    df = df.withColumn("first_sell_temp", when((col("first_buy").isNotNull()) & (col("first_sell").isNull()), t_1).otherwise(col("first_sell")))
    df = df.withColumn("first_sell_ts_temp", when((col("first_buy_ts").isNotNull()) & (col("first_sell_ts").isNull()), first_sell_ts_temp).otherwise(col("first_sell_ts")))

    df = df.withColumn("day_diff", when((col("first_buy").isNotNull()) & (col("first_sell_temp").isNotNull()), datediff(col("first_sell_temp"), col("first_buy"))).otherwise(0))
    df = df.withColumn("hour_diff", when(((col("first_buy_ts").isNull()) | (col("first_sell_ts_temp").isNull())), 0).otherwise(round(((col("first_sell_ts_temp").cast(LongType())) - (col("first_buy_ts").cast(LongType())))/3600,0)))
    created = datetime.strptime(str(t_1), '%Y-%m-%d').replace(hour=00, minute=00, second=00, microsecond=000000)
    df = df.withColumn("created", lit(created))
    df = df.drop("first_sell_temp","first_sell_ts_temp")
    df.coalesce(1).write.mode('overwrite').save(config_data["global_stock_metrics"]["metric_bucket"]+config_data["global_stock_metrics"]["metric_folder"]+"/"+config_data["global_stock_metrics"]["avg_hold_time_folder"]+"/dt="+str(t_1)+"/",format="csv", header=True)
    df = df.withColumn("asset_category", lit("globalStocks"))
    df = df.withColumn("asset_id", col("global_stock_id"))
    df_mongo = df.groupBy(["asset_category", "asset_id", "global_stock_id", "created"]).agg(avg("day_diff").cast(LongType()).alias("avg_hold_days"), avg("hour_diff").cast(LongType()).alias("avg_hold_hours"))
    df_mongo = df_mongo.withColumn("asset_id", col("asset_id").cast(LongType()))
    df_mongo = df_mongo.drop("global_stock_id")
    write_asset_returns_to_mongo(df_mongo, config_data["global_stock_metrics"]["mongo_avg_hold_time"], "global_stock Average Hold Time","update", config_data["global_stock_metrics"]["mongo_avg_hold_time"]["shardkey"])


if __name__ == "__main__":
    start_time= datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"global_stock_avg_holding_time")

