from common import *
from structs import *


raw_bucket = config_data["global_stock_pocket"]["raw_bucket"]
spark = spark_session_create("snap_global_stock_pocket_return")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()
cut_off_time=None

def get_current_global_stock_price(asset_name):
    try:
        offset = config_data["offset"]
        t_1 = get_date(offset)
        s3_path  = "s3a://{}/{}/dt=".format(config_data["global_stock"]["bucket_name"],config_data["global_stock"]["price_path"])
        data = spark.read.csv( s3_path + str(t_1) +"/",header=True,inferSchema=True)
        global_stock_partner_prices  = data.withColumn("mid_price",round(col("mid_price"),2))
        global_stock_partner_prices = global_stock_partner_prices.select(col("_id").alias("partner_global_stock_id"),"mid_price")
        logging.info("Successfully loaded {} partner prices".format(asset_name))
        return global_stock_partner_prices

    except Exception as e:
        logging.error("An error has occurred while loading {} partner_prices: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def calculate_global_stock_returns(global_stock_t_0, current_global_stock_prices, asset_name,currency_to_idr):
    try:
        global_stock_t_0 = global_stock_t_0.join(current_global_stock_prices,global_stock_t_0["global_stock_id"] ==current_global_stock_prices["partner_global_stock_id"],"left").drop("partner_global_stock_id")

        global_stock_returns_t_0 = global_stock_t_0.withColumnRenamed("mid_price", "unit_price")

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("total_quantity", round(col("total_quantity").cast("double"),10))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("total_value", col("total_quantity") * col("unit_price"))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("unrealised_gain", col("total_value") - (col("total_quantity") * col("weighted_cost")))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("currency_to_idr", lit(currency_to_idr))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("currency_to_idr", col("currency_to_idr").cast("long"))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("total_value_idr",floor(col("total_quantity") * col("unit_price") * currency_to_idr))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("unrealised_gain_idr", col("total_value_idr") - ceil(col("total_quantity") * col("weighted_cost_in_idr")))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("weighted_cost", col("weighted_cost").cast("double"))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("realised_gain", col("realised_gain").cast("double"))

        global_stock_returns_t_0 = global_stock_returns_t_0.withColumn("total_dividend", col("total_dividend").cast("double"))

        logging.info("Successfully calculated {} returns".format(asset_name))

        return global_stock_returns_t_0

    except Exception as e:
        logging.error("An error has occurred while calculating {} returns: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def get_global_stock_accounts_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    global_stock_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["global_stock_pocket"]["t1"]["asset_accounts_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_global_stock_pocket_accounts)
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["global_stock_pocket"]["t1"]["asset_accounts_folder"]),"id",lowerbound_ts,upperbound_ts)
    global_stock_t_1 = global_stock_t_1.filter(col("updated")<=cut_off_time)
    global_stock_t_1 = global_stock_t_1.filter(~col("id").isin(delete_record))
    logging.info("Count for global stock accounts : {}".format(global_stock_t_1.count()))
    logging.info("Completed spark read")
    logging.info("Saving deduped T1 files back to s3")
    save_de_duped_asset_t_1_to_s3(global_stock_t_1,
                                  config_data["global_stock_pocket"]["bucket"],
                                  config_data["global_stock_pocket"]["de_dupe_t_1"]["asset_accounts_folder"],
                                  config_data["global_stock_pocket"]["de_dupe_t_1"]["files_folder"],
                                  get_date(config_data["offset"]),
                                  config_data["global_stock_pocket"]["asset_name"],
                                  config_data["global_stock_pocket"]["primary_keys"],
                                  config_data["global_stock_pocket"]["accounts_column_order"])

    global_stock_t_2 = get_asset_t_2(config_data["global_stock_pocket"]["bucket"],
                                     config_data["global_stock_pocket"]["t2"]["asset_accounts_folder"],
                                     config_data["global_stock_pocket"]["t2"]["files_folder"],
                                     get_date(config_data["offset"]+1),
                                     schema_for_global_stock_pocket_accounts,
                                     config_data["global_stock_pocket"]["asset_name"])
    global_stock_t_2 = global_stock_t_2.filter(~col("id").isin(delete_record))
    global_stock_t_0 = get_asset_t_0(global_stock_t_1,
                                     global_stock_t_2,
                                     config_data["global_stock_pocket"]["primary_keys"],
                                     config_data["global_stock_pocket"]["asset_name"],
                                     config_data["global_stock_pocket"]["accounts_column_order"])

    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(global_stock_t_0,
                         config_data["global_stock_pocket"]["bucket"],
                         config_data["global_stock_pocket"]["t2"]["asset_accounts_folder"],
                         config_data["global_stock_pocket"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["global_stock_pocket"]["asset_name"])

    return global_stock_t_0


def get_global_stock_returns_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    global_stock_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["global_stock_pocket"]["t1"]["asset_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_global_stock_pocket_returns)
    logging.info("Count for global stock returns : {}".format(global_stock_t_1.count()))
    logging.info("Completed spark read")
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["global_stock_pocket"]["t1"]["asset_folder"]),"id",lowerbound_ts,upperbound_ts)
    global_stock_t_1 = global_stock_t_1.filter(col("updated")<=cut_off_time)
    global_stock_t_1 = global_stock_t_1.filter(~col("id").isin(delete_record))
    logging.info("Saving deduped T1 files back to s3")
    save_de_duped_asset_t_1_to_s3(global_stock_t_1,
                                  config_data["global_stock_pocket"]["bucket"],
                                  config_data["global_stock_pocket"]["de_dupe_t_1"]["asset_folder"],
                                  config_data["global_stock_pocket"]["de_dupe_t_1"]["files_folder"],
                                  get_date(config_data["offset"]),
                                  config_data["global_stock_pocket"]["asset_name"],
                                  config_data["global_stock_pocket"]["primary_keys"],
                                  config_data["global_stock_pocket"]["column_order"])

    global_stock_t_2 = get_asset_t_2(config_data["global_stock_pocket"]["bucket"],
                                     config_data["global_stock_pocket"]["t2"]["asset_folder"],
                                     config_data["global_stock_pocket"]["t2"]["files_folder"],
                                     get_date(config_data["offset"]+1),
                                     schema_for_global_stock_pocket_returns,
                                     config_data["global_stock_pocket"]["asset_name"])
    global_stock_t_2 = global_stock_t_2.filter(~col("id").isin(delete_record))
    global_stock_t_0 = get_asset_t_0(global_stock_t_1,
                                     global_stock_t_2,
                                     config_data["global_stock_pocket"]["primary_keys"],
                                     config_data["global_stock_pocket"]["asset_name"],
                                     config_data["global_stock_pocket"]["column_order"])

    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(global_stock_t_0,
                         config_data["global_stock_pocket"]["bucket"],
                         config_data["global_stock_pocket"]["t2"]["asset_folder"],
                         config_data["global_stock_pocket"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["global_stock_pocket"]["asset_name"])

    global_stock_t_0 = drop_unnecessary_columns(global_stock_t_0,
                                                config_data["global_stock_pocket"]["list_of_unnecessary_columns"])
    return global_stock_t_0


def start_processing():
    logging.info("Starting execution for global stocks Snapshotting")
    get_global_stock_accounts_data()
    global_stock_t_0 = get_global_stock_returns_data()

    current_global_stock_prices = get_current_global_stock_price(
        config_data["global_stock"]["asset_name"])

    current_forex_price = get_current_forex_price(config_data["forex"]["partner_id"],
                                                  config_data["forex"]["forex_id"],
                                                  config_data["forex"]["asset_name"])
    current_forex_price = current_forex_price.select(col("forex_id").alias("partner_forex_id"),
           col("mid_price").alias("partner_mid_price"))

    currency_to_idr = current_forex_price.collect()[0]["partner_mid_price"]

    logging.info("Calculation cost and value using current price")
    global_stock_returns_t_0 = calculate_global_stock_returns(global_stock_t_0,
                                                              current_global_stock_prices,
                                                              config_data["global_stock_pocket"]["asset_name"],currency_to_idr)

    global_stock_returns_t_0 = global_stock_returns_t_0.withColumnRenamed("realised_gain_in_idr", "realised_gain_idr")
    global_stock_returns_t_0 = global_stock_returns_t_0.withColumnRenamed("total_dividend_in_idr", "total_dividend_idr")
    global_stock_returns_t_0 = global_stock_returns_t_0.withColumnRenamed("weighted_cost_in_idr", "weighted_cost_idr")

    logging.info("Updating calculated values to s3")
    save_calculated_asset_returns_t_0_to_s3(global_stock_returns_t_0,
                                            config_data["global_stock_pocket"]["bucket"],
                                            config_data["global_stock_pocket"]["snap"]["asset_folder"],
                                            config_data["global_stock_pocket"]["snap"]["files_folder"],
                                            get_date(config_data["offset"]),
                                            config_data["global_stock_pocket"]["asset_name"])



if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--cut_off_time", help="offset for price cutoff")
    args = parser.parse_args()
    cut_off_time_config = job_config_data["cut_off_time"][args.cut_off_time] if args.cut_off_time else job_config_data["cut_off_time"]["0000JKT"]
    config_data["offset"] = cut_off_time_config["offset"]
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    cut_off_time = get_cutoff_time(cut_off_time_config)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_global_stock_pocket_return")
