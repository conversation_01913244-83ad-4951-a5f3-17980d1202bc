from common import *
from structs import *
from config import *

spark = spark_session_create("snap_crypto_currency_pocket_return")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()
cut_off_time=None
def get_crypto_currency_price_from_mongo(t_1):
    try:
        price_path = "s3a://{}/{}/dt={}/".format(config_data["crypto_currency"]["bucket_name"],
                                                 config_data["crypto_currency"]["price_path"], t_1)
        crypto_currency_partner_prices = spark.read.csv(price_path, header=True, inferSchema=True)
        crypto_currency_partner_prices = crypto_currency_partner_prices.select(
            col("_id").alias("partner_crypto_currency_id"), "mid_price")
        logging.info("Successfully loaded {} partner prices")
        return crypto_currency_partner_prices
    except Exception as e:
        logging.error("An error has occurred while loading partner_prices: {}".format(repr(e)))
        logging.exception(e)
        raise e


def calculate_crypto_currency_returns(crypto_currency_t_0, current_crypto_currency_prices, asset_name):
    try:
        crypto_currency_t_0 = crypto_currency_t_0.join(current_crypto_currency_prices,
                                                       crypto_currency_t_0["crypto_currency_id"] ==
                                                       current_crypto_currency_prices["partner_crypto_currency_id"],
                                                       "left").drop("partner_crypto_currency_id")

        crypto_currency_returns_t_0 = crypto_currency_t_0.withColumnRenamed("mid_price", "unit_price")

        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_quantity",
                                                                             col("total_quantity").cast("double"))

        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("weighted_cost",
                                                                             col("weighted_cost").cast("double"))

        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("realised_gain",
                                                                             col("realised_gain").cast("double"))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_quantity",
                                                                             round(col("total_quantity"), 8))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_value", floor(
            col("total_quantity") * col("unit_price")))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("unrealised_gain",
                                                                             col("total_value") - ceil(
                                                                                 col("total_quantity") * col(
                                                                                     "weighted_cost")))

        logging.info("Successfully calculated {} returns".format(asset_name))

        return crypto_currency_returns_t_0

    except Exception as e:
        logging.error("An error has occurred while calculating {} returns: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def get_snapshot_data(asset_config, t_1, t_2, snapshot_type, schema):
    snapshot_config = asset_config[snapshot_type]
    logging.info("Reading t1 files from s3")
    crypto_pocket_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], snapshot_config["raw_folder"], t_1), is_raw=True, schema_for_empty_df=schema)
    logging.info("Count for {} : {}".format(crypto_pocket_t_1.count(), snapshot_config["asset_name"]))
    logging.info("Completed spark read")
    crypto_pocket_t_1 = crypto_pocket_t_1.filter(col("updated")<=cut_off_time)
    delete_record = read_deleted_record("s3a://{}/{}/dt=".format(config_data["bucket"],snapshot_config["raw_folder"]),"id",lowerbound_ts,upperbound_ts)
    crypto_pocket_t_1 = crypto_pocket_t_1.filter(~col("id").isin(delete_record))
    logging.info("Saving deduped T1 files back to s3")
    save_de_duped_asset_t_1_to_s3(crypto_pocket_t_1,
                                  config_data["bucket"],
                                  snapshot_config["asset_folder"],
                                  asset_config["de_dupe_t_1_folder"],
                                  t_1,
                                  snapshot_config["asset_name"],
                                  asset_config["primary_keys"],
                                  snapshot_config["column_order"])
    crypto_pocket_t_2 = get_asset_t_2(config_data["bucket"],
                                      snapshot_config["asset_folder"],
                                      asset_config["t2_files_folder"],
                                      t_2,
                                      schema,
                                      snapshot_config["asset_name"])
    crypto_pocket_t_2 = crypto_pocket_t_2.filter(~col("id").isin(delete_record))
    crypto_currency_pocket_t_0 = get_asset_t_0(crypto_pocket_t_1,
                                               crypto_pocket_t_2,
                                               asset_config["primary_keys"],
                                               snapshot_config["asset_name"],
                                               snapshot_config["column_order"])
    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(crypto_currency_pocket_t_0,
                         config_data["bucket"],
                         snapshot_config["asset_folder"],
                         asset_config["t2_files_folder"],
                         t_1,
                         snapshot_config["asset_name"])
    crypto_currency_t_0 = drop_unnecessary_columns(crypto_currency_pocket_t_0,
                                                   asset_config["list_of_unnecessary_columns"])
    return crypto_currency_t_0


def start_processing():
    logging.info("Starting execution for crypto currency pocket snapshotting")
    asset_config = job_config_data["crypto_currency_pocket"]
    t_1 = get_date(config_data["offset"])
    t_2 = get_date(config_data["offset"] + 1)
    crypto_currency_returns_t_0 = get_snapshot_data(asset_config, t_1, t_2, "crypto_currency_pocket_returns",
                                                    schema_for_crypto_currency_pocket_returns)
    crypto_currency_accounts_t_0 = get_snapshot_data(asset_config, t_1, t_2, "crypto_currency_pocket_accounts",
                                                     schema_for_crypto_currency_pocket_accounts)

    crypto_currency_t_0 = crypto_currency_returns_t_0.join(crypto_currency_accounts_t_0, (
            (crypto_currency_returns_t_0.account_id == crypto_currency_accounts_t_0.account_id) & (
            crypto_currency_returns_t_0.crypto_currency_id == crypto_currency_accounts_t_0.crypto_currency_id) & (
                    crypto_currency_returns_t_0.user_pocket_id == crypto_currency_accounts_t_0.user_pocket_id)),
                                                           "inner").drop(
        crypto_currency_returns_t_0["total_quantity"]).drop(crypto_currency_accounts_t_0["account_id"]).drop(
        crypto_currency_accounts_t_0["crypto_currency_id"]).drop(crypto_currency_accounts_t_0["user_pocket_id"]).drop(
        crypto_currency_accounts_t_0["created"])

    current_crypto_currency_prices = get_crypto_currency_price_from_mongo(t_1)

    crypto_currency_t_0 = crypto_currency_t_0.withColumnRenamed("balance", "total_quantity")
    logging.info("Calculation cost and value using current price")
    crypto_currency_t_0 = calculate_crypto_currency_returns(crypto_currency_t_0,
                                                            current_crypto_currency_prices,
                                                            "crypto_currency_pocket")

    crypto_currency_t_0 = drop_unnecessary_columns(crypto_currency_t_0,
                                                   asset_config["removal_list_after_merge"])

    logging.info("Updating calculated values to s3")
    save_calculated_asset_returns_t_0_to_s3(crypto_currency_t_0,
                                            config_data["bucket"],
                                            asset_config["crypto_currency_pocket_returns"]["asset_folder"],
                                            asset_config["snap_folder"],
                                            t_1,
                                            "crypto_currency_pocket")


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--cut_off_time", help="offset for price cutoff")
    args = parser.parse_args()
    cut_off_time_config = job_config_data["cut_off_time"][args.cut_off_time] if args.cut_off_time else job_config_data["cut_off_time"]["0000JKT"]
    config_data["offset"] = cut_off_time_config["offset"]
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    cut_off_time = get_cutoff_time(cut_off_time_config)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_crypto_currency_pocket_return")

