from common import *
from structs import *
from config import *

spark = spark_session_create("crypto_yield_calculation")


def add_eligible_quantity_for_yield(df):
    if (config_data["crypto_yield"].get("yield_max_quantity") is None) or (len(config_data["crypto_yield"]["yield_max_quantity"].items()) == 0):
        df = df.withColumn("eligible_quantity", col("balance"))
    else:
        crypto_max_eligible_quantity_tuples = [(k, v) for k, v in config_data["crypto_yield"]["yield_max_quantity"].items()]
        df_max_eligible = spark.createDataFrame(crypto_max_eligible_quantity_tuples, ["crypto_currency_id", "max_eligible_quantity"])
        df = df.join(df_max_eligible, on=["crypto_currency_id"], how="left")
        df = df.withColumn("eligible_quantity", when(((col("max_eligible_quantity").isNotNull()) & (col("max_eligible_quantity") <= col("balance"))), col("max_eligible_quantity")).otherwise(col("balance")))
    return df.drop("max_eligible_quantity")


def get_crypto_currency_accounts_data(s3_path,offset,asset_name):
    """
    get account data till 7 AM JKT of running day
    """
    dt = get_date(offset)
    if asset_name == "crypto_account":
        crypto_currency_t_1 = read_json_data("{}{}/".format(s3_path, dt), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_accounts)
    if asset_name =="crypto_pocket":
        crypto_currency_t_1 = read_json_data("{}{}/".format(s3_path, dt), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_pocket_accounts)

    dt_time = get_date_for_query(offset)
    hour= config_data["crypto_yield"]["filter_time"]["hour"]
    minute= config_data["crypto_yield"]["filter_time"]["minute"]
    second= config_data["crypto_yield"]["filter_time"]["second"]
    microsecond = config_data["crypto_yield"]["filter_time"]["microsecond"]
    dt_time =dt_time.replace(hour=hour,minute=minute,second=second,microsecond=microsecond).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    crypto_currency_t_1 = crypto_currency_t_1.filter(col("updated")< dt_time ).distinct()
    return crypto_currency_t_1


def get_pluang_cuan_active(bucket_name,asset_folder_name,offset,asset_schema):
    date_t_0 = str(get_date(offset))
    date_t_1 = str(get_date(offset+1))
    folder_name = config_data["crypto_yield"]["pluang_cuan_user"]["folder_name"]
    t_1_folder = config_data["crypto_yield"]["pluang_cuan_user"]["t_1_folder"]
    snap_folder = config_data["crypto_yield"]["pluang_cuan_user"]["snap_folder"]
    raw_folder_name = config_data["crypto_yield"]["raw_folder_name"]
    table_name = config_data["crypto_yield"]["pluang_cuan_user"]["table_name"]
    primary_keys = config_data["crypto_yield"]["pluang_cuan_user"]["primary_keys"]
    column_order = config_data["crypto_yield"]["pluang_cuan_user"]["column_order"]
    #read previous day pluang_curn user
    path_to_s3 = "s3a://{}/{}/{}/dt={}/".format(bucket_name, raw_folder_name, table_name,date_t_1)
    logging.info("Reading t1 files from path {}".format(path_to_s3))
    asset_t_1 = read_json_data(path_to_s3, is_raw=True, schema_for_empty_df=schema_for_crypto_currency_account_settings)
    logging.info("{}_t_1 loaded successfully from {} bucket".format(asset_folder_name, bucket_name))
    #read current day pluang_curn user update
    path_to_s3 = "s3a://{}/{}/{}/dt={}/".format(bucket_name, raw_folder_name, table_name,date_t_0)
    logging.info("Reading t1 files from path {}".format(path_to_s3))
    asset_t_0 = read_json_data(path_to_s3, is_raw=True, schema_for_empty_df=schema_for_crypto_currency_account_settings)
    logging.info("{}_t_2 loaded successfully from {} bucket".format(asset_folder_name, bucket_name))
    asset_t_1_columns = asset_t_1.columns
    asset_t_0_columns = asset_t_0.columns
    asset_t_1_columns.sort()
    asset_t_0_columns.sort()
    asset_t_0 = asset_t_0.select(column_order)
    asset_t_1 = asset_t_1.select(column_order)
    plug_cuan_user_t_0 = asset_t_0.union(asset_t_1)
    dt_time = get_date_for_query(offset)
    hour= config_data["crypto_yield"]["filter_time"]["hour"]
    minute= config_data["crypto_yield"]["filter_time"]["minute"]
    second= config_data["crypto_yield"]["filter_time"]["second"]
    microsecond = config_data["crypto_yield"]["filter_time"]["microsecond"]
    dt_time =dt_time.replace(hour=hour,minute=minute,second=second,microsecond=microsecond).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    plug_cuan_user_t_0 = plug_cuan_user_t_0.filter(col("updated")< dt_time ).distinct()
    snap_path  =  "s3a://{}/{}/{}/{}/dt=".format(bucket_name, asset_folder_name, folder_name,snap_folder)
    snap_plug_cuan_user_t_1 = spark.read.csv(snap_path + date_t_1 + "/*.csv", header=True, inferSchema=True)
    snap_plug_cuan_user_t_1 = snap_plug_cuan_user_t_1.select("id","user_id","account_id","client_id","partner_id","pluang_cuan_waitlisted","is_pluang_cuan_active","created","updated","pluang_cuan_joining_date")
    plug_cuan_user_t_0 = plug_cuan_user_t_0.select(column_order)
    snap_plug_cuan_user_t_1 = snap_plug_cuan_user_t_1.select(column_order)
    snap_plug_cuan_user_t_0 = get_asset_t_0(plug_cuan_user_t_0, snap_plug_cuan_user_t_1, primary_keys, asset_folder_name, column_order)
    snap_plug_cuan_user_t_0  = snap_plug_cuan_user_t_0.filter((col("is_pluang_cuan_active")==True))
    snap_plug_cuan_user_t_0.coalesce(1).write.mode('overwrite').save(snap_path + date_t_0 ,format="csv", header=True)
    return snap_plug_cuan_user_t_0


def get_price_from_url(url):
    try:
        response = requests.get(url)
        if response.status_code > 200:
            raise requests.exceptions.HTTPError
        json_data = json.dumps(response.json())
        json_data_list = []
        json_data_list.append(json_data)
        json_rdd = spark.sparkContext.parallelize(json_data_list)
        df = spark.read.json(json_rdd)
        df = df.select("data.sellPrice")
        return df
    except (requests.exceptions.Timeout, requests.exceptions.HTTPError,
            requests.exceptions.ConnectionError, Exception) as errt:
        logging.error("An timeout error has occurred api call for crypto price")
        logging.exception(errt)
        raise errt


def start_processing():
    asset_config = job_config_data["crypto_currency_pocket"]["crypto_currency_pocket_accounts"]
    url_btc = config_data["crypto_yield"]["crypto_price_api_Btc"]
    url_eth = config_data["crypto_yield"]["crypto_price_api_Eth"]
    crypto_price_btc = get_price_from_url(url_btc)
    crypto_price_eth = get_price_from_url(url_eth)
    df_btc = crypto_price_btc.withColumn("cryptocurrencyId",lit(10000))
    df_eth =crypto_price_eth.withColumn("cryptocurrencyId",lit(10001))
    crypto_price = df_btc.union(df_eth)
    logging.info("Starting execution for crypto yield calculation")
    offset = config_data["offset"]-1
    current_day_s3_path = config_data["crypto_currency"]["raw_bucket"] + config_data["crypto_currency"]["t1"]["asset_account_folder"]+"/dt="
    asset_name_crypto = "crypto_account"
    crypto_currency_accounts_t_0 = get_crypto_currency_accounts_data(current_day_s3_path,offset,asset_name_crypto)
    crypto_currency_account_t_1 = get_asset_t_2(config_data["crypto_currency"]["t2"]["bucket"],
                                                config_data["crypto_currency"]["t2"]["asset_account_folder"],
                                                config_data["crypto_currency"]["t2"]["files_folder"],
                                                get_date(config_data["offset"]),
                                                schema_for_crypto_currency_accounts,
                                                config_data["crypto_currency"]["asset_name"])
    crypto_currency_accounts_balance = get_asset_t_0(crypto_currency_accounts_t_0,crypto_currency_account_t_1,config_data["crypto_currency"]["primary_keys"],
                                                     config_data["crypto_currency"]["asset_name"],
                                                     config_data["crypto_currency"]["accounts_column_order"])
    #crypto pocket
    current_day_s3_pocket_path = "s3a://{}/{}/dt=".format(config_data["bucket"], asset_config["raw_folder"])
    asset_name_pocket = "crypto_pocket"
    crypto_currency_pocket_accounts_t_0 = get_crypto_currency_accounts_data(current_day_s3_pocket_path,offset,asset_name_pocket)
    crypto_currency_pocekt_account_t_1 = get_asset_t_2(config_data["bucket"],
                                                asset_config["asset_folder"],
                                                asset_config["t2_files_folder"],
                                                get_date(config_data["offset"]),
                                                schema_for_crypto_currency_pocket_accounts,
                                                asset_config["asset_name"])

    crypto_currency_pocket_accounts_balance = get_asset_t_0(crypto_currency_pocket_accounts_t_0,crypto_currency_pocekt_account_t_1,job_config_data["crypto_currency_pocket"]["primary_keys"],
                                                            asset_config["asset_name"],
                                                            asset_config["column_order"])


    coins_list = config_data["crypto_yield"]["coins_list"]
    crypto_currency_accounts_balance = crypto_currency_accounts_balance.filter(col("crypto_currency_id").isin(coins_list))
    bucket_name = config_data["crypto_yield"]["bucket_name"]
    asset_folder_name = config_data["crypto_yield"]["assert_name"]
    account_files_folder = config_data["crypto_yield"]["account_folder_name"]
    dt = get_date(offset)
    #write account balance in s3 path on running day
    crypto_currency_accounts_balance.coalesce(1).write.mode('overwrite').save(
        "s3a://{}/{}/{}/dt={}".format(bucket_name, asset_folder_name, account_files_folder,str(dt)),
        format="csv", header=True)
    cols= ["crypto_currency_id","user_id","account_id","partner_id"]
    select_column = cols + ["balance"]
    crypto_currency_pocket_accounts_balance = crypto_currency_pocket_accounts_balance.filter(col("crypto_currency_id").isin(coins_list)).select(select_column)
    crypto_currency_accounts_balance  = crypto_currency_accounts_balance.select(select_column)
    crypto_currency_accounts_balance = crypto_currency_accounts_balance.union(crypto_currency_pocket_accounts_balance)

    crypto_currency_accounts_balance = crypto_currency_accounts_balance.groupBy(cols).agg(round(sum("balance"),8).alias("balance"))
    crypto_currency_accounts_balance = crypto_currency_accounts_balance.withColumn("client_id",lit(3))
    #pluangcuan level data
    crypto_currency_pluangcuan_levels = spark.read.format("jdbc").option("user", config_data["crypto_yield"]["postgres"]["username"]).option("password",config_data["crypto_yield"]["postgres"]["password"]).option("url", config_data["crypto_yield"]["postgres"]["url"]).option("dbtable", config_data["crypto_yield"]["postgres"]["db_table"]).load()
    crypto_currency_pluangcuan_levels = crypto_currency_pluangcuan_levels.filter(col("active")==True).select("crypto_currency_id","level","minimum_quantity","maximum_quantity","interest_rate","active").withColumnRenamed("crypto_currency_id","crypto_currency_id_dup")
    crypto_account_with_interest  = crypto_currency_accounts_balance.join(crypto_currency_pluangcuan_levels,((crypto_currency_accounts_balance.crypto_currency_id== crypto_currency_pluangcuan_levels.crypto_currency_id_dup) & (crypto_currency_accounts_balance.balance > crypto_currency_pluangcuan_levels.minimum_quantity) & ((crypto_currency_accounts_balance.balance <= crypto_currency_pluangcuan_levels.maximum_quantity) | (crypto_currency_pluangcuan_levels.maximum_quantity.isNull()))),"inner").drop("crypto_currency_id_dup")
    #pluang_curn user
    asset_schema = config_data["crypto_yield"]["pluang_cuan_user"]["asset_schema"]
    snap_plug_cuan_user_t_0 = get_pluang_cuan_active(bucket_name,asset_folder_name,offset,asset_schema)
    snap_plug_cuan_user_t_0 = snap_plug_cuan_user_t_0.filter(col("is_pluang_cuan_active")==True).select("account_id","is_pluang_cuan_active").withColumnRenamed("account_id","account_id_dup")

    yield_elgible_user = crypto_account_with_interest.join(snap_plug_cuan_user_t_0,crypto_account_with_interest.account_id==snap_plug_cuan_user_t_0.account_id_dup,"inner")

    yield_elgible_user = add_eligible_quantity_for_yield(yield_elgible_user)
    crypto_currency_yield = yield_elgible_user.withColumn("yield_quantity",f.round(((col("eligible_quantity")* f.pow((1 + f.col("interest_rate") / 100), 1 / 365))- col("eligible_quantity")),8)).drop("level","minimum_quantity","maximum_quantity","active","account_id_dup","is_pluang_cuan_active")
    crypto_currency_yield_transaction = crypto_currency_yield.join(crypto_price,crypto_currency_yield.crypto_currency_id==crypto_price.cryptocurrencyId,"inner")
    crypto_currency_yield_transaction = crypto_currency_yield_transaction.withColumnRenamed("balance","principal_quantity").withColumnRenamed("interest_rate","yield_percentage").withColumnRenamed("sellPrice","unit_price").withColumn("status",lit("PENDING")).withColumn("effective_date",lit(str(dt))).withColumn("info",lit(None).cast(StringType())).drop("cryptocurrencyId")
    current_time = datetime.now()
    crypto_currency_yield_transaction = crypto_currency_yield_transaction.withColumn("created",lit(current_time)).withColumn("updated",lit(current_time)).withColumn("transaction_time",lit(current_time))
    yield_calculation_files_folder =  config_data["crypto_yield"]["yield_calculation_files_folder"]
    yield_cols = ["crypto_currency_id","user_id","account_id","client_id","partner_id","principal_quantity", "eligible_quantity","created","updated","yield_percentage","yield_quantity","unit_price","status","effective_date","info","transaction_time"]
    crypto_currency_yield_transaction = crypto_currency_yield_transaction.select(yield_cols)
    try:
        crypto_currency_yield_transaction.coalesce(1).write.save("s3a://{}/{}/{}/dt={}".format(bucket_name, asset_folder_name, yield_calculation_files_folder,str(dt)),format="csv", header=True)
    except Exception as e:
        logging.error("file does not save")
        logging.exception(e)
        raise e


if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"crypto_yield_calculation")


