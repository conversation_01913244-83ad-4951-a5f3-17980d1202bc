from common import *
from config import *

spark = spark_session_create("pocket_mongo_write")
def get_struct(df):
    original_columns = df.columns
    for col in original_columns:
        input_str = col
        if col == "created":
            input_str = input_str + "_at"

        renamed_col = convert_snake_to_camel_case(input_str)
        df = df.withColumnRenamed(col, renamed_col)
    new_cols = df.columns
    new_cols.remove("accountId")
    new_cols.remove("createdAt")
    new_cols.remove("userPocketId")
    df = df.withColumn("pocketAssetReturns", f.struct(new_cols))
    return df


if __name__ == "__main__":
    start_time = datetime.now()
    write_format = config_data["pocket_mongo_write"]["mongo"]["write_format"]
    shardkey = config_data["pocket_mongo_write"]["mongo"]["shardkey"]
    global_stock_pocket_df = get_asset_snap(config_data["bucket"],
                                     config_data["global_stock_pocket"]["snap"]["asset_folder"],
                                     config_data["global_stock_pocket"]["snap"]["files_folder"],
                                     get_date(config_data["offset"]),
                                     config_data["global_stock_pocket"]["asset_name"])
    global_stock_pocket_df = global_stock_pocket_df.withColumn("asset_category", lit(config_data["pocket_mongo_write"]["assets"]["global_stock_pocket"]["asset_category"]))
    global_stock_pocket_df = global_stock_pocket_df.withColumnRenamed(config_data["pocket_mongo_write"]["assets"]["global_stock_pocket"]["asset_key"], "asset_id")
    global_stock_pocket_df = get_struct(global_stock_pocket_df)

    crypto_pocket_df = get_asset_snap(config_data["bucket"],
                                        job_config_data["crypto_currency_pocket"]["crypto_currency_pocket_returns"]["asset_folder"],
                                        job_config_data["crypto_currency_pocket"]["snap_folder"],
                                        get_date(config_data["offset"]),
                                        "crypto_currency_pocket")
    crypto_pocket_df = crypto_pocket_df.withColumn("asset_category", lit(config_data["pocket_mongo_write"]["assets"]["crypto_currency_pocket"]["asset_category"]))
    crypto_pocket_df = crypto_pocket_df.withColumnRenamed(config_data["pocket_mongo_write"]["assets"]["crypto_currency_pocket"]["asset_key"], "asset_id")
    crypto_pocket_df = get_struct(crypto_pocket_df)

    union_df = global_stock_pocket_df.unionByName(crypto_pocket_df, allowMissingColumns=True)
    union_df = union_df.groupBy(["accountId", "createdAt", "userPocketId"]).agg(f.collect_list("pocketAssetReturns").alias("pocketAssetReturns"))
    mongo_config = config_data["pocket_mongo_write"]["mongo"]
    if write_format == "update":
        union_df.write.format("mongo").option("replaceDocument", "false").option("shardKey", shardkey).option(
            "uri", mongo_config["uri"]).option("batchsize", mongo_config["batch_size"]). \
            mode(mongo_config["mode"]).save()
    else:
        union_df.write.format("mongo").option("uri", mongo_config["uri"]).option("batchsize",
                                                                                          mongo_config["batch_size"]). \
            mode(mongo_config["mode"]).save()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"asset_mongo_write_in_pockets")

