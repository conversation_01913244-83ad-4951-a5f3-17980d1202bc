from common import *
import argparse
import time
from pyspark.sql.functions import row_number, col, isnan, when, count, lit

spark = spark_session_create("asset_mongo_write")

def start_processing():
    start_time = datetime.now()
    asset = "fund"
    parser = argparse.ArgumentParser()
    parser.add_argument("--asset_name", help="asset name of mongo write")
    parser.add_argument("--write_format", help="please enter value update or insert")
    parser.add_argument("--offset", help="please enter value of offset")
    args = parser.parse_args()
    if args.asset_name:
        asset = args.asset_name

    write_format = config_data[asset]["mongo"]["write_format"]
    app_name = "mongo_write_" + str(asset)
    spark.conf.set('spark.app.name',app_name )
    if args.write_format:
        write_format = args.write_format
    if args.offset:
        config_data["offset"]= int(args.offset)
    shardkey = config_data[asset]["mongo"]["shardkey"]
    returns_t_0 = get_asset_snap(config_data[asset]["snap"]["bucket"],
                                 config_data[asset]["snap"]["asset_folder"],
                                 config_data[asset]["snap"]["files_folder"],
                                 get_date(config_data["offset"]),
                                 config_data[asset]["asset_name"])
    logging.info("Filtering all accounts with 0 balance")
    returns_t_0 = returns_t_0.where((returns_t_0.total_quantity > 0))
    num_of_partition= config_data["num_of_partition"]
    sleep_time_mongo = config_data["sleep_time_mongo"]
    if asset == "gold" or asset == "crypto_currency":
        if asset == "gold":
            returns_t_0 = returns_t_0.where((returns_t_0.partner_id.isin(config_data[asset]["whitelisted_partner_ids"])))
            logging.info("Dropping unwanted columns for mongo")
            returns_t_0 = drop_unnecessary_columns(returns_t_0, config_data[asset]["removal_list_after_merge"])
            logging.info("Updating calculated value to mongo")
        else:
            returns_t_0 = returns_t_0.drop("reward_balance", "total_reward_value")
        for i in range(0,num_of_partition):
            logging.info("writing mongo insert partition number " + str(i))
            df = returns_t_0.filter(col("account_id") % num_of_partition == i)
            # for old returns
            if asset != "gold":
                write_asset_returns_to_mongo(df, config_data[asset]["mongo"], config_data[asset]["asset_name"],write_format,shardkey)
                time.sleep(sleep_time_mongo)
            if asset == "gold":
                temp_config = config_data[asset]["mongo"]
                temp_config["uri"] = temp_config["array_uri"]
                write_asset_returns_to_mongo(df, config_data[asset]["mongo"], config_data[asset]["asset_name"],write_format,shardkey)
            else:
                write_asset_returns_to_mongo_array_format(df, config_data[asset]["mongo"], config_data[asset]["asset_name"],write_format,config_data[asset]["mongo"]["array_shardkey"])
            time.sleep(sleep_time_mongo)

    else:
        write_asset_returns_to_mongo(returns_t_0, config_data[asset]["mongo"], config_data[asset]["asset_name"],write_format,shardkey)
        if asset != "indo_stock":
            time.sleep(sleep_time_mongo)
            write_asset_returns_to_mongo_array_format(returns_t_0, config_data[asset]["mongo"], config_data[asset]["asset_name"],write_format,config_data[asset]["mongo"]["array_shardkey"])
    logging.info("Completed mongo write" + asset)
    end_time = datetime.now()
    job_name = "asset_mongo_write_{}".format(asset)
    job_time_metrics(start_time,end_time,job_name)


if __name__ == "__main__":
    start_processing()