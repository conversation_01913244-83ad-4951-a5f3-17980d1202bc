from common import *
import argparse
from config import *

spark = spark_session_create("asset_mongo_write_in_pockets")

def asset_write_in_mongo_for_global_stock(df):
    columns = df.columns
    aggregation_key = ["account_id", "created", "unit_price", "global_stock_id", "currency_to_idr"]
    df = df.withColumn("weighted_cost", (ceil(col("weighted_cost") * col("total_quantity") * 1000000)) / 1000000)
    df = df.withColumn("weighted_cost_idr", (ceil(col("weighted_cost_idr") * col("total_quantity") * 1000000)) / 1000000)
    sum_col = []
    for cls in columns:
        if (cls not in aggregation_key) and (cls != "user_pocket_id"):
            sum_col.append(cls)
    df.createOrReplaceTempView("df")
    sql = "select " + ','.join(aggregation_key)
    for sum_cl in sum_col:
        sql = sql + ", sum(" + sum_cl + ") as " + sum_cl
    sql = sql + ", collect_list(user_pocket_id) as user_pocket_id"
    sql = sql + " from df group by " + ','.join(aggregation_key)
    df = spark.sql(sql)
    df = df.withColumn("total_quantity", round("total_quantity", 12))
    df = df.withColumn("weighted_cost", round(col("weighted_cost")/col("total_quantity"), 6))
    df = df.withColumn("weighted_cost_idr", ceil(col("weighted_cost_idr")/col("total_quantity")))
    df = df.withColumn("realised_gain", round("realised_gain", 2))
    df = df.withColumn("realised_gain_idr", ceil("realised_gain_idr"))
    df = df.withColumn("total_dividend", round("realised_gain", 2))
    df = df.withColumn("total_dividend_idr", ceil("total_dividend_idr"))
    df = df.withColumn("total_value", round("total_value", 6))
    df = df.withColumn("total_value_idr", ceil("total_value_idr"))
    df = df.withColumn("unrealised_gain", round("unrealised_gain", 2))
    df = df.withColumn("unrealised_gain_idr", ceil("unrealised_gain_idr"))
    return df


def asset_write_in_mongo_for_crypto_currency(df):
    columns = df.columns
    aggregation_key = ["account_id", "created", "unit_price", "crypto_currency_id"]
    df = df.withColumn("weighted_cost", ceil(col("weighted_cost") * col("total_quantity")))
    sum_col = []
    for cls in columns:
        if (cls not in aggregation_key) and (cls != "user_pocket_id"):
            sum_col.append(cls)
    df.createOrReplaceTempView("df")
    sql = "select " + ','.join(aggregation_key)
    for sum_cl in sum_col:
        sql = sql + ", sum(" + sum_cl + ") as " + sum_cl
    sql = sql + ", collect_list(user_pocket_id) as user_pocket_id"
    sql = sql + " from df group by " + ','.join(aggregation_key)
    df = spark.sql(sql)
    df = df.withColumn("total_quantity", round("total_quantity", 8))
    df = df.withColumn("weighted_cost", ceil(col("weighted_cost")/col("total_quantity")))
    df = df.withColumn("realised_gain", ceil("realised_gain"))
    df = df.withColumn("total_value", ceil("total_value"))
    df = df.withColumn("unrealised_gain", ceil("unrealised_gain"))
    return df


if __name__ == "__main__":
    start_time = datetime.now()
    job_type=None
    parser = argparse.ArgumentParser()
    parser.add_argument("--asset_name", help="asset name of mongo write")
    parser.add_argument("--write_format", help="please enter value update or insert")
    parser.add_argument("--job_type",help="add job type for morning_write")
    parser.add_argument("--offset", help="please enter value of offset")
    args = parser.parse_args()
    if args.asset_name:
        asset = args.asset_name
    write_format = config_data["global_stock_pocket"]["mongo"]["write_format"]

    if args.write_format:
        write_format = args.write_format
    if args.offset:
        config_data["offset"]= int(args.offset)
    if args.job_type:
        job_type =args.job_type
    if job_type == "gss_mongo_write_market_close":
        shardkey = config_data["global_stock_pocket"]["mongo"]["shardkey"]
        df = get_asset_snap(config_data["bucket"],
                            config_data["global_stock_pocket"]["snap"]["asset_folder"],
                            config_data["global_stock_pocket"]["snap"]["files_folder"],
                            get_date(config_data["offset"]),
                            config_data["global_stock_pocket"]["asset_name"])
        df = asset_write_in_mongo_for_global_stock(df)
        config_data["global_stock_pocket"]["mongo"]["uri"] = config_data["reporting_uri"]+"."+config_data["global_stock_pocket"]["mongo"]["collection"]+"?authSource=admin"
        write_asset_returns_to_mongo_array_format(df.drop("created"), config_data["global_stock_pocket"]["mongo"],
                                                  config_data["global_stock_pocket"]["asset_name"], write_format, shardkey)
    
    else:
        shardkey = job_config_data["crypto_currency_pocket"]["mongo"]["shardkey"]
        df = get_asset_snap(config_data["bucket"],
                            job_config_data["crypto_currency_pocket"]["crypto_currency_pocket_returns"]["asset_folder"],
                            job_config_data["crypto_currency_pocket"]["snap_folder"],
                            get_date(config_data["offset"]),
                            "crypto_currency_pocket")
        df = asset_write_in_mongo_for_crypto_currency(df)
        job_config_data["crypto_currency_pocket"]["mongo"]["array_uri"] = config_data["reporting_uri"]+"."+job_config_data["crypto_currency_pocket"]["mongo"]["collection"]+"?authSource=admin"
        job_config_data["crypto_currency_pocket"]["mongo"]["array_collection"] = job_config_data["crypto_currency_pocket"]["mongo"]["collection"]
        write_asset_returns_to_mongo_array_format(df.drop("created"), job_config_data["crypto_currency_pocket"]["mongo"],
                                                  "crypto_currency_pocket", write_format, shardkey)

    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"asset_mongo_write_in_pockets")
