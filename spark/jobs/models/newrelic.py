from newrelic_telemetry_sdk import GaugeMetric, SummaryMetric, CountMetric, MetricClient
from newrelic_telemetry_sdk.client import HTTPError
import time


class NewRelic:
    def __init__(self, newrelic_api_key, newrelic_integration_enabled, env, logging):
        self.metric_client = MetricClient(newrelic_api_key)
        self.newrelic_integration_enabled = newrelic_integration_enabled
        self.env = env
        self.logging = logging

    def send_newrelic_gauge_metrics(self, key, value, app_name, **kwargs):
        if self.newrelic_integration_enabled == 1:
            tags = {"app_name": app_name, "metrics": "record_count", "enviornment": self.env, "unit": "number"}
            tag_keys = tags.keys()
            for tag_key in tag_keys:
                if kwargs.get(tag_key) is not None:
                    tags[tag_key] = kwargs[tag_key]
            metrics = [
                GaugeMetric(
                    "spark-job.{}".format(key), value, tags
                )
            ]
            self.send_metrics_batch(key, metrics)

    def send_metrics_batch(self, key, metrics, attempt=1, max_attempts=3):
        try:
            response = self.metric_client.send_batch(metrics)
            response.raise_for_status()
            self.logging.info("Job metrics {} is published successfully!".format(key))
        except HTTPError as http_error:
            if attempt >= max_attempts:
                self.logging.error("Retry exhausted for sending metrics to New Relic, quitting!")
                return
            self.logging.warning(
                "HTTPError encountered, retrying in 5 seconds (Attempt {} of {}): {}".format(attempt, max_attempts, http_error)
            )
            time.sleep(1)
            self.send_metrics_batch(key, metrics, attempt + 1)
        except Exception as e:
            self.logging.error("An unknown error occurred: {}".format(e))
