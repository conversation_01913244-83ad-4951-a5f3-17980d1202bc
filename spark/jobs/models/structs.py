from pyspark.sql.types import LongType, StringType, TimestampType, StructType, StructField, IntegerType, DoubleType, BooleanType, FloatType

schema_for_gold_returns = StructType([StructField("id", LongType(), True),
                                      StructField("account_id", LongType(), True),
                                      StructField("total_quantity", StringType(), True),
                                      StructField("unit_price", LongType(), True),
                                      StructField("weighted_cost", LongType(), True),
                                      StructField("last_weighted_cost", LongType(), True),
                                      StructField("balance_price", LongType(), True),
                                      StructField("return_pnl", LongType(), True),
                                      StructField("return_percentage", StringType(), True),
                                      StructField("created", TimestampType(), True),
                                      StructField("updated", TimestampType(), True),
                                      StructField("total_cost", LongType(), True),
                                      StructField("realised_gain", LongType(), True)])

schema_for_gold_accounts = StructType([StructField("account_id", LongType(), True),
                                       StructField("gold_balance", StringType(), True),
                                       StructField("gold_balance_frozen", StringType(), True),
                                       StructField("created", TimestampType(), True),
                                       StructField("updated", TimestampType(), True),
                                       StructField("partner_id", LongType(), True),
                                       StructField("client_id", LongType(), True),
                                       StructField("user_id", LongType(), True),
                                       StructField("first_order_date", TimestampType(), True)])

schema_for_crypto_currency_returns = StructType([StructField("id", LongType(), True),
                                                 StructField("account_id", LongType(), True),
                                                 StructField("user_id", LongType(), True),
                                                 StructField("crypto_currency_id", LongType(), True),
                                                 StructField("total_quantity", StringType(), True),
                                                 StructField("weighted_cost", StringType(), True),
                                                 StructField("realised_gain", StringType(), True),
                                                 StructField("created", TimestampType(), True),
                                                 StructField("updated", TimestampType(), True)])

schema_for_crypto_currency_accounts = StructType([StructField("id", LongType(), True),
                                                  StructField("crypto_currency_id", LongType(), True),
                                                  StructField("user_id", LongType(), True),
                                                  StructField("account_id", LongType(), True),
                                                  StructField("client_id", LongType(), True),
                                                  StructField("partner_id", LongType(), True),
                                                  StructField("balance", StringType(), True),
                                                  StructField("created", TimestampType(), True),
                                                  StructField("updated", TimestampType(), True),
                                                  StructField("first_order_date", TimestampType(), True),
                                                  StructField("total_yield_balance", LongType(), True),
                                                  StructField("current_yield_balance", LongType(), True),
                                                  StructField("reward_balance", StringType(), True),
                                                  StructField("pending_reward_balance", StringType(), True),
                                                  StructField("total_reward_balance", StringType(), True),
                                                  StructField("locked_balance",StringType(),True)])

schema_for_forex_returns = StructType([StructField("id", LongType(), True),
                                       StructField("account_id", LongType(), True),
                                       StructField("user_id", LongType(), True),
                                       StructField("forex_id", LongType(), True),
                                       StructField("total_quantity", StringType(), True),
                                       StructField("weighted_cost", LongType(), True),
                                       StructField("realised_gain", LongType(), True),
                                       StructField("created", TimestampType(), True),
                                       StructField("updated", TimestampType(), True)])

schema_for_forex_accounts = StructType([StructField("id", LongType(), True),
                                        StructField("forex_id", LongType(), True),
                                        StructField("user_id", LongType(), True),
                                        StructField("account_id", LongType(), True),
                                        StructField("client_id", LongType(), True),
                                        StructField("partner_id", LongType(), True),
                                        StructField("balance", StringType(), True),
                                        StructField("created", TimestampType(), True),
                                        StructField("updated", TimestampType(), True),
                                        StructField("blocked_balance", StringType(), True)])

schema_for_fund_returns = StructType([StructField("id", LongType(), True),
                                      StructField("account_id", LongType(), True),
                                      StructField("user_id", LongType(), True),
                                      StructField("fund_id", LongType(), True),
                                      StructField("total_quantity", StringType(), True),
                                      StructField("weighted_cost", StringType(), True),
                                      StructField("realised_gain", StringType(), True),
                                      StructField("created", TimestampType(), True),
                                      StructField("updated", TimestampType(), True),
                                      StructField("currency", StringType(), True),
                                      StructField("weighted_cost_idr", StringType(), True),
                                      StructField("realised_gain_idr", StringType(), True)])


schema_for_wallet = StructType([StructField("account_id", LongType(), True),
                                StructField("cash_balance", LongType(), True),
                                StructField("voucher_balance", LongType(), True),
                                StructField("created", TimestampType(), True),
                                StructField("updated", TimestampType(), True),
                                StructField("cashback", LongType(), True),
                                StructField("free_balance", LongType(), True),
                                StructField("client_id", LongType(), True),
                                StructField("user_id", LongType(), True),
                                StructField("cash_reward", LongType(), True),
                                StructField("first_order_date", TimestampType(), True),
                                StructField("blocked_cash_balance", LongType(), True),
                                StructField("blocked_voucher_balance", LongType(), True),
                                StructField("blocked_free_balance", LongType(), True)])

schema_for_gold_snap = StructType([StructField("account_id", LongType(), True),
                                   StructField("realised_gain", LongType(), True),
                                   StructField("total_quantity", DoubleType(), True),
                                   StructField("total_value", LongType(), True),
                                   StructField("unit_price", LongType(), True),
                                   StructField("unrealised_gain", LongType(), True),
                                   StructField("weighted_cost", LongType(), True),
                                   StructField("created", TimestampType(), True)])

schema_for_crypto_currency_snap = StructType([StructField("account_id", LongType(), True),
                                              StructField("crypto_currency_id", LongType(), True),
                                              StructField("realised_gain", LongType(), True),
                                              StructField("total_quantity", DoubleType(), True),
                                              StructField("total_value", LongType(), True),
                                              StructField("unit_price", LongType(), True),
                                              StructField("unrealised_gain", LongType(), True),
                                              StructField("weighted_cost", LongType(), True),
                                              StructField("created", TimestampType(), True)])

schema_for_forex_snap = StructType([StructField("account_id", LongType(), True),
                                    StructField("forex_id", LongType(), True),
                                    StructField("total_quantity", DoubleType(), True),
                                    StructField("weighted_cost", LongType(), True),
                                    StructField("realised_gain", LongType(), True),
                                    StructField("unit_price", LongType(), True),
                                    StructField("created", TimestampType(), True),
                                    StructField("total_value", LongType(), True),
                                    StructField("unrealised_gain", LongType(), True)])

schema_for_fund_snap = StructType([StructField("account_id", LongType(), True),
                                   StructField("fund_id", LongType(), True),
                                   StructField("realised_gain", LongType(), True),
                                   StructField("total_quantity", DoubleType(), True),
                                   StructField("total_value", DoubleType(), True),
                                   StructField("total_value_idr", LongType(), True),
                                   StructField("unit_price", DoubleType(), True),
                                   StructField("unrealised_gain", DoubleType(), True),
                                   StructField("unrealised_gain_idr", LongType(), True),
                                   StructField("weighted_cost", DoubleType(), True),
                                   StructField("weighted_cost_idr", LongType(), True),
                                   StructField("created", TimestampType(), True)])


schema_for_wallet_snap = StructType([StructField("account_id", IntegerType(), True),
                                     StructField("cash_balance", IntegerType(), True),
                                     StructField("voucher_balance", IntegerType(), True),
                                     StructField("created", TimestampType(), True),
                                     StructField("updated", TimestampType(), True),
                                     StructField("cashback", IntegerType(), True),
                                     StructField("free_balance", IntegerType(), True),
                                     StructField("client_id", IntegerType(), True),
                                     StructField("user_id", IntegerType(), True),
                                     StructField("cash_reward", IntegerType(), True)])

schema_for_global_stock_returns = StructType([StructField("id", LongType(), True),
                                              StructField("global_stock_id", LongType(), True),
                                              StructField("account_id", LongType(), True),
                                              StructField("user_id", LongType(), True),
                                              StructField("total_quantity", StringType(), True),
                                              StructField("weighted_cost", StringType(), True),
                                              StructField("realised_gain", StringType(), True),
                                              StructField("created", TimestampType(), True),
                                              StructField("updated", TimestampType(), True),
                                              StructField("batch_quantity", StringType(), True),
                                              StructField("weighted_cost_in_idr", LongType(), True),
                                              StructField("realised_gain_in_idr", LongType(), True),
                                              StructField("total_dividend", StringType(), True),
                                              StructField("total_dividend_in_idr", LongType(), True),
                                              StructField("total_trading_margin_used", StringType(), True),
                                              StructField("free_credit", StringType(), True),
                                              StructField("free_credit_in_idr", LongType(), True),
                                              StructField("usd_to_idr_quantity_average", LongType(), True)])

schema_for_global_stock_accounts = StructType([StructField("id", LongType(), True),
                                               StructField("global_stock_id", LongType(), True),
                                               StructField("user_id", LongType(), True),
                                               StructField("account_id", LongType(), True),
                                               StructField("partner_id", LongType(), True),
                                               StructField("client_id", LongType(), True),
                                               StructField("balance", StringType(), True),
                                               StructField("first_order_date", TimestampType(), True),
                                               StructField("created", TimestampType(), True),
                                               StructField("updated", TimestampType(), True),
                                               StructField("pending_transactions", LongType(), True),
                                               StructField("pending_reward_balance", StringType(), True),
                                               StructField("total_reward_balance", StringType(), True),
                                               StructField("reward_balance", StringType(), True)])

schema_for_global_stock_pocket_returns = StructType([StructField("id", LongType(), True),
                                                     StructField("global_stock_id", LongType(), True),
                                                     StructField("account_id", LongType(), True),
                                                     StructField("user_id", LongType(), True),
                                                     StructField("user_pocket_id", LongType(), True),
                                                     StructField("total_quantity", StringType(), True),
                                                     StructField("batch_quantity", StringType(), True),
                                                     StructField("weighted_cost", StringType(), True),
                                                     StructField("realised_gain", StringType(), True),
                                                     StructField("weighted_cost_in_idr", LongType(), True),
                                                     StructField("realised_gain_in_idr", LongType(), True),
                                                     StructField("total_dividend", StringType(), True),
                                                     StructField("total_dividend_in_idr", LongType(), True),
                                                     StructField("created", TimestampType(), True),
                                                     StructField("updated", TimestampType(), True),
                                                     StructField("usd_to_idr_quantity_average", LongType(), True)])


schema_for_global_stock_pocket_accounts = StructType([StructField("id", LongType(), True),
                                                      StructField("global_stock_id", LongType(), True),
                                                      StructField("user_id", LongType(), True),
                                                      StructField("account_id", LongType(), True),
                                                      StructField("partner_id", LongType(), True),
                                                      StructField("client_id", LongType(), True),
                                                      StructField("balance", StringType(), True),
                                                      StructField("first_order_date", TimestampType(), True),
                                                      StructField("user_pocket_id", LongType(), True),
                                                      StructField("created", TimestampType(), True),
                                                      StructField("updated", TimestampType(), True)])

schema_for_gold_withdrawals=StructType([StructField("id",LongType(),True),
                                        StructField("account_id",LongType(),True),
                                        StructField("user_id",LongType(),True),
                                        StructField("gold_amount",StringType(),True),
                                        StructField("order_ref",StringType(),True),
                                        StructField("invoice_link",StringType(),True),
                                        StructField("receipt_link",StringType(),True),
                                        StructField("status",StringType(),True),
                                        StructField("created",TimestampType(),True),
                                        StructField("updated",TimestampType(),True),
                                        StructField("fee",StringType(),True),
                                        StructField("net_amount",StringType(),True),
                                        StructField("old_invoice_number",StringType(),True),
                                        StructField("old_merchant_id",StringType(),True),
                                        StructField("shipping_method",StringType(),True),
                                        StructField("delivery_fee",LongType(),True),
                                        StructField("printing_fee",LongType(),True),
                                        StructField("printing_fee_type",StringType(),True),
                                        StructField("unit_price",LongType(),True),
                                        StructField("insurance_fee",LongType(),True),
                                        StructField("final_price",LongType(),True),
                                        StructField("client_id",LongType(),True),
                                        StructField("ref_id",StringType(),True),
                                        StructField("address_proof_link",StringType(),True),
                                        StructField("delivery_address_type",StringType(),True),
                                        StructField("buy_price",LongType(),True),
                                        StructField("sell_price",LongType(),True),
                                        StructField("jfx_synced",StringType(),True)])


schema_for_gold_gift_transactions=StructType([StructField("id",LongType(),True),
                                              StructField("account_id",LongType(),True),
                                              StructField("target_account_id",LongType(),True),
                                              StructField("user_id",LongType(),True),
                                              StructField("target_user_id",LongType(),True),
                                              StructField("transaction_type",StringType(),True),
                                              StructField("quantity",StringType(),True),
                                              StructField("unit_price",LongType(),True),
                                              StructField("fees",LongType(),True),
                                              StructField("final_amount",LongType(),True),
                                              StructField("gift_id",StringType(),True),
                                              StructField("status",StringType(),True),
                                              StructField("actor_role",StringType(),True),
                                              StructField("receiver_phone_no",StringType(),True),
                                              StructField("client_id",LongType(),True),
                                              StructField("created",TimestampType(),True),
                                              StructField("updated",TimestampType(),True),
                                              StructField("jfx_synced",StringType(),True)])

schema_for_gold_transactions=StructType([StructField("id",LongType(),True),
                                         StructField("partner_id",LongType(),True),
                                         StructField("account_id",LongType(),True),
                                         StructField("transaction_type",StringType(),True),
                                         StructField("quantity",StringType(),True),
                                         StructField("unit_price",LongType(),True),
                                         StructField("fees",LongType(),True),
                                         StructField("final_amount",LongType(),True),
                                         StructField("partner_reference",StringType(),True),
                                         StructField("commission_percent",StringType(),True),
                                         StructField("commission_amount",LongType(),True),
                                         StructField("status",StringType(),True),
                                         StructField("created",TimestampType(),True),
                                         StructField("updated",TimestampType(),True),
                                         StructField("meta",StringType(),True),
                                         StructField("client_id",LongType(),True),
                                         StructField("user_id",LongType(),True),
                                         StructField("auto_invest",StringType(),True),
                                         StructField("round_up",StringType(),True),
                                         StructField("jfx_synced",StringType(),True)])


schema_for_gold_loans=StructType([StructField("id",LongType(),True),
                                  StructField("account_id",LongType(),True),
                                  StructField("partner_reference",StringType(),True),
                                  StructField("partner_id",LongType(),True),
                                  StructField("gold_loan_amount",StringType(),True),
                                  StructField("gold_frozen",StringType(),True),
                                  StructField("tenure",LongType(),True),
                                  StructField("admin_fee",LongType(),True),
                                  StructField("down_payment",LongType(),True),
                                  StructField("interest_rate",StringType(),True),
                                  StructField("issued_price",LongType(),True),
                                  StructField("currency",StringType(),True),
                                  StructField("monthly_installment",LongType(),True),
                                  StructField("total_gold_price",LongType(),True),
                                  StructField("total_installment",LongType(),True),
                                  StructField("source",StringType(),True),
                                  StructField("status",StringType(),True),
                                  StructField("installment_index",LongType(),True),
                                  StructField("due_date",TimestampType(),True),
                                  StructField("cash_back",LongType(),True),
                                  StructField("fine",LongType(),True),
                                  StructField("fine_pay_date",TimestampType(),True),
                                  StructField("deleted",StringType(),True),
                                  StructField("created",TimestampType(),True),
                                  StructField("updated",TimestampType(),True),
                                  StructField("user_id",LongType(),True),
                                  StructField("client_id",LongType(),True),
                                  StructField("auto_debit_emi",StringType(),True),
                                  StructField("gold_brand",StringType(),True),
                                  StructField("missed_emis",LongType(),True),
                                  StructField("total_principal",LongType(),True)])


schema_for_indo_stock_returns=StructType([StructField("id",LongType(),True),
                                          StructField("created",TimestampType(),True),
                                          StructField("updated",TimestampType(),True),
                                          StructField("account_id",LongType(),True),
                                          StructField("realised_gain",LongType(),True),
                                          StructField("stock_id",LongType(),True),
                                          StructField("user_id",LongType(),True),
                                          StructField("total_quantity",StringType(),True),
                                          StructField("weighted_cost",StringType(),True),
                                          StructField("dividend",FloatType(),True)])

schema_for_indo_stock_accounts=StructType([StructField("id",LongType(),True),
                                           StructField("created",TimestampType(),True),
                                           StructField("updated",TimestampType(),True),
                                           StructField("account_id",LongType(),True),
                                           StructField("balance",StringType(),True),
                                           StructField("stock_id",LongType(),True),
                                           StructField("client_id",LongType(),True),
                                           StructField("user_id",LongType(),True),
                                           StructField("partner_id",LongType(),True)])

schema_for_cashin=StructType([StructField("id",LongType(),True),
                              StructField("account_id",LongType(),True),
                              StructField("user_id",LongType(),True),
                              StructField("nominal",LongType(),True),
                              StructField("fee",LongType(),True),
                              StructField("amount",LongType(),True),
                              StructField("vendor_channel",StringType(),True),
                              StructField("payment_channel",StringType(),True),
                              StructField("vendor_transaction_id",StringType(),True),
                              StructField("status",StringType(),True),
                              StructField("valid_payment_time",TimestampType(),True),
                              StructField("payment_time",TimestampType(),True),
                              StructField("created",TimestampType(),True),
                              StructField("updated",TimestampType(),True),
                              StructField("invoice_number",StringType(),True),
                              StructField("wallet_type",StringType(),True),
                              StructField("client_id",LongType(),True)])


schema_for_cashouts=StructType([StructField("id",LongType(),True),
                                StructField("account_id",LongType(),True),
                                StructField("user_id",LongType(),True),
                                StructField("user_bank_id",LongType(),True),
                                StructField("nominal",LongType(),True),
                                StructField("fee",LongType(),True),
                                StructField("amount",LongType(),True),
                                StructField("vendor_channel",StringType(),True),
                                StructField("payment_channel",StringType(),True),
                                StructField("vendor_transaction_id",StringType(),True),
                                StructField("status",StringType(),True),
                                StructField("invoice_number",StringType(),True),
                                StructField("created",TimestampType(),True),
                                StructField("updated",TimestampType(),True),
                                StructField("wallet_type",StringType(),True),
                                StructField("weekly_cashout",LongType(),True),
                                StructField("today_cashout",LongType(),True),
                                StructField("client_id",LongType(),True),
                                StructField("auto_approved",StringType(),True)])


schema_for_crypto_currency_transactions=StructType([StructField("id",LongType(),True),
                                                    StructField("crypto_currency_id",LongType(),True),
                                                    StructField("user_id",LongType(),True),
                                                    StructField("account_id",LongType(),True),
                                                    StructField("client_id",LongType(),True),
                                                    StructField("partner_id",LongType(),True),
                                                    StructField("quantity",StringType(),True),
                                                    StructField("unit_price",StringType(),True),
                                                    StructField("fee",StringType(),True),
                                                    StructField("total_price",StringType(),True),
                                                    StructField("ref_id",StringType(),True),
                                                    StructField("status",StringType(),True),
                                                    StructField("transaction_type",StringType(),True),
                                                    StructField("created",TimestampType(),True),
                                                    StructField("updated",TimestampType(),True),
                                                    StructField("exchange_total_price",StringType(),True),
                                                    StructField("source",StringType(),True),
                                                    StructField("hedging_status",StringType(),True),
                                                    StructField("unhedged_quantity",StringType(),True),
                                                    StructField("yield_quantity_used",StringType(),True),
                                                    StructField("premium_fee",StringType(),True),
                                                    StructField("transaction_fee",StringType(),True),
                                                    StructField("estimated_quantity",StringType(),True),
                                                    StructField("estimated_unit_price",StringType(),True),
                                                    StructField("executed_quantity",StringType(),True),
                                                    StructField("executed_unit_price",StringType(),True),
                                                    StructField("executed_total_price",StringType(),True),
                                                    StructField("vendor_transaction_id",StringType(),True),
                                                    StructField("order_type",StringType(),True),
                                                    StructField("reward_quantity_used",StringType(),True),
                                                    StructField("taxation_fee",StringType(),True)])


schema_for_global_stock_transactions = StructType([StructField("id",LongType(),True),
                                          StructField("global_stock_id",LongType(),True),
                                          StructField("user_id",LongType(),True),
                                          StructField("account_id",LongType(),True),
                                          StructField("client_id",LongType(),True),
                                          StructField("partner_id",LongType(),True),
                                          StructField("quantity",StringType(),True),
                                          StructField("unit_price",LongType(),True),
                                          StructField("transaction_fee",LongType(),True),
                                          StructField("premium_fee",LongType(),True),
                                          StructField("total_price",LongType(),True),
                                          StructField("executed_total_price",LongType(),True),
                                          StructField("estimated_quantity",LongType(),True),
                                          StructField("executed_quantity",LongType(),True),
                                          StructField("estimated_unit_price",LongType(),True),
                                          StructField("executed_unit_price",LongType(),True),
                                          StructField("ref_id",StringType(),True),
                                          StructField("order_type",StringType(),True),
                                          StructField("status",StringType(),True),
                                          StructField("transaction_type",StringType(),True),
                                          StructField("source",StringType(),True),
                                          StructField("created",TimestampType(),True),
                                          StructField("updated",TimestampType(),True),
                                          StructField("estimated_total_price",LongType(),True),
                                          StructField("hedging_type",StringType(),True),
                                          StructField("expiry_date_time",StringType(),True),
                                          StructField("info",StringType(),True),
                                          StructField("usd_to_idr",LongType(),True),
                                          StructField("forex_price_id",LongType(),True),
                                          StructField("cancelled_type",StringType(),True),
                                          StructField("effective_spread",StringType(),True),
                                          StructField("raw_spread",StringType(),True),
                                          StructField("exchange_mid_price",StringType(),True),
                                          StructField("lock_type",StringType(),True),
                                          StructField("amend_order_info",StringType(),True),
                                          StructField("order_sub_type",StringType(),True),
                                          StructField("vendor_transaction_id",StringType(),True),
                                          StructField("advanced_order_price_info",StringType(),True),
                                          StructField("linked_transaction_info",StringType(),True),
                                          StructField("wallet_type",StringType(),True),
                                          StructField("realised_gain",StringType(),True),
                                          StructField("trading_margin_used",StringType(),True)
                                          ])



schema_for_crypto_currency_wallet_transfers = StructType([StructField("id",LongType(),True),
                                                          StructField("user_id",LongType(),True),
                                                          StructField("account_id",LongType(),True),
                                                          StructField("client_id",LongType(),True),
                                                          StructField("partner_id",LongType(),True),
                                                          StructField("crypto_currency_id",LongType(),True),
                                                          StructField("source_address",StringType(),True),
                                                          StructField("destination_address",StringType(),True),
                                                          StructField("quantity",StringType(),True),
                                                          StructField("total_quantity",StringType(),True),
                                                          StructField("unit_price",StringType(),True),
                                                          StructField("transfer_fee",StringType(),True),
                                                          StructField("status",StringType(),True),
                                                          StructField("transaction_type",StringType(),True),
                                                          StructField("info",StringType(),True),
                                                          StructField("external_response",StringType(),True),
                                                          StructField("source",StringType(),True),
                                                          StructField("vendor_txn_id",StringType(),True),
                                                          StructField("external_status",StringType(),True),
                                                          StructField("created_at",TimestampType(),True),
                                                          StructField("updated_at",TimestampType(),True),
                                                          StructField("custodian_asset_id",StringType(),True),
                                                          StructField("network_fee",StringType(),True),
                                                          StructField("block_explorer_url",StringType(),True),
                                                          StructField("network",StringType(),True),
                                                          StructField("risk_score",StringType(),True),
                                                          StructField("transaction_sub_status",StringType(),True),
                                                          StructField("tag",StringType(),True),
                                                          StructField("taxation_fee",StringType(),True),
                                                          StructField("taxation_fee_percentage",StringType(),True)
                                                          ])

schema_for_crypto_currency_account_settings = StructType([ StructField("id", LongType(),True),
                                                           StructField("user_id", LongType(),True),
                                                           StructField("account_id", LongType(),True),
                                                           StructField("client_id", LongType(),True),
                                                           StructField("partner_id", LongType(),True),
                                                           StructField("pluang_cuan_waitlisted", BooleanType(),True),
                                                           StructField("is_pluang_cuan_active", BooleanType(),True),
                                                           StructField("created", TimestampType(),True),
                                                           StructField("updated", TimestampType(),True),
                                                           StructField("pluang_cuan_joining_date", TimestampType(),True)
                                                           ])

schema_for_forex_top_ups=StructType([StructField("id", LongType(), True),
                                     StructField("forex_id", LongType(), True),
                                     StructField("user_id", LongType(), True),
                                     StructField("account_id", LongType(), True),
                                     StructField("client_id", LongType(), True),
                                     StructField("partner_id", LongType(), True),
                                     StructField("status", StringType(), True),
                                     StructField("top_up_amount", StringType(), True),
                                     StructField("source_bank", StringType(), True),
                                     StructField("source_bank_account_number", StringType(), True),
                                     StructField("proof_of_transfer", StringType(), True),
                                     StructField("top_up_form", StringType(), True),
                                     StructField("bank_statement_transaction_id", StringType(), True),
                                     StructField("bank_statement_transaction_proof", StringType(), True),
                                     StructField("final_amount", StringType(), True),
                                     StructField("unit_price", LongType(), True),
                                     StructField("created", TimestampType(), True),
                                     StructField("updated", TimestampType(), True)])


schema_for_forex_cash_outs=StructType([StructField("id", LongType(), True),
                                       StructField("forex_id", LongType(), True),
                                       StructField("user_id", LongType(), True),
                                       StructField("account_id", LongType(), True),
                                       StructField("client_id", LongType(), True),
                                       StructField("partner_id", LongType(), True),
                                       StructField("status", StringType(), True),
                                       StructField("withdrawal_amount", StringType(), True),
                                       StructField("destination_bank_account", StringType(), True),
                                       StructField("destination_bank_account_owner", StringType(), True),
                                       StructField("destination_bank", StringType(), True),
                                       StructField("destination_bank_swift", StringType(), True),
                                       StructField("destination_bank_city", StringType(), True),
                                       StructField("destination_bank_country", StringType(), True),
                                       StructField("proof_of_request", StringType(), True),
                                       StructField("proof_of_bank_account_ownership", StringType(), True),
                                       StructField("unit_price", LongType(), True),
                                       StructField("cs_call_confirmation_link", StringType(), True),
                                       StructField("bank_transfer_slip", StringType(), True),
                                       StructField("finance_jira_link", StringType(), True),
                                       StructField("created", TimestampType(), True),
                                       StructField("updated", TimestampType(), True)])

schema_for_crypto_currency_pocket_returns = StructType([StructField("id", LongType(), True),
                                                 StructField("account_id", LongType(), True),
                                                 StructField("user_id", LongType(), True),
                                                 StructField("crypto_currency_id", LongType(), True),
                                                 StructField("user_pocket_id", LongType(), True),
                                                 StructField("total_quantity", StringType(), True),
                                                 StructField("weighted_cost", StringType(), True),
                                                 StructField("realised_gain", StringType(), True),
                                                 StructField("free_credit", StringType(), True),
                                                 StructField("created", TimestampType(), True),
                                                 StructField("updated", TimestampType(), True)])

schema_for_crypto_currency_pocket_accounts = StructType([StructField("id", LongType(), True),
                                                  StructField("crypto_currency_id", LongType(), True),
                                                  StructField("user_pocket_id", LongType(), True),
                                                  StructField("account_id", LongType(), True),
                                                  StructField("user_id", LongType(), True),
                                                  StructField("client_id", LongType(), True),
                                                  StructField("partner_id", LongType(), True),
                                                  StructField("balance", StringType(), True),
                                                  StructField("first_order_date", TimestampType(), True),
                                                  StructField("created", TimestampType(), True),
                                                  StructField("updated", TimestampType(), True)])

schema_for_gss_kyc_information =  StructType([StructField("id", LongType(), True),
                                              StructField("user_id", LongType(), True),
                                              StructField("gss_kyc_information_id", LongType(), True),
                                              StructField("to_state", StringType(), True),
                                              StructField("created", TimestampType(), True)])

schema_for_statement_transactions = StructType([StructField("account_id", LongType(), True),
                                                StructField("order_number", LongType(), True),
                                                StructField("order_type", StringType(), True),
                                                StructField("code", StringType(), True),
                                                StructField("quantity", DoubleType(), True),
                                                StructField("price", DoubleType(), True),
                                                StructField("fee", DoubleType(), True),
                                                StructField("total", DoubleType(), True),
                                                StructField("status", StringType(), True),
                                                StructField("asset_type", StringType(), True),
                                                StructField("asset_sub_type", StringType(), True),
                                                StructField("transaction_type", StringType(), True)])
