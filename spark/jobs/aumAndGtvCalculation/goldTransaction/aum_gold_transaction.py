'''gold_gift_transactions
gold_loans
gold_transactions- done
gold_withdrawals
'''

from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
raw_bucket_name = config_data["read_raw_bucket"] + config_data["raw_data_folder"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["gold"]["asset_folder"]
delete_record_date = "1970-01-01"

lowerbound = get_date_for_query(config_data["offset"]+1)
upperbound = datetime.now()
spark = spark_session_create("aum_gold_transaction")
win= Window.partitionBy("account_id","user_id","partner_id","client_id")

def gold_transaction(offset):
    dt = get_date(offset)
    gold_transaction_path  = config_data["gold"]["gold_transaction"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_transaction_path["read_gold_transaction_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_transaction_path["read_gold_transaction_path"],str(delete_record_date))
    try:
        logging.info("read gold transaction data from " + s3_path)
        gold_transaction = spark.read.json(s3_path)
        order_column = gold_transaction_path["order_column"]
        columns  = gold_transaction_path["columns"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        gold_trans,df_deleted_list = read_raw_data_saved_dedup(gold_transaction,order_column,columns,primary_keys,delete_record)
        gold_trans = gold_trans.withColumn("auto_invest",col("auto_invest").cast('boolean'))
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        gold_trans = spark.createDataFrame(emptyRDD,schema_for_gold_transaction)
        df_deleted_list =[]
    gold_trans = gold_trans.withColumn("excute_quantity",when((col("status")=="SUCCESS") & (col("transaction_type")=="BUY"), col("quantity")).when((col("status")=="SUCCESS") & (col("transaction_type")=="SELL"), -col("quantity")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,gold_transaction_path["write_gold_transaction_path"],str(dt))
    gold_trans.write.mode("overwrite").csv(write_path,header=True)
    return gold_trans,df_deleted_list


def gold_gift(offset):
    dt = get_date(offset)
    gold_gift_path = config_data["gold"]["gold_gift"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_gift_path["read_gold_gift_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_gift_path["read_gold_gift_path"],str(delete_record_date))
    try:
        logging.info("read gold gift transaction data from " + s3_path)
        gold_gift_transaction = spark.read.json(s3_path)
        order_column = gold_gift_path["order_column"]
        columns  = gold_gift_path["columns"] + ["unit_price"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        gold_gift_trans,df_deleted_list = read_raw_data_saved_dedup(gold_gift_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        gold_gift_trans = spark.createDataFrame(emptyRDD,schema_for_gold_gift)
        df_deleted_list =[]
    gold_gift_trans = gold_gift_trans.withColumn("excute_quantity",when((col("status")=="ACCEPTED") & (col("transaction_type")=="RECEIVE"), col("quantity")).when((col("status")=="ACCEPTED") & (col("transaction_type")=="SEND"), -col("quantity")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,gold_gift_path["write_gold_gift_path"],str(dt))
    gold_gift_trans.write.mode("overwrite").csv(write_path,header=True)
    gold_gift_trans = gold_gift_trans.drop("unit_price")
    return gold_gift_trans,df_deleted_list


def gold_loans(offset):
    dt = get_date(offset)
    gold_loans_path = config_data["gold"]["gold_loans"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_loans_path["read_gold_loans_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_loans_path["read_gold_loans_path"],str(delete_record_date))
    try:
        logging.info("read gold loan transaction data from " + s3_path)
        gold_loan_transaction = spark.read.json(s3_path)
        order_column = gold_loans_path["order_column"]
        columns  = gold_loans_path["columns"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        gold_loan_trans,df_deleted_list = read_raw_data_saved_dedup(gold_loan_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        gold_loan_trans = spark.createDataFrame(emptyRDD,schema_for_gold_loans)
        df_deleted_list =[]
    gold_loan_trans = gold_loan_trans.withColumn("excute_quantity",when((col("status")=="PAID_OFF"), col("gold_loan_amount")).otherwise(0.0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,gold_loans_path["write_gold_loans_path"],str(dt))
    gold_loan_trans.write.mode("overwrite").csv(write_path,header=True)
    return gold_loan_trans,df_deleted_list

def gold_frozen(offset):
    dt = get_date(offset)
    gold_frozen_path = config_data["gold"]["gold_frozen"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_frozen_path["read_gold_frozen_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_frozen_path["read_gold_frozen_path"],str(delete_record_date))
    try:
        logging.info("read gold frozen transaction data from " + s3_path)
        gold_frozen_transaction = spark.read.json(s3_path)
        order_column = gold_frozen_path["order_column"]
        columns  = gold_frozen_path["columns"] + ["issued_price"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        gold_frozen_trans,df_deleted_list = read_raw_data_saved_dedup(gold_frozen_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        gold_frozen_trans = spark.createDataFrame(emptyRDD,schema_for_gold_loans)
        df_deleted_list =[]
    gold_frozen_trans = gold_frozen_trans.withColumn("excute_quantity",when(col("status").isin("ACCEPTED","WAITING_FOR_CANCEL") & (col("partner_id")==1000002), col("gold_loan_amount")).when(col("status").isin("ACCEPTED","WAITING_FOR_CANCEL") & (col("partner_id")!=1000002), col("gold_loan_amount")).otherwise(0.0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,gold_frozen_path["write_gold_frozen_path"],str(dt))
    gold_frozen_trans.write.mode("overwrite").csv(write_path,header=True)
    gold_frozen_trans = gold_frozen_trans.drop("issued_price")
    return gold_frozen_trans,df_deleted_list


def gold_withdrawals(offset):
    dt = get_date(offset)
    gold_withdrawals_path = config_data["gold"]["gold_withdrawals"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_withdrawals_path["read_gold_withdrawals_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,gold_withdrawals_path["read_gold_withdrawals_path"],str(delete_record_date))
    try:
        logging.info("read gold withdrawals transaction data from " + s3_path)
        gold_withdrawals_transaction = spark.read.json(s3_path)
        order_column = gold_withdrawals_path["order_column"]
        columns  = gold_withdrawals_path["columns"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        gold_withdrawals_trans,df_deleted_list = read_raw_data_saved_dedup(gold_withdrawals_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        gold_withdrawals_trans = spark.createDataFrame(emptyRDD,schema_for_gold_withdrawals)
        df_deleted_list =[]
    gold_withdrawals_trans = gold_withdrawals_trans.withColumn("excute_quantity",when((col("status")=="DELIVERED") , -col("net_amount")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,gold_withdrawals_path["write_gold_withdrawals_path"],str(dt))
    gold_withdrawals_trans.write.mode("overwrite").csv(write_path,header=True)
    return gold_withdrawals_trans,df_deleted_list


def calculation_gold_transaction(offset,dt_1,dt_2):
    gold_transaction_path = config_data["gold"]["gold_transaction"]
    order_column = gold_transaction_path["order_column"]
    columns  = gold_transaction_path["columns"]
    primary_keys = config_data["primary_keys"]
    gold_transaction_t1,df_deleted_list = gold_transaction(offset)
    gold_transaction_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,gold_transaction_path["gold_transaction_t2_path"])
    gold_transaction_t2 = spark.read.csv( gold_transaction_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    gold_transaction_all = dedup_t1_and_t2(gold_transaction_t1,gold_transaction_t2,order_column,columns,primary_keys)

    gold_transaction_all = gold_transaction_all.filter(~col("id").isin(df_deleted_list))
    gold_transaction_all.coalesce(10).write.mode("overwrite").csv(gold_transaction_t2_path + str(dt_1)+"/",header=True)
    gold_transaction_all = gold_transaction_all.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    gold_transaction_all = gold_transaction_all.withColumn("is_recurring",when((col("auto_invest")==True) ,lit(True)).otherwise(False))
    gold_transaction_all = gold_transaction_all.withColumn("recurring_transaction_id",lit(None).cast("string"))
    gold_trans = gold_transaction_all.groupBy("account_id","client_id","user_id","partner_id","is_recurring","recurring_transaction_id","first_created","last_updated").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    gold_trans = gold_trans.withColumn("asset_subtype",lit("gold_transaction"))
    return gold_trans



def calculation_gold_gift(offset,dt_1,dt_2):
    gold_gift_path = config_data["gold"]["gold_gift"]
    order_column = gold_gift_path["order_column"]
    columns  = gold_gift_path["columns"]
    primary_keys = config_data["primary_keys"]
    gold_gift_t1,df_deleted_list = gold_gift(offset)
    gold_gift_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,gold_gift_path["gold_gift_t2_path"])
    gold_gift_t2 = spark.read.csv(gold_gift_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    gold_gift_all = dedup_t1_and_t2(gold_gift_t1,gold_gift_t2,order_column,columns,primary_keys)
    gold_gift_all = gold_gift_all.filter(~col("id").isin(df_deleted_list))
    gold_gift_all.coalesce(1).write.mode("overwrite").csv(gold_gift_t2_path + str(dt_1)+"/",header=True)
    gold_gift_trans = gold_gift_all.withColumn("partner_id",lit(1000002))
    gold_gift_trans = gold_gift_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    gold_gift_trans = gold_gift_trans.withColumn("is_recurring",lit(False))
    gold_gift_trans = gold_gift_trans.withColumn("recurring_transaction_id",lit(None).cast("string"))
    gold_gift_trans = gold_gift_trans.groupBy("account_id","client_id","user_id","partner_id","is_recurring","recurring_transaction_id","first_created","last_updated").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    gold_gift_trans = gold_gift_trans.withColumn("asset_subtype",lit("gold_gift"))
    return gold_gift_trans


def calculation_gold_loans(offset,dt_1,dt_2):
    gold_loans_path = config_data["gold"]["gold_loans"]
    order_column = gold_loans_path["order_column"]
    columns  = gold_loans_path["columns"]
    primary_keys = config_data["primary_keys"]
    gold_loans_t1,df_deleted_list = gold_loans(offset)
    gold_loans_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,gold_loans_path["gold_loans_t2_path"])
    gold_loans_t2 = spark.read.csv(gold_loans_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    gold_loans_all = dedup_t1_and_t2(gold_loans_t1,gold_loans_t2,order_column,columns,primary_keys)
    gold_loans_all = gold_loans_all.filter(~col("id").isin(df_deleted_list))
    gold_loans_all.coalesce(1).write.mode("overwrite").csv(gold_loans_t2_path + str(dt_1)+"/",header=True)
    gold_loans_all = gold_loans_all.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    gold_loans_all = gold_loans_all.withColumn("is_recurring",lit(False))
    gold_loans_all = gold_loans_all.withColumn("recurring_transaction_id",lit(None).cast("string"))
    gold_loans_all = gold_loans_all.groupBy("account_id","client_id","user_id","partner_id","is_recurring","recurring_transaction_id","first_created","last_updated").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    gold_loans_trans = gold_loans_all.withColumn("asset_subtype",lit("gold_loans"))
    return gold_loans_trans


def calculation_gold_frozen(offset,dt_1,dt_2):
    gold_frozen_path = config_data["gold"]["gold_frozen"]
    order_column = gold_frozen_path["order_column"]
    columns  = gold_frozen_path["columns"]
    primary_keys = config_data["primary_keys"]
    gold_frozen_t1,df_deleted_list = gold_frozen(offset)
    gold_frozen_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,gold_frozen_path["gold_frozen_t2_path"])
    gold_frozen_t2 = spark.read.csv(gold_frozen_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    gold_frozen_all = dedup_t1_and_t2(gold_frozen_t1,gold_frozen_t2,order_column,columns,primary_keys)
    gold_frozen_all = gold_frozen_all.filter(~col("id").isin(df_deleted_list))
    gold_frozen_all.coalesce(1).write.mode("overwrite").csv(gold_frozen_t2_path + str(dt_1)+"/",header=True)
    gold_frozen_all = gold_frozen_all.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    gold_frozen_all = gold_frozen_all.withColumn("is_recurring",lit(False))
    gold_frozen_all = gold_frozen_all.withColumn("recurring_transaction_id",lit(None).cast("string"))
    gold_frozen_all = gold_frozen_all.groupBy("account_id","client_id","user_id","partner_id","is_recurring","recurring_transaction_id","first_created","last_updated").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    gold_frozen_trans =gold_frozen_all.withColumn("asset_subtype",lit("gold_frozen"))
    return gold_frozen_trans

def calculation_gold_withdrawals(offset,dt_1,dt_2):
    gold_withdrawals_path = config_data["gold"]["gold_withdrawals"]
    order_column = gold_withdrawals_path["order_column"]
    columns  = gold_withdrawals_path["columns"]
    primary_keys = config_data["primary_keys"]
    gold_withdrawals_t1,df_deleted_list = gold_withdrawals(offset)
    gold_withdrawals_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,gold_withdrawals_path["gold_withdrawals_t2_path"])
    gold_withdrawals_t2 = spark.read.csv(gold_withdrawals_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    gold_withdrawals_all = dedup_t1_and_t2(gold_withdrawals_t1,gold_withdrawals_t2,order_column,columns,primary_keys)
    gold_withdrawals_all = gold_withdrawals_all.filter(~col("id").isin(df_deleted_list))
    gold_withdrawals_all.coalesce(1).write.mode("overwrite").csv(gold_withdrawals_t2_path + str(dt_1)+"/",header=True)
    gold_withdrawals_trans = gold_withdrawals_all.withColumn("partner_id",lit(1000002))
    gold_withdrawals_trans = gold_withdrawals_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    gold_withdrawals_trans = gold_withdrawals_trans.withColumn("is_recurring",lit(False))
    gold_withdrawals_trans = gold_withdrawals_trans.withColumn("recurring_transaction_id",lit(None).cast("string"))
    gold_withdrawals_trans = gold_withdrawals_trans.groupBy("account_id","client_id","user_id","partner_id","is_recurring","recurring_transaction_id","first_created","last_updated").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    gold_withdrawals_trans = gold_withdrawals_trans.withColumn("asset_subtype",lit("gold_withdrawals"))
    return gold_withdrawals_trans


def get_current_gold_price(dt_1):
    try:
        s3_path  = "s3a://{}/{}/dt=".format(bucket_name,config_data["gold"]["price_path"])
        data = spark.read.json( s3_path + str(dt_1) +"/")
        w2 = Window.partitionBy("partnerId").orderBy(col("createdDate").desc())
        gold_partner_prices = data.withColumn("row", row_number().over(w2)).filter(col("row") == 1).drop(
            "row")
        gold_partner_prices = gold_partner_prices.withColumnRenamed("partnerId","partner_id_dup").withColumn("mid_price", floor(
            ((col("closeBuyBack").cast(DoubleType()) + col("closeSell").cast(DoubleType())) / 2)))
        gold_partner_prices = gold_partner_prices.select("partner_id_dup","mid_price","closeBuyBack","closeSell")
        return gold_partner_prices
    except Exception as e:
        logging.error("An error has occurred while loading gold partner_prices: {}".format(repr(e)))
        logging.exception(e)
        raise e


def start_processing():
    logging.info("Starting execution for gold aum")
    offset = config_data["offset"]
    dt_1 = get_date(offset)
    dt_2 = get_date(offset+1)
    snap_gold_transaction  = calculation_gold_transaction(offset,dt_1,dt_2)
    snap_gold_gift = calculation_gold_gift(offset,dt_1,dt_2)
    snap_gold_loans = calculation_gold_loans(offset,dt_1,dt_2)
    snap_gold_withdrawals = calculation_gold_withdrawals(offset,dt_1,dt_2)
    snap_gold_frozen = calculation_gold_frozen(offset,dt_1,dt_2)
    gold_price = get_current_gold_price(dt_1)
    snap_gold = snap_gold_transaction.union(snap_gold_gift).union(snap_gold_loans).union(snap_gold_withdrawals).union(snap_gold_frozen)
    snap_gold = snap_gold.groupBy("account_id","client_id","user_id","partner_id","asset_subtype","is_recurring","recurring_transaction_id","first_created","last_updated").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    snap_gold_with_price = snap_gold.join(gold_price,snap_gold["partner_id"]==gold_price["partner_id_dup"]).drop("partner_id_dup").withColumn("currency",lit("IDR"))
    snap_gold_with_price = snap_gold_with_price.withColumn("asset_type",lit("gold")).withColumn("product_id",lit(0))
    snap_gold_with_price = snap_gold_with_price.withColumnRenamed("mid_price","product_mid_price").withColumnRenamed("closeBuyBack","product_buy_back_price").withColumnRenamed("closeSell","product_sell_price")
    snap_gold_with_price = snap_gold_with_price.withColumnRenamed("excute_quantity","quantity").withColumn("is_pocket",lit(False)).withColumn("user_pocket_id",lit(None).cast('string'))
    write_snap_gold_path =   "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,config_data["gold"]["gold_aum_path"],str(dt_1))
    snap_gold_with_price.coalesce(1).write.mode("overwrite").csv(write_snap_gold_path,header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"aum_gold_transaction")
