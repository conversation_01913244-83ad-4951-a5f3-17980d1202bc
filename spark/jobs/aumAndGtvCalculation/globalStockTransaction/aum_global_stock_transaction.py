'''
global_stock_transactions
global_stcok_reverse_transaction
global_stock_split_transactions
global_stcok_reverse_split_transaction
'''

import argparse
from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
raw_bucket_name = config_data["read_raw_bucket"] + config_data["raw_data_folder"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["global_stock"]["asset_folder"]
delete_record_date = "1970-01-01"

lowerbound = get_date_for_query(config_data["offset"]+1)
upperbound = datetime.now()
spark = spark_session_create("aum_global_stock_transaction")
win= Window.partitionBy("account_id","user_id","global_stock_id","client_id")

def user_pocket_transactions(offset,dt_2):
    dt = get_date(offset)
    user_pocket_transactions_path = config_data["user_pocket"]["user_pocket_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,user_pocket_transactions_path["read_user_pocket_raw_data"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,user_pocket_transactions_path["read_user_pocket_raw_data"],str(delete_record_date))
    order_column = user_pocket_transactions_path["order_column"]
    columns  = user_pocket_transactions_path["columns"]
    primary_keys = config_data["primary_keys"]
    try:
        logging.info("read user pocket transaction data from " + s3_path)
        user_pocket_transaction = spark.read.json(s3_path)
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        user_pocket_transaction,df_deleted_list = read_raw_data_saved_dedup(user_pocket_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        user_pocket_transaction = spark.createDataFrame(emptyRDD,schema_for_user_pocket_transactions)
    s3_path = "s3a://{}{}/{}".format(bucket_name,aum_folder,user_pocket_transactions_path["user_pocket_t_2_path"])
    user_pocket_t2 = spark.read.csv("{}{}/*".format(s3_path,str(dt_2)),header=True,inferSchema=True)
    user_pocket_tran = dedup_t1_and_t2(user_pocket_transaction,user_pocket_t2,order_column,columns,primary_keys)
    user_pocket_tran.write.mode("overwrite").csv("{}{}".format(s3_path,dt),header=True)
    return user_pocket_tran

def user_pocket_asset_transaction(offset,dt_2):
    dt = get_date(offset)
    user_pocket_asset_transactions_path = config_data["user_pocket"]["user_pocket_asset_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,user_pocket_asset_transactions_path["read_user_pocket_asset_raw_data"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,user_pocket_asset_transactions_path["read_user_pocket_asset_raw_data"],str(delete_record_date))
    order_column = user_pocket_asset_transactions_path["order_column"]
    columns  = user_pocket_asset_transactions_path["columns"]
    primary_keys = config_data["primary_keys"]
    try:
        logging.info("read user pocket transaction data from " + s3_path)
        user_pocket_asset_transaction = spark.read.json(s3_path)
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        user_pocket_asset_transaction,df_deleted_list = read_raw_data_saved_dedup(user_pocket_asset_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        user_pocket_asset_transaction = spark.createDataFrame(emptyRDD,schema_for_user_pocket_asset_transaction)
    s3_path = "s3a://{}{}/{}".format(bucket_name,aum_folder,user_pocket_asset_transactions_path["user_pocket_asset_t_2_path"])
    user_pocket_t2 = spark.read.csv("{}{}/*".format(s3_path,str(dt_2)),header=True,inferSchema=True)
    user_pocket_asset_tran = dedup_t1_and_t2(user_pocket_asset_transaction,user_pocket_t2,order_column,columns,primary_keys)
    user_pocket_asset_tran.write.mode("overwrite").csv("{}{}".format(s3_path,dt),header=True)
    return user_pocket_asset_tran

def get_user_pocket_transaction_asset_transaction_mapping(user_pocket_transaction,user_pocket_asset_transactions,dt):
    user_pocket_transaction = user_pocket_transaction.select("id","user_id","account_id","user_pocket_id","currency","recurring_transaction_id").filter(col("currency")=="USD")
    user_pocket_asset_transactions = user_pocket_asset_transactions.select("pocket_transaction_id","asset_transaction_id","asset_id","asset_category")
    user_transaction_mapping = user_pocket_transaction.join(user_pocket_asset_transactions,user_pocket_transaction["id"]==user_pocket_asset_transactions["pocket_transaction_id"],"inner")
    user_transaction_mapping = user_transaction_mapping.select("user_id","account_id","user_pocket_id","recurring_transaction_id","pocket_transaction_id","asset_transaction_id","asset_id")
    s3_path = "s3a://{}{}/{}".format(bucket_name,aum_folder,config_data["user_pocket"]["user_pocket_transaction_mapping"])
    user_transaction_mapping.write.mode("overwrite").csv("{}{}".format(s3_path,dt),header=True)
    return user_transaction_mapping


def global_stock_transactions(offset):
    dt = get_date(offset)
    global_stock_transactions_path = config_data["global_stock"]["global_stock_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_transactions_path["read_global_stock_transactions_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_transactions_path["read_global_stock_transactions_path"],str(delete_record_date))
    try:
        logging.info("read global stock transaction data from " + s3_path)
        global_stock_transaction = spark.read.json(s3_path)
        order_column = global_stock_transactions_path["order_column"]
        columns  = global_stock_transactions_path["columns"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        global_stock_trans,df_deleted_list = read_raw_data_saved_dedup(global_stock_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        global_stock_trans = spark.createDataFrame(emptyRDD,schema_for_global_stock_transactions)
        df_deleted_list =  []
    global_stock_trans = global_stock_trans.withColumn("excute_quantity",when((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & (col("transaction_type")=="BUY"), col("quantity")).when((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & (col("transaction_type")=="SELL"), -col("quantity")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,global_stock_transactions_path["write_global_stock_transactions_path"],str(dt))
    global_stock_trans.write.mode("overwrite").csv(write_path,header=True)
    return global_stock_trans,df_deleted_list


def global_stock_split_transactions(offset):
    dt = get_date(offset)
    global_stock_split_transactions_path  = config_data["global_stock"]["global_stock_split_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_split_transactions_path["read_global_stock_split_transactions_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_split_transactions_path["read_global_stock_split_transactions_path"],str(delete_record_date))
    try:
        logging.info("read global stock split transaction data from " + s3_path)
        global_stock_split_transaction = spark.read.json(s3_path)
        order_column = global_stock_split_transactions_path["order_column"]
        columns  = global_stock_split_transactions_path["columns"] + ["new_rate"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        global_stock_split_trans,df_deleted_list = read_raw_data_saved_dedup(global_stock_split_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        logging.warn(e)
        emptyRDD = spark.sparkContext.emptyRDD()
        global_stock_split_trans = spark.createDataFrame(emptyRDD,schema_for_global_stock_split_transactions)
        df_deleted_list =[]
    global_stock_split_trans = global_stock_split_trans.filter(col("status").isin("SUCCESS")).withColumn("excute_quantity",(col("quantity_after_split")-col("quantity_before_split")))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,global_stock_split_transactions_path["write_global_stock_split_transactions_path"],str(dt))
    global_stock_split_trans.write.mode("overwrite").csv(write_path,header=True)
    global_stock_split_trans= global_stock_split_trans.drop("new_rate")
    return global_stock_split_trans,df_deleted_list


def global_stcok_reverse_split_transaction(offset):
    dt = get_date(offset)
    global_stock_reverse_split_transactions_path = config_data["global_stock"]["global_stock_reverse_split_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_reverse_split_transactions_path["read_global_stock_reverse_split_transactions_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_reverse_split_transactions_path["read_global_stock_reverse_split_transactions_path"],str(delete_record_date))
    try:
        logging.info("read global stock reverse split transaction data from " + s3_path)
        global_stock_reverse_split_transaction = spark.read.json(s3_path)
        order_column = global_stock_reverse_split_transactions_path["order_column"]
        columns  = global_stock_reverse_split_transactions_path["columns"] + ["new_rate"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        global_stock_reverse_split_trans,df_deleted_list = read_raw_data_saved_dedup(global_stock_reverse_split_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        logging.warn(e)
        emptyRDD = spark.sparkContext.emptyRDD()
        global_stock_reverse_split_trans = spark.createDataFrame(emptyRDD,schema_for_global_stcok_reverse_split_transaction)
        df_deleted_list =[]
    global_stock_reverse_split_trans = global_stock_reverse_split_trans.filter(col("status").isin("SUCCESS")).withColumn("excute_quantity",(col("quantity_after_split")-col("quantity_before_split")))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,global_stock_reverse_split_transactions_path["write_global_stock_reverse_split_transactions_path"],str(dt))
    global_stock_reverse_split_trans.write.mode("overwrite").csv(write_path,header=True)
    global_stock_reverse_split_trans = global_stock_reverse_split_trans.drop("new_rate")
    return global_stock_reverse_split_trans,df_deleted_list


def global_stock_mission_rewards(offset):
    dt = get_date(offset)
    global_stock_mission_rewards_path = config_data["global_stock"]["global_stock_mission_rewards"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_mission_rewards_path["read_global_stock_mission_rewards_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_mission_rewards_path["read_global_stock_mission_rewards_path"],str(delete_record_date))
    try:
        logging.info("read global stock mission rewards data from " + s3_path)
        global_stock_mission_rewards = spark.read.json(s3_path)
        order_column = global_stock_mission_rewards_path["order_column"]
        columns  = global_stock_mission_rewards_path["columns"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        global_stock_mission_rewards_trans,df_deleted_list = read_raw_data_saved_dedup(global_stock_mission_rewards,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        logging.warn(e)
        emptyRDD = spark.sparkContext.emptyRDD()
        global_stock_mission_rewards_trans = spark.createDataFrame(emptyRDD,schema_for_global_stock_mission_rewards)
        df_deleted_list =[]
    global_stock_mission_rewards_trans = global_stock_mission_rewards_trans.withColumn("excute_quantity",when((col("status").isin("CLAIMED","UNLOCKED")), col("executed_quantity")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,global_stock_mission_rewards_path["write_global_stock_mission_rewards_path"],str(dt))
    global_stock_mission_rewards_trans.write.mode("overwrite").csv(write_path,header=True)
    return global_stock_mission_rewards_trans,df_deleted_list

def global_stock_merger_transactions(offset):
    dt = get_date(offset)
    global_stock_merger_transactions_path = config_data["global_stock"]["global_stock_merger_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_merger_transactions_path["read_global_stock_merger_transactions_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_merger_transactions_path["read_global_stock_merger_transactions_path"],str(delete_record_date))
    try:
        logging.info("read global stock merger tansactions data from " + s3_path)
        global_stock_merger_transactions = spark.read.json(s3_path)
        order_column = global_stock_merger_transactions_path["order_column"]
        columns  = global_stock_merger_transactions_path["raw_data_column"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        global_stock_merger_trans,df_deleted_list = read_raw_data_saved_dedup(global_stock_merger_transactions,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        logging.warn(e)
        emptyRDD = spark.sparkContext.emptyRDD()
        global_stock_merger_trans = spark.createDataFrame(emptyRDD,schema_for_global_stock_merger_trans)
        df_deleted_list =[]
    global_stock_merger_trans_buy = global_stock_merger_trans.select("account_id","client_id","created","id","partner_id","reference_table_id","status","updated","user_id","destination_stock_id","destination_stock_quantity","destination_stock_merger_price").withColumn("transaction_type",lit("BUY"))
    global_stock_merger_trans_buy = global_stock_merger_trans_buy.withColumnRenamed("destination_stock_id","global_stock_id").withColumnRenamed("destination_stock_quantity","quantity").withColumnRenamed("destination_stock_merger_price","unit_price")
    global_stock_merger_trans_sell = global_stock_merger_trans.select("account_id","client_id","created","id","partner_id","reference_table_id","status","updated","user_id","source_stock_id","source_stock_quantity","source_stock_merger_price").withColumn("transaction_type",lit("SELL"))
    global_stock_merger_trans_sell = global_stock_merger_trans_sell.withColumnRenamed("source_stock_id","global_stock_id").withColumnRenamed("source_stock_quantity","quantity").withColumnRenamed("source_stock_merger_price","unit_price")
    global_stock_merger_trans_all = global_stock_merger_trans_buy.union(global_stock_merger_trans_sell)
    global_stock_merger_trans_all = global_stock_merger_trans_all.withColumn("excute_quantity",when((col("status").isin("SUCCESS")) & (col("transaction_type")=="BUY"), col("quantity")).when((col("status").isin("SUCCESS")) & (col("transaction_type")=="SELL"), -col("quantity")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,global_stock_merger_transactions_path["write_global_stock_merger_transactions_path"],str(dt))
    global_stock_merger_trans_all.write.mode("overwrite").csv(write_path,header=True)
    return global_stock_merger_trans_all,df_deleted_list


def global_stock_option_transactions(offset):
    dt = get_date(offset)
    global_stock_option_transactions_path = config_data["global_stock"]["global_stock_options_contract_transactions"]
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_option_transactions_path["read_global_stock_options_contract_transactions_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,global_stock_option_transactions_path["read_global_stock_options_contract_transactions_path"],str(delete_record_date))
    try:
        logging.info("read global stock options tansactions data from " + s3_path)
        global_stock_option_transactions = spark.read.json(s3_path)
        order_column = global_stock_option_transactions_path["order_column"]
        columns  = global_stock_option_transactions_path["columns"]
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        global_stock_option_trans,df_deleted_list = read_raw_data_saved_dedup(global_stock_option_transactions,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        logging.warning(e)
        emptyRDD = spark.sparkContext.emptyRDD()
        global_stock_option_trans = spark.createDataFrame(emptyRDD,schema_for_options_contract_transactions)
        df_deleted_list =[]
    try:
        global_stock_options_contract =spark.read.csv("s3a://{}/{}{}/*".format(bucket_name,global_stock_option_transactions_path["global_stock_options_contract"],str(dt)),header=True,inferSchema=True)
    except Exception as e:
        logging.warning("reading option contract error occur {}".format(str(e)))
        dt_2 = get_date(offset+1)
        global_stock_options_contract =spark.read.csv("s3a://{}/{}{}/*".format(bucket_name,global_stock_option_transactions_path["global_stock_options_contract"],str(dt_2)),header=True,inferSchema=True)
    contracts = global_stock_options_contract.select(col("id").alias("options_contract_id"), col("global_stock_id"), col("shares_per_contract"))
    global_stock_option_trans = global_stock_option_trans.join(contracts, on=["options_contract_id", "global_stock_id"], how="left").fillna(0)
    global_stock_trans = global_stock_option_trans.withColumn("excute_quantity",when((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & (col("transaction_type")=="LONG_OPEN"), col("quantity")* col("shares_per_contract")).when((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & (col("transaction_type")=="LONG_CLOSE"), -col("quantity")*col("shares_per_contract")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,global_stock_option_transactions_path["write_global_stock_options_contract_transactions_path"],str(dt))
    global_stock_trans.write.mode("overwrite").csv(write_path,header=True)
    return global_stock_trans,df_deleted_list


def calculation_global_stock_transactions(offset,dt_1,dt_2,user_transaction_mapping):
    global_stock_transactions_path = config_data["global_stock"]["global_stock_transactions"]
    order_column = global_stock_transactions_path["order_column"]
    columns  = global_stock_transactions_path["columns"]
    primary_keys = config_data["primary_keys"]
    global_stock_transactions_t1,df_deleted_list = global_stock_transactions(offset)
    global_stock_transactions_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,global_stock_transactions_path["global_stock_transactions_t2_path"])
    global_stock_transactions_t2 = spark.read.csv( global_stock_transactions_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    logging.info("Reading global stock transactions T2 data from " + global_stock_transactions_t2_path + str(dt_2) + "/*")
    columns = columns + ["excute_quantity"]
    global_stock_transactions_all = dedup_t1_and_t2(global_stock_transactions_t1,global_stock_transactions_t2,order_column,columns,primary_keys)
    global_stock_transactions_all = global_stock_transactions_all.filter(~col("id").isin(df_deleted_list))
    global_stock_transactions_all.coalesce(1).write.mode("overwrite").csv(global_stock_transactions_t2_path + str(dt_1)+"/",header=True)
    global_stock_trans = global_stock_transactions_all.withColumn("is_pocket",when((col("user_pocket_id").isNull()) ,lit(False)).otherwise(True))
    global_stock_trans = global_stock_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    user_pocket_transaction = user_transaction_mapping.select("user_id","account_id","user_pocket_id","recurring_transaction_id","pocket_transaction_id","asset_transaction_id","asset_id")
    user_pocket_transaction = user_pocket_transaction.withColumnRenamed("recurring_transaction_id","recurring_pocket_transaction_id").withColumnRenamed("asset_transaction_id","id").withColumnRenamed("asset_id","global_stock_id")
    global_stock_trans_with_recurring  = global_stock_trans.join(user_pocket_transaction,on=["id","user_id","account_id","user_pocket_id","global_stock_id"],how="left")
    global_stock_trans_with_recurring = global_stock_trans_with_recurring.withColumn("recurring_trans_id",when(col("user_pocket_id").isNotNull(),col("recurring_pocket_transaction_id")).otherwise(col("recurring_transaction_id"))).drop("recurring_transaction_id")
    global_stock_trans_with_recurring = global_stock_trans_with_recurring.withColumnRenamed("recurring_trans_id","recurring_transaction_id")
    global_stock_trans_with_recurring = global_stock_trans_with_recurring.withColumn("is_recurring",when((col("recurring_transaction_id").isNull()) ,lit(False)).otherwise(True))
    global_stock_trans_with_recurring = global_stock_trans_with_recurring.groupBy("account_id","user_id","client_id","global_stock_id","partner_id",'is_pocket',"user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    global_stock_trans_with_recurring = global_stock_trans_with_recurring.withColumn("asset_subtype",lit("global_stock_transactions"))
    return global_stock_trans_with_recurring

def calculation_global_stock_split_transactions(offset,dt_1,dt_2):
    global_stock_split_transactions_path = config_data["global_stock"]["global_stock_split_transactions"]
    order_column = global_stock_split_transactions_path["order_column"]
    columns  = global_stock_split_transactions_path["columns"]
    primary_keys = config_data["primary_keys"]
    global_stock_split_transactions_t1,df_deleted_list = global_stock_split_transactions(offset)
    global_stock_split_transactions_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,global_stock_split_transactions_path["global_stock_split_transactions_t2_path"])
    global_stock_split_transactions_t2 = spark.read.csv( global_stock_split_transactions_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    global_stock_split_transactions_all = dedup_t1_and_t2(global_stock_split_transactions_t1,global_stock_split_transactions_t2,order_column,columns,primary_keys)
    global_stock_split_transactions_all = global_stock_split_transactions_all.filter(~col("id").isin(df_deleted_list))
    global_stock_split_transactions_all.coalesce(1).write.mode("overwrite").csv(global_stock_split_transactions_t2_path + str(dt_1)+"/",header=True)
    global_stock_split_trans = global_stock_split_transactions_all.withColumn("is_pocket",lit(False)).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None)).withColumn("user_pocket_id",lit(None))
    global_stock_split_trans = global_stock_split_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    global_stock_split_trans = global_stock_split_trans.groupBy("account_id","user_id","client_id","global_stock_id","partner_id",'is_pocket',"user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    global_stock_split_trans = global_stock_split_trans.withColumn("asset_subtype",lit("global_stock_split"))
    return global_stock_split_trans


def calculation_global_stock_reverse_split_transaction(offset,dt_1,dt_2):
    global_stock_reverse_split_transactions_path = config_data["global_stock"]["global_stock_reverse_split_transactions"]
    order_column = global_stock_reverse_split_transactions_path["order_column"]
    columns  = global_stock_reverse_split_transactions_path["columns"]
    primary_keys = config_data["primary_keys"]
    global_stock_reverse_split_transactions_t1,df_deleted_list = global_stcok_reverse_split_transaction(offset)
    global_stock_reverse_split_transactions_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,global_stock_reverse_split_transactions_path["global_stock_reverse_split_transactions_t2_path"])
    global_stock_reverse_split_transactions_t2 = spark.read.csv( global_stock_reverse_split_transactions_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    global_stock_reverse_split_transactions_all = dedup_t1_and_t2(global_stock_reverse_split_transactions_t1,global_stock_reverse_split_transactions_t2,order_column,columns,primary_keys)
    global_stock_reverse_split_transactions_all = global_stock_reverse_split_transactions_all.filter(~col("id").isin(df_deleted_list))
    global_stock_reverse_split_transactions_all.coalesce(1).write.mode("overwrite").csv(global_stock_reverse_split_transactions_t2_path + str(dt_1)+"/",header=True)
    global_stock_reverse_split_trans = global_stock_reverse_split_transactions_all.withColumn("is_pocket",lit(False)).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None)).withColumn("user_pocket_id",lit(None))
    global_stock_reverse_split_trans = global_stock_reverse_split_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    global_stock_reverse_split_trans = global_stock_reverse_split_trans.groupBy("account_id","user_id","client_id","global_stock_id","partner_id",'is_pocket',"user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    global_stock_reverse_split_trans = global_stock_reverse_split_trans.withColumn("asset_subtype",lit("global_stock_reverse_split"))
    return global_stock_reverse_split_trans


def calculation_global_stock_mission_rewards(offset,dt_1,dt_2):
    global_stock_mission_rewards_path = config_data["global_stock"]["global_stock_mission_rewards"]
    order_column = global_stock_mission_rewards_path["order_column"]
    columns  = global_stock_mission_rewards_path["columns"]
    primary_keys = config_data["primary_keys"]
    global_stock_mission_rewards_t1,df_deleted_list= global_stock_mission_rewards(offset)
    global_stock_mission_rewards_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,global_stock_mission_rewards_path["global_stock_mission_rewards_t2_path"])
    global_stock_mission_rewards_t2 = spark.read.csv(global_stock_mission_rewards_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    global_stock_mission_rewards_all = dedup_t1_and_t2(global_stock_mission_rewards_t1,global_stock_mission_rewards_t2,order_column,columns,primary_keys)
    global_stock_mission_rewards_all = global_stock_mission_rewards_all.filter(~col("id").isin(df_deleted_list))
    global_stock_mission_rewards_all.coalesce(1).write.mode("overwrite").csv(global_stock_mission_rewards_t2_path + str(dt_1)+"/",header=True)
    global_stock_mission_rewards_trans = global_stock_mission_rewards_all.withColumn("is_pocket",lit(False)).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None)).withColumn("user_pocket_id",lit(None))
    global_stock_mission_rewards_trans = global_stock_mission_rewards_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    global_stock_mission_rewards_trans = global_stock_mission_rewards_trans.groupBy("account_id","user_id","client_id","global_stock_id","partner_id",'is_pocket',"user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    global_stock_mission_rewards_trans = global_stock_mission_rewards_trans.withColumn("asset_subtype",lit("global_stock_mission_rewards"))
    return global_stock_mission_rewards_trans

def calculation_global_stock_merger_transactions(offset,dt_1,dt_2):
    global_stock_merger_transactions_path = config_data["global_stock"]["global_stock_merger_transactions"]
    order_column = global_stock_merger_transactions_path["order_column"]
    columns  = global_stock_merger_transactions_path["columns"]
    primary_keys = config_data["primary_keys"] + ["transaction_type"]
    global_stock_merger_transactions_t1,df_deleted_list= global_stock_merger_transactions(offset)
    global_stock_merger_transactions_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,global_stock_merger_transactions_path["global_stock_merger_transactions_t2_path"])
    global_stock_merger_transactions_t2 = spark.read.csv(global_stock_merger_transactions_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    global_stock_merger_transactions_all = dedup_t1_and_t2(global_stock_merger_transactions_t1,global_stock_merger_transactions_t2,order_column,columns,primary_keys)
    global_stock_merger_transactions_all = global_stock_merger_transactions_all.filter(~col("id").isin(df_deleted_list))
    global_stock_merger_transactions_all.coalesce(1).write.mode("overwrite").csv(global_stock_merger_transactions_t2_path + str(dt_1)+"/",header=True)
    global_stock_merger_transactions_trans = global_stock_merger_transactions_all.withColumn("is_pocket",lit(False)).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None)).withColumn("user_pocket_id",lit(None))
    global_stock_merger_transactions_trans = global_stock_merger_transactions_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    global_stock_merger_transactions_trans = global_stock_merger_transactions_trans.groupBy("account_id","user_id","client_id","global_stock_id","partner_id",'is_pocket',"user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    global_stock_merger_transactions_trans = global_stock_merger_transactions_trans.withColumn("asset_subtype",lit("global_stock_merger_transaction"))
    return global_stock_merger_transactions_trans

def calculation_global_stock_options_transactions(offset,dt_1,dt_2):
    global_stock_option_transactions_path = config_data["global_stock"]["global_stock_options_contract_transactions"]
    order_column = global_stock_option_transactions_path["order_column"]
    columns  = global_stock_option_transactions_path["columns"]
    primary_keys = config_data["primary_keys"]
    global_stock_option_transactions_t1,df_deleted_list = global_stock_option_transactions(offset)
    global_stock_option_transactions_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,global_stock_option_transactions_path["global_stock_options_contract_transactions_t2_path"])
    global_stock_option_transactions_t2 = spark.read.csv(global_stock_option_transactions_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    global_stock_option_transactions_all = dedup_t1_and_t2(global_stock_option_transactions_t1,global_stock_option_transactions_t2,order_column,columns,primary_keys)
    # Remove delete for options contract transactions
    # global_stock_option_transactions_all = global_stock_option_transactions_all.filter(~col("id").isin(df_deleted_list))
    global_stock_option_transactions_all.coalesce(1).write.mode("overwrite").csv(global_stock_option_transactions_t2_path + str(dt_1)+"/",header=True)
    global_stock_option_trans = global_stock_option_transactions_all.withColumn("is_pocket",lit(False))
    global_stock_option_trans = global_stock_option_trans.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    global_stock_option_trans = global_stock_option_trans.withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None)).withColumn("user_pocket_id",lit(None))
    global_stock_option_trans = global_stock_option_trans.groupBy("account_id","user_id","client_id","global_stock_id","partner_id",'is_pocket',"user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated","options_contract_id").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    global_stock_option_trans = global_stock_option_trans.withColumn("asset_subtype",lit("global_stock_options_transactions"))
    return global_stock_option_trans


def get_current_global_stock_price_from_mongo(asset_name):
    try:
        zone = pytz.timezone("Asia/Jakarta")
        offset = config_data["offset"]
        dt_1 = (datetime.now(tz=zone) - timedelta(offset+1))
        t_2 = (datetime.now(tz=zone) - timedelta(offset+1)).date()
        t_1 = (datetime.now(tz=zone) - timedelta(offset)).date()
        dt_0 = (datetime.now(tz=zone) - timedelta(offset))
        dt_1 = dt_1.replace(hour=17,minute=0,second=0,microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        dt_0 = dt_0.replace(hour=17,minute=0,second=0,microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        pipeline = "[{'$match':{endTime: {'$gte':ISODate('" + dt_1 + "'),'$lt':ISODate('" + dt_0 + "')}}},{'$sort':{endTime:-1}},{'$group':{_id:'$globalStockId',mid_price:{'$first':'$midClosePrice'},buy_price:{'$first':'$buyBackClosePrice'},sell_price:{'$first':'$sellClosePrice'}}}]"
        mongo_ip = common_config_data["global_stock"]["mongo_ip"]
        data = spark.read.format("com.mongodb.spark.sql.DefaultSource").option("spark.mongodb.input.uri",mongo_ip).option("pipeline", pipeline).load()
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket_name"], config_data["global_stock"]["price_path"])
        previous_day_price = spark.read.csv("{}{}/".format(s3_path, t_2),header=True,inferSchema=True)
        if((data.count()) < 1):
            data = previous_day_price
        else:
            previous_day_price = previous_day_price.withColumn("dt",lit(t_2))
            data = data.withColumn("dt",lit(t_1))
            data = data.union(previous_day_price)
            data = de_dupe_dataframe(data,["_id"],"dt")
            data = data.drop("dt")
        data.coalesce(1).write.mode('overwrite').save("{}{}/".format(s3_path, t_1),format="csv", header=True)
        global_stock_partner_prices  = data.withColumn("mid_price",round(col("mid_price"),2))
        global_stock_partner_prices = global_stock_partner_prices.select(col("_id").alias("partner_global_stock_id"),"mid_price","buy_price","sell_price")
        logging.info("Successfully loaded {} partner prices".format(asset_name))
        return global_stock_partner_prices

    except Exception as e:
        logging.error("An error has occurred while loading {} partner_prices: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def get_current_global_stock_price(dt_1):
    try:
        s3_path  = "s3a://{}/{}/dt=".format(bucket_name,config_data["global_stock"]["price_path"])
        data = spark.read.csv( s3_path + str(dt_1) +"/*",header=True,inferSchema=True)
        global_stock_partner_prices  = data.withColumn("mid_price",round(col("mid_price"),2))
        global_stock_partner_prices = global_stock_partner_prices.select(col("_id").alias("partner_global_stock_id"),"mid_price","buy_price","sell_price")
        logging.info("Successfully loaded global stock partner prices")
        return global_stock_partner_prices
    except Exception as e:
        logging.error("An error has occurred while loading global stock partner_prices: {}".format( repr(e)))
        logging.exception(e)
        raise e

def get_option_trade_price(dt_1):
    try:
        s3_path  = "s3a://{}/{}/dt=".format(bucket_name,config_data["global_stock"]["option_price_path"])
        data = spark.read.csv( s3_path + str(dt_1) +"/*",header=True,inferSchema=True)

        global_stock_partner_prices  = data.withColumn("product_mid_price",round(col("price"),2))
        global_stock_partner_prices = global_stock_partner_prices.select(col("globalStockId").alias("global_stock_id"),col("optionsContractId").alias("options_contract_id"),"product_mid_price")
        logging.info("Successfully loaded global stock partner prices")
        return global_stock_partner_prices
    except Exception as e:
        logging.error("An error has occurred while loading global stock partner_prices: {}".format( repr(e)))
        logging.exception(e)
        raise e

def start_processing():
    logging.info("Starting execution for global stock aum")
    offset = config_data["offset"]
    dt_1 = get_date(offset)
    dt_2 = get_date(offset+1)
    user_pocket_transaction = user_pocket_transactions(offset,dt_2)
    user_pocket_asset_transactions = user_pocket_asset_transaction(offset,dt_2)
    user_transaction_mapping = get_user_pocket_transaction_asset_transaction_mapping(user_pocket_transaction,user_pocket_asset_transactions,dt_1)
    snap_global_stock_transactions =calculation_global_stock_transactions(offset,dt_1,dt_2,user_transaction_mapping)
    snap_global_stock_split_transactions = calculation_global_stock_split_transactions(offset,dt_1,dt_2)
    snap_global_stock_reverse_split_transaction = calculation_global_stock_reverse_split_transaction(offset,dt_1,dt_2)
    snap_global_stock_mission_rewards = calculation_global_stock_mission_rewards(offset,dt_1,dt_2)
    snap_global_stock_merger_transactions = calculation_global_stock_merger_transactions(offset,dt_1,dt_2)
    snap_global_stock = snap_global_stock_transactions.union(snap_global_stock_split_transactions).union(snap_global_stock_reverse_split_transaction).union(snap_global_stock_mission_rewards)
    snap_global_stock = snap_global_stock.union(snap_global_stock_merger_transactions)
    snap_global_stock = snap_global_stock.groupBy("account_id","user_id","client_id","global_stock_id","partner_id","asset_subtype","is_pocket","user_pocket_id","recurring_transaction_id","is_recurring","first_created","last_updated").agg(round(sum("excute_quantity"),10).alias("excute_quantity"))
    if offset == 0:
        current_global_stock_price = get_current_global_stock_price_from_mongo("global stock hourly ohlc prices")
    else:
        current_global_stock_price = get_current_global_stock_price(dt_1)
    snap_global_stock_with_price = snap_global_stock.join(current_global_stock_price,snap_global_stock["global_stock_id"]==current_global_stock_price["partner_global_stock_id"]).drop("partner_global_stock_id").withColumn("currency",lit("USD"))
    snap_global_stock_with_price = snap_global_stock_with_price.withColumn("asset_type",lit("global_stock")).withColumnRenamed("global_stock_id","product_id")
    snap_global_stock_with_price = snap_global_stock_with_price.withColumnRenamed("mid_price","product_mid_price").withColumnRenamed("buy_price","product_buy_back_price").withColumnRenamed("sell_price","product_sell_price")
    snap_global_stock_with_price = snap_global_stock_with_price.withColumnRenamed("excute_quantity","quantity")
    snap_global_stock_with_price = snap_global_stock_with_price.withColumn("options_contract_id",lit(None).cast('long'))

    current_global_stock_option_price = get_option_trade_price(dt_1)
    global_stock_options = calculation_global_stock_options_transactions(offset,dt_1,dt_2)
    snap_global_stock_option_with_price = global_stock_options.join(current_global_stock_option_price,on=["global_stock_id","options_contract_id"],how="left").withColumn("currency",lit("USD"))
    snap_global_stock_option_with_price = snap_global_stock_option_with_price.withColumnRenamed("excute_quantity","quantity").withColumn("asset_type",lit("global_stock")).withColumnRenamed("global_stock_id","product_id")
    snap_global_stock_option_with_price = snap_global_stock_option_with_price.withColumn("product_buy_back_price",lit(None).cast('double')).withColumn("product_sell_price",lit(None).cast('double'))
    column = snap_global_stock_with_price.columns
    snap_global_stock_option_with_price = snap_global_stock_option_with_price.select(column)
    global_stock_aum = snap_global_stock_with_price.union(snap_global_stock_option_with_price)
    write_snap_global_stock_path =   "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,config_data["global_stock"]["global_stock_aum_path"],str(dt_1))
    global_stock_aum.coalesce(3).write.mode("overwrite").csv(write_snap_global_stock_path,header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--output_folder", help="output folder for GSS aum")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    if args.output_folder:
        output_folder = args.output_folder
        config_data["global_stock"]["global_stock_aum_path"] = output_folder
    start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"aum_global_stock_transaction")
