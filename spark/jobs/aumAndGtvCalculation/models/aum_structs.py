from pyspark.sql.types import LongType, StringType, TimestampType, StructType, StructField, IntegerType, DoubleType, BooleanType, FloatType


schema_for_crypto_currency_transactions = StructType([StructField("id",LongType(),True),
                                                      StructField("account_id",LongType(),True),
                                                      StructField("client_id",LongType(),True),
                                                      StructField("created",TimestampType(),True),
                                                      StructField("crypto_currency_id",LongType(),True),
                                                      StructField("executed_quantity",StringType(),True),
                                                      StructField("executed_unit_price",StringType(),True),
                                                      StructField("order_type",StringType(),True),
                                                      StructField("partner_id",LongType(),True),
                                                      StructField("quantity",StringType(),True),
                                                      StructField("status",StringType(),True),
                                                      StructField("transaction_type",StringType(),True),
                                                      StructField("updated",TimestampType(),True),
                                                      StructField("user_id",LongType(),True),
                                                      StructField("taxation_fee",LongType(),True),
                                                      StructField("recurring_order_type",StringType(),True),
                                                      StructField("recurring_transaction_id",StringType(),True)])

schema_for_crypto_currency_pocket_transactions = StructType([StructField("id",LongType(),True),
                                                      StructField("account_id",LongType(),True),
                                                      StructField("client_id",LongType(),True),
                                                      StructField("created",TimestampType(),True),
                                                      StructField("crypto_currency_id",LongType(),True),
                                                      StructField("executed_quantity",StringType(),True),
                                                      StructField("executed_unit_price",StringType(),True),
                                                      StructField("order_type",StringType(),True),
                                                      StructField("partner_id",LongType(),True),
                                                      StructField("quantity",StringType(),True),
                                                      StructField("status",StringType(),True),
                                                      StructField("transaction_type",StringType(),True),
                                                      StructField("updated",TimestampType(),True),
                                                      StructField("user_id",LongType(),True),
                                                      StructField("taxation_fee",LongType(),True),
                                                      StructField("user_pocket_id",StringType(),True),
                                                      StructField("recurring_order_type",StringType(),True),
                                                      StructField("recurring_transaction_id",StringType(),True)])


schema_for_crypto_currency_mission_rewards = StructType([StructField("id",LongType(),True),
                                                         StructField("account_id",LongType(),True),
                                                         StructField("client_id",LongType(),True),
                                                         StructField("created",TimestampType(),True),
                                                         StructField("crypto_currency_id",LongType(),True),
                                                         StructField("partner_id",LongType(),True),
                                                         StructField("quantity",StringType(),True),
                                                         StructField("status",StringType(),True),
                                                         StructField("updated",TimestampType(),True),
                                                         StructField("user_id",LongType(),True),
                                                         StructField("claim_date",TimestampType(),True),
                                                         StructField("unit_price",StringType(),True),
                                                         StructField("effective_date",TimestampType(),True),
                                                         StructField("expiry_date",TimestampType(),True)])

schema_for_crypto_currency_yield = StructType([StructField("id",LongType(),True),
                                               StructField("account_id",LongType(),True),
                                               StructField("client_id",LongType(),True),
                                               StructField("created",TimestampType(),True),
                                               StructField("crypto_currency_id",LongType(),True),
                                               StructField("partner_id",LongType(),True),
                                               StructField("yield_quantity",StringType(),True),
                                               StructField("status",StringType(),True),
                                               StructField("updated",TimestampType(),True),
                                               StructField("user_id",StringType(),True),
                                               StructField("effective_date",TimestampType(),True)])

schema_for_crypto_currency_wallet_transfers = StructType([StructField("id",LongType(),True),
                                                      StructField("account_id",LongType(),True),
                                                      StructField("client_id",LongType(),True),
                                                      StructField("created_at",TimestampType(),True),
                                                      StructField("crypto_currency_id",LongType(),True),
                                                      StructField("total_quantity",StringType(),True),
                                                      StructField("unit_price",StringType(),True),
                                                      StructField("partner_id",LongType(),True),
                                                      StructField("quantity",StringType(),True),
                                                      StructField("status",StringType(),True),
                                                      StructField("transaction_type",StringType(),True),
                                                      StructField("updated_at",TimestampType(),True),
                                                      StructField("user_id",LongType(),True)])

schema_for_fund_transaction = StructType([StructField("id",LongType(),True),
                                          StructField("account_id",LongType(),True),
                                          StructField("client_id",LongType(),True),
                                          StructField("fund_id",LongType(),True),
                                          StructField("partner_id",LongType(),True),
                                          StructField("quantity",StringType(),True),
                                          StructField("status",StringType(),True),
                                          StructField("updated",TimestampType(),True),
                                          StructField("user_id",LongType(),True),
                                          StructField("transaction_type",StringType(),True),
                                          StructField("currency",StringType(),True),
                                          StructField("unit_price",StringType(),True),
                                          StructField("usd_to_idr",LongType(),True),
                                          StructField("created",TimestampType(),True)])


schema_for_fund_transaction_switch = StructType([StructField("id",LongType(),True),
                                                 StructField("account_id",LongType(),True),
                                                 StructField("client_id",LongType(),True),
                                                 StructField("fund_id",LongType(),True),
                                                 StructField("partner_id",LongType(),True),
                                                 StructField("quantity",StringType(),True),
                                                 StructField("status",StringType(),True),
                                                 StructField("updated",TimestampType(),True),
                                                 StructField("user_id",LongType(),True),
                                                 StructField("transaction_type",StringType(),True),
                                                 StructField("switching_id",StringType(),True),
                                                 StructField("created",TimestampType(),True),
                                                 StructField("target_fund_id",LongType(),True),
                                                 StructField("unit_price",StringType(),True)])

schema_for_forex_transaction = StructType([StructField("id",LongType(),True),
                                           StructField("account_id",LongType(),True),
                                           StructField("client_id",LongType(),True),
                                           StructField("forex_id",LongType(),True),
                                           StructField("partner_id",LongType(),True),
                                           StructField("quantity",StringType(),True),
                                           StructField("status",StringType(),True),
                                           StructField("updated",TimestampType(),True),
                                           StructField("user_id",LongType(),True),
                                           StructField("transaction_type",StringType(),True),
                                           StructField("centralized_wallet_id",StringType(),True),
                                           StructField("created",TimestampType(),True),
                                           StructField("unit_price",StringType(),True)])

schema_for_forex_payment_transaction = StructType([StructField("id",LongType(),True),
                                                   StructField("account_id",LongType(),True),
                                                   StructField("client_id",LongType(),True),
                                                   StructField("forex_id",LongType(),True),
                                                   StructField("partner_id",LongType(),True),
                                                   StructField("amount",StringType(),True),
                                                   StructField("status",StringType(),True),
                                                   StructField("updated",TimestampType(),True),
                                                   StructField("user_id",LongType(),True),
                                                   StructField("activity",StringType(),True),
                                                   StructField("created",TimestampType(),True)])

schema_for_forex_cash_outs_transaction = StructType([StructField("id",LongType(),True),
                                                   StructField("account_id",LongType(),True),
                                                   StructField("client_id",LongType(),True),
                                                   StructField("forex_id",LongType(),True),
                                                   StructField("partner_id",LongType(),True),
                                                   StructField("withdrawal_amount",StringType(),True),
                                                   StructField("status",StringType(),True),
                                                   StructField("updated",TimestampType(),True),
                                                   StructField("user_id",LongType(),True),
                                                   StructField("unit_price",StringType(),True),
                                                   StructField("created",TimestampType(),True)])

schema_for_forex_top_ups_transaction = StructType([StructField("id",LongType(),True),
                                                     StructField("account_id",LongType(),True),
                                                     StructField("client_id",LongType(),True),
                                                     StructField("forex_id",LongType(),True),
                                                     StructField("partner_id",LongType(),True),
                                                     StructField("final_amount",StringType(),True),
                                                     StructField("status",StringType(),True),
                                                     StructField("updated",TimestampType(),True),
                                                     StructField("user_id",LongType(),True),
                                                     StructField("unit_price",StringType(),True),
                                                     StructField("created",TimestampType(),True)])

schema_for_forex_yield_transaction = StructType([StructField("id",LongType(),True),
                                                 StructField("account_id",LongType(),True),
                                                 StructField("user_id",LongType(),True),
                                                 StructField("forex_id",LongType(),True),
                                                 StructField("yield_quantity", DoubleType(),True),
                                                 StructField("yield_percentage",DoubleType(),True),
                                                 StructField("yield_tax",DoubleType(),True),
                                                 StructField("yield_qty_received",DoubleType(),True),
                                                 StructField("usd_to_idr",DoubleType(),True),
                                                 StructField("created",TimestampType(),True),
                                                 StructField("updated",TimestampType(),True)])


schema_for_leverage_wallet_transaction = StructType([StructField("id",LongType(),True),
                                                     StructField("account_id",LongType(),True),
                                                     StructField("client_id",LongType(),True),
                                                     StructField("partner_id",LongType(),True),
                                                     StructField("quantity",StringType(),True),
                                                     StructField("status",StringType(),True),
                                                     StructField("updated",TimestampType(),True),
                                                     StructField("user_id",LongType(),True),
                                                     StructField("wallet_transaction_type",StringType(),True),
                                                     StructField("total_price_in_idr",StringType(),True),
                                                     StructField("unit_price_in_idr",StringType(),True),
                                                     StructField("currency",StringType(),True),
                                                     StructField("created",TimestampType(),True)])


schema_for_leverage_wallet_payment_transaction = StructType([StructField("id",LongType(),True),
                                                             StructField("account_id",LongType(),True),
                                                             StructField("client_id",LongType(),True),
                                                             StructField("partner_id",LongType(),True),
                                                             StructField("amount",StringType(),True),
                                                             StructField("status",StringType(),True),
                                                             StructField("updated",TimestampType(),True),
                                                             StructField("user_id",LongType(),True),
                                                             StructField("realised_pnl",StringType(),True),
                                                             StructField("trading_margin",StringType(),True),
                                                             StructField("unit_price_in_idr",StringType(),True),
                                                             StructField("activity",StringType(),True),
                                                             StructField("leverage",StringType(),True),
                                                             StructField("fee",StringType(),True),
                                                             StructField("created",TimestampType(),True)])

schema_for_leverage_wallet_overnight_fee_transaction = StructType([StructField("id",LongType(),True),
                                                                   StructField("account_id",LongType(),True),
                                                                   StructField("client_id",LongType(),True),
                                                                   StructField("partner_id",LongType(),True),
                                                                   StructField("overnight_fee",StringType(),True),
                                                                   StructField("status",StringType(),True),
                                                                   StructField("updated",TimestampType(),True),
                                                                   StructField("user_id",LongType(),True),
                                                                   StructField("created",TimestampType(),True)])

schema_for_leverage_wallet_account = StructType([StructField("id",LongType(),True),
                                                                   StructField("user_id",LongType(),True),
                                                                   StructField("account_id",LongType(),True),
                                                                   StructField("client_id",LongType(),True),
                                                                   StructField("partner_id",LongType(),True),
                                                                   StructField("balance",LongType(),True),
                                                                   StructField("locked_margin",LongType(),True),
                                                                   StructField("trading_margin",LongType(),True),
                                                                   StructField("total_overnight_fee",LongType(),True),
                                                                   StructField("weighted_cost",LongType(),True),
                                                                   StructField("eligibility_status",StringType(),True),
                                                                   StructField("margin_call_type",StringType(),True),
                                                                   StructField("updated",TimestampType(),True),
                                                                   StructField("created",TimestampType(),True)])


schema_for_global_stock_transactions =  StructType([StructField("id",LongType(),True),
                                                    StructField("account_id",LongType(),True),
                                                    StructField("client_id",LongType(),True),
                                                    StructField("global_stock_id",LongType(),True),
                                                    StructField("partner_id",LongType(),True),
                                                    StructField("quantity",StringType(),True),
                                                    StructField("status",StringType(),True),
                                                    StructField("updated",TimestampType(),True),
                                                    StructField("user_id",LongType(),True),
                                                    StructField("transaction_type",StringType(),True),
                                                    StructField("estimated_quantity",StringType(),True),
                                                    StructField("created",TimestampType(),True),
                                                    StructField("estimated_total_price",StringType(),True),
                                                    StructField("estimated_unit_price",StringType(),True),
                                                    StructField("executed_quantity",StringType(),True),
                                                    StructField("executed_unit_price",StringType(),True),
                                                    StructField("forex_price_id",LongType(),True),
                                                    StructField("order_type",StringType(),True),
                                                    StructField("usd_to_idr",LongType(),True),
                                                    StructField("wallet_type",StringType(),True),
                                                    StructField("user_pocket_id",StringType(),True),
                                                    StructField("transaction_fee",DoubleType(),True),
                                                    StructField("stock_type",StringType(),True),
                                                    StructField("recurring_order_type",StringType(),True),
                                                    StructField("recurring_transaction_id",StringType(),True),
                                                    StructField("transaction_time",TimestampType(),True),
                                                    StructField("transaction_fee_config_id",LongType(),True),
                                                    StructField("waived_off_fee",DoubleType()),
                                                    StructField("trading_hours",StringType(),True)])


schema_for_global_stock_split_transactions = StructType([StructField("id",LongType(),True),
                                                         StructField("account_id",LongType(),True),
                                                         StructField("client_id",LongType(),True),
                                                         StructField("global_stock_id",LongType(),True),
                                                         StructField("partner_id",LongType(),True),
                                                         StructField("quantity_after_split",StringType(),True),
                                                         StructField("status",StringType(),True),
                                                         StructField("updated",TimestampType(),True),
                                                         StructField("user_id",StringType(),True),
                                                         StructField("quantity_before_split",StringType(),True),
                                                         StructField("created",TimestampType(),True),
                                                         StructField("new_rate",StringType(),True)])


schema_for_global_stcok_reverse_split_transaction = StructType([StructField("id",LongType(),True),
                                                                StructField("account_id",LongType(),True),
                                                                StructField("client_id",LongType(),True),
                                                                StructField("global_stock_id",LongType(),True),
                                                                StructField("partner_id",LongType(),True),
                                                                StructField("quantity_after_split",StringType(),True),
                                                                StructField("status",StringType(),True),
                                                                StructField("updated",TimestampType(),True),
                                                                StructField("user_id",StringType(),True),
                                                                StructField("quantity_before_split",StringType(),True),
                                                                StructField("created",TimestampType(),True),
                                                                StructField("new_rate",StringType(),True)])

schema_for_global_stock_mission_rewards = StructType([StructField("id",LongType(),True),
                                                      StructField("account_id",LongType(),True),
                                                      StructField("client_id",LongType(),True),
                                                      StructField("global_stock_id",LongType(),True),
                                                      StructField("partner_id",LongType(),True),
                                                      StructField("status",StringType(),True),
                                                      StructField("updated",TimestampType(),True),
                                                      StructField("user_id",LongType(),True),
                                                      StructField("created",TimestampType(),True),
                                                      StructField("executed_quantity",StringType(),True),
                                                      StructField("executed_unit_price",StringType(),True),
                                                      StructField("usd_to_idr",LongType(),True)])

schema_for_global_stock_merger_trans = StructType([StructField("id",LongType(),True),
                                                   StructField("account_id",LongType(),True),
                                                   StructField("client_id",LongType(),True),
                                                   StructField("source_stock_id",LongType(),True),
                                                   StructField("destination_stock_id",LongType(),True),
                                                   StructField("partner_id",LongType(),True),
                                                   StructField("status",StringType(),True),
                                                   StructField("updated",TimestampType(),True),
                                                   StructField("user_id",LongType(),True),
                                                   StructField("created",TimestampType(),True),
                                                   StructField("reference_table_id",LongType(),True),
                                                   StructField("source_stock_quantity",StringType(),True),
                                                   StructField("destination_stock_quantity",StringType(),True),
                                                   StructField("source_stock_merger_price",StringType(),True),
                                                   StructField("destination_stock_merger_price",StringType(),True),
                                                   StructField("transaction_time",TimestampType(),True)])

schema_for_stock_index_transaction = StructType([StructField("id",LongType(),True),
                                                 StructField("account_id",LongType(),True),
                                                 StructField("currency",StringType(),True),
                                                 StructField("client_id",LongType(),True),
                                                 StructField("stock_index_id",LongType(),True),
                                                 StructField("partner_id",LongType(),True),
                                                 StructField("quantity",DoubleType(),True),
                                                 StructField("status",StringType(),True),
                                                 StructField("updated",TimestampType(),True),
                                                 StructField("user_id",LongType(),True),
                                                 StructField("transaction_type",StringType(),True),
                                                 StructField("created",TimestampType(),True),
                                                 StructField("currency_to_idr",LongType(),True),
                                                 StructField("unit_price",DoubleType(),True)])


schema_for_gold_transaction = StructType([StructField("id",LongType(),True),
                                         StructField("account_id",LongType(),True),
                                         StructField("user_id",LongType(),True),
                                        StructField("status",StringType(),True),
                                         StructField("transaction_type",StringType(),True),
                                         StructField("quantity",StringType(),True),
                                         StructField("client_id",LongType(),True),
                                         StructField("created",TimestampType(),True),
                                         StructField("updated",TimestampType(),True),
                                         StructField("partner_id",LongType(),True),
                                        StructField("unit_price",DoubleType(),True),
                                          StructField("auto_invest",BooleanType(),True)])


schema_for_gold_gift = StructType([StructField("id",LongType(),True),
                                   StructField("account_id",LongType(),True),
                                   StructField("user_id",LongType(),True),
                                   StructField("transaction_type",StringType(),True),
                                   StructField("quantity",StringType(),True),
                                   StructField("client_id",LongType(),True),
                                   StructField("created",TimestampType(),True),
                                   StructField("updated",TimestampType(),True),
                                   StructField("gift_id",StringType(),True),
                                   StructField("status",StringType(),True),
                                   StructField("partner_id",LongType(),True),
                                   StructField("unit_price",DoubleType(),True)])

schema_for_gold_loans = StructType([StructField("id",LongType(),True),
                                    StructField("account_id",LongType(),True),
                                    StructField("user_id",LongType(),True),
                                    StructField("gold_loan_amount",StringType(),True),
                                    StructField("client_id",LongType(),True),
                                    StructField("created",TimestampType(),True),
                                    StructField("updated",TimestampType(),True),
                                    StructField("status",StringType(),True),
                                    StructField("partner_id",LongType(),True),
                                    StructField("currency",StringType(),True),
                                    StructField("issued_price",DoubleType(),True),
                                    StructField("gold_frozen",StringType(),True),
                                    StructField("admin_fee",StringType(),True),
                                    StructField("down_payment",DoubleType(),True),
                                    StructField("interest_rate",DoubleType(),True),
                                    StructField("monthly_installment",DoubleType(),True),
                                    StructField("total_gold_price",DoubleType(),True),
                                    StructField("total_installment",DoubleType(),True),
                                    StructField("installment_index",LongType(),True),
                                    StructField("cash_back",DoubleType(),True),
                                    StructField("fine",DoubleType(),True),
                                    StructField("total_principal",DoubleType(),True),
                                    StructField("transaction_time",TimestampType(),True),
                                    ])

schema_for_gold_withdrawals = StructType([StructField("id",LongType(),True),
                                          StructField("account_id",LongType(),True),
                                          StructField("user_id",LongType(),True),
                                          StructField("net_amount",StringType(),True),
                                          StructField("client_id",LongType(),True),
                                          StructField("created",TimestampType(),True),
                                          StructField("updated",TimestampType(),True),
                                          StructField("status",StringType(),True),
                                          StructField("unit_price",DoubleType(),True)])

schema_for_indo_stock_transaction = StructType([StructField("id",LongType(),True),
                                                StructField("account_id",LongType(),True),
                                                StructField("user_id",LongType(),True),
                                                StructField("transaction_type",StringType(),True),
                                                StructField("executed_quantity",StringType(),True),
                                                StructField("client_id",LongType(),True),
                                                StructField("created",TimestampType(),True),
                                                StructField("updated",TimestampType(),True),
                                                StructField("stock_id",LongType(),True),
                                                StructField("service_provider_status",StringType(),True),
                                                StructField("expiry_type",StringType(),True),
                                                StructField("ordered_quantity",StringType(),True),
                                                StructField("order_type",StringType(),True),
                                                StructField("unit_price",DoubleType(),True)])

schema_for_indo_stock_bonus_transaction = StructType([StructField("id",LongType(),True),
                                                      StructField("account_id",LongType(),True),
                                                      StructField("user_id",LongType(),True),
                                                      StructField("quantity",StringType(),True),
                                                      StructField("created",TimestampType(),True),
                                                      StructField("updated",TimestampType(),True),
                                                      StructField("stock_id",StringType(),True)])

schema_for_indo_stock_ipo_transaction = StructType([StructField("id",LongType(),True),
                                                    StructField("account_id",LongType(),True),
                                                    StructField("user_id",LongType(),True),
                                                    StructField("quantity",StringType(),True),
                                                    StructField("created",TimestampType(),True),
                                                    StructField("updated",TimestampType(),True),
                                                    StructField("stock_id",LongType(),True),
                                                    StructField("sid",StringType(),True),
                                                    StructField("unit_price",DoubleType(),True)])



schema_for_indo_stock_split_transaction = StructType([StructField("id",LongType(),True),
                                                      StructField("account_id",LongType(),True),
                                                      StructField("user_id",LongType(),True),
                                                      StructField("quantity",StringType(),True),
                                                      StructField("created",TimestampType(),True),
                                                      StructField("updated",TimestampType(),True),
                                                      StructField("stock_id",LongType(),True),
                                                      StructField("ratio",StringType(),True),
                                                      StructField("previous_quantity",StringType(),True),
                                                      StructField("current_quantity",StringType(),True)])

schema_for_indo_stock_reverse_split_transaction = StructType([StructField("id",LongType(),True),
                                                              StructField("account_id",LongType(),True),
                                                              StructField("user_id",LongType(),True),
                                                              StructField("quantity",StringType(),True),
                                                              StructField("created",TimestampType(),True),
                                                              StructField("updated",TimestampType(),True),
                                                              StructField("stock_id",StringType(),True),
                                                              StructField("ratio",StringType(),True),
                                                              StructField("previous_quantity",StringType(),True),
                                                              StructField("current_quantity",StringType(),True)])

schema_for_indo_stock_right_issue_transaction = StructType([StructField("id",LongType(),True),
                                                    StructField("account_id",LongType(),True),
                                                    StructField("user_id",LongType(),True),
                                                    StructField("quantity",StringType(),True),
                                                    StructField("created",TimestampType(),True),
                                                    StructField("updated",TimestampType(),True),
                                                    StructField("stock_id",LongType(),True),
                                                    StructField("sid",StringType(),True),
                                                    StructField("unit_price",DoubleType(),True)])


schema_for_indo_stock_warrant_transaction = StructType([StructField("id",LongType(),True),
                                                    StructField("account_id",LongType(),True),
                                                    StructField("user_id",LongType(),True),
                                                    StructField("quantity",StringType(),True),
                                                    StructField("created",TimestampType(),True),
                                                    StructField("updated",TimestampType(),True),
                                                    StructField("stock_id",LongType(),True),
                                                    StructField("sid",StringType(),True),
                                                    StructField("transaction_type",StringType(),True),
                                                    StructField("unit_price",DoubleType(),True)])


schema_for_user_pocket_transactions = StructType([StructField("id",LongType(),True),
                                                  StructField("account_id",LongType(),True),
                                                  StructField("user_id",LongType(),True),
                                                  StructField("user_pocket_id",LongType(),True),
                                                  StructField("buy_fee",DoubleType(),True),
                                                  StructField("sell_fee",DoubleType(),True),
                                                  StructField("currency",StringType(),True),
                                                  StructField("transaction_status",StringType(),True),
                                                  StructField("ref_id",StringType(),True),
                                                  StructField("estimated_amount",DoubleType(),True),
                                                  StructField("executed_amount",DoubleType(),True),
                                                  StructField("estimated_buy_fee",DoubleType(),True),
                                                  StructField("estimated_sell_fee",DoubleType(),True),
                                                  StructField("executed_buy_fee",DoubleType(),True),
                                                  StructField("executed_sell_fee",DoubleType(),True),
                                                  StructField("estimated_tax",DoubleType(),True),
                                                  StructField("executed_tax",DoubleType(),True),
                                                  StructField("transaction_type",StringType(),True),
                                                  StructField("recurring_transaction_id",StringType(),True),
                                                  StructField("pocket_sell_type",StringType(),True),
                                                  StructField("internal_transaction_status",StringType(),True),
                                                  StructField("recurring_order_id",StringType(),True),
                                                  StructField("created",TimestampType(),True),
                                                  StructField("updated",TimestampType(),True),
                                                  StructField("transaction_time",TimestampType(),True),
                                                  StructField("forex_fee",DoubleType(),True),
                                                  StructField("dummy_transaction_fee",DoubleType(),True)])

schema_for_user_pocket_asset_transaction = StructType([StructField("id",LongType(),True),
                                                  StructField("pocket_transaction_id",LongType(),True),
                                                  StructField("asset_transaction_id",LongType(),True),
                                                  StructField("asset_id",LongType(),True),
                                                  StructField("asset_category",StringType(),True),
                                                  StructField("percentage",DoubleType(),True),
                                                  StructField("amount",DoubleType(),True),
                                                  StructField("total_price",DoubleType(),True),
                                                  StructField("quantity",DoubleType(),True),
                                                  StructField("estimated_quantity",DoubleType(),True),
                                                  StructField("executed_quantity",DoubleType(),True),
                                                  StructField("unit_price",DoubleType(),True),
                                                  StructField("transaction_fee",DoubleType(),True),
                                                  StructField("taxation_fee",DoubleType(),True),
                                                  StructField("status",StringType(),True),
                                                  StructField("created",TimestampType(),True),
                                                  StructField("updated",TimestampType(),True),
                                                  StructField("dummy_transaction_fee",DoubleType(),True)])

schema_for_options_contract_transactions = StructType([StructField("id",LongType(),True),
                                                       StructField("options_contract_id",LongType(),True),
                                                       StructField("global_stock_id",LongType(),True),
                                                       StructField("user_id",LongType(),True),
                                                       StructField("account_id",LongType(),True),
                                                       StructField("client_id",LongType(),True),
                                                       StructField("partner_id",LongType(),True),
                                                       StructField("quantity",DoubleType(),True),
                                                       StructField("unit_price",DoubleType(),True),
                                                       StructField("total_price",DoubleType(),True),
                                                       StructField("estimated_quantity",DoubleType(),True),
                                                       StructField("estimated_unit_price",DoubleType(),True),
                                                       StructField("estimated_total_price",DoubleType(),True),
                                                       StructField("executed_quantity",DoubleType(),True),
                                                       StructField("executed_unit_price",DoubleType(),True),
                                                       StructField("executed_total_price",DoubleType(),True),
                                                       StructField("order_type",StringType(),True),
                                                       StructField("transaction_type",StringType(),True),
                                                       StructField("status",StringType(),True),
                                                       StructField("time_in_force",StringType(),True),
                                                       StructField("expiry_date_time",TimestampType(),True),
                                                       StructField("client_order_id",StringType(),True),
                                                       StructField("sub_state",StringType(),True),
                                                       StructField("transaction_fee_config_id",LongType(),True),
                                                       StructField("transaction_fee",DoubleType(),True),
                                                       StructField("cancelled_type",StringType(),True),
                                                       StructField("lock_type",StringType(),True),
                                                       StructField("usd_to_idr",LongType(),True),
                                                       StructField("transaction_time",TimestampType(),True),
                                                       StructField("created",TimestampType(),True),
                                                       StructField("updated",TimestampType(),True),
                                                       StructField("waived_off_fee",DoubleType(),True)])


