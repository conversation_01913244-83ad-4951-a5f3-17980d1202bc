'''
calculated quantity from snap_aum_calculation from - stock index transaction
<EMAIL>
'''
from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["indo_stock"]["asset_folder"]

spark = spark_session_create("aum_calculation")


def calculation_bappebti_wallets(bappebti_wallets_path,dt_1):
    wallet = spark.read.csv(bappebti_wallets_path,header=True,inferSchema=True).distinct()
    wallet_columns = ['account_id', 'cash_balance', 'voucher_balance','cashback', 'free_balance','user_id', 'cash_reward']
    wallet = wallet.select(wallet_columns)
    wallet = wallet.withColumn("wallet_cash",(col("cash_balance")+col("voucher_balance")+col("free_balance")))
    wallet = wallet.withColumn("cash_rewards_cashback",(col("cashback") + col("cash_reward")))
    wallet = wallet.drop("cash_balance","voucher_balance","cashback","free_balance","cash_reward")
    wallet = wallet.groupBy("account_id","user_id").agg(sum("wallet_cash").alias("wallet_cash"), sum("cash_rewards_cashback").alias("cash_rewards_cashback"))
    for cols in ['product_id','quantity', 'product_mid_price', 'product_buy_back_price', 'product_sell_price', 'currency', 'asset_type', 'asset_subtype',"usd_to_idr_mid_price","usd_to_idr_buyback_price","usd_to_idr_sell_price","is_pocket",'client_id','partner_id','user_pocket_id','recurring_transaction_id','is_recurring','first_created','last_updated','options_contract_id']:
        wallet = wallet.withColumn(cols, f.lit(None))
    wallet = wallet.withColumn("asset_type",lit("cash"))
    return wallet

def current_forex_price_currency(current_forex_prices,forex_id):
    current_forex_prices = current_forex_prices.filter(col("forex_id")==forex_id)
    current_forex_prices = current_forex_prices.select(col("mid_price").alias("usd_to_idr_mid_price"),col("buy_back_price").alias("usd_to_idr_buyback_price"),col("sell_price").alias("usd_to_idr_sell_price"))
    current_forex_prices_usd = current_forex_prices.withColumn("currency_dup",lit("USD")).distinct()
    current_forex_prices_idr = current_forex_prices.withColumn("currency_dup",lit("IDR")).distinct()
    columns_name = ["currency_dup","usd_to_idr_mid_price","usd_to_idr_buyback_price","usd_to_idr_sell_price"]
    current_forex_prices_usd= current_forex_prices_usd.select(columns_name)
    current_forex_prices_idr = current_forex_prices_idr.select(columns_name)
    current_forex_price = current_forex_prices_usd.union(current_forex_prices_idr)
    return current_forex_price


def start_processing():
    logging.info("Starting execution for aum Snapshotting")
    offset = config_data["offset"]
    dt_2 = get_date(offset+1)
    dt_1 = get_date(offset)
    crypto_aum_path =  "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["crypto"]["asset_folder"],config_data["crypto"]["crypto_currency_aum_path"],str(dt_1))
    forex_aum_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["forex"]["asset_folder"],config_data["forex"]["forex_aum_path"],str(dt_1))
    forex_leverage_aum_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["forex_leverage"]["asset_folder"],config_data["forex_leverage"]["forex_leverage_aum_path"],str(dt_1))
    fund_aum_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["fund"]["asset_folder"],config_data["fund"]["fund_aum_path"],str(dt_1))
    global_stock_aum_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["global_stock"]["asset_folder"],config_data["global_stock"]["global_stock_aum_path"],str(dt_1))
    gold_aum_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["gold"]["asset_folder"],config_data["gold"]["gold_aum_path"],str(dt_1))
    indo_stock_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,config_data["indo_stock"]["asset_folder"],config_data["indo_stock"]["indo_stock_aum_path"],str(dt_1))
    stock_index_path = "s3a://{}/{}*".format(bucket_name,config_data["stock_index"]["stock_index_decommission_aum"])

    columns = ['account_id','product_id','user_id','quantity', 'product_mid_price', 'product_buy_back_price', 'product_sell_price', 'currency', 'asset_type', 'asset_subtype','is_pocket','client_id','partner_id','user_pocket_id','recurring_transaction_id','is_recurring','first_created','last_updated','options_contract_id']
    crypto_aum = spark.read.csv(crypto_aum_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).select(columns)
    forex_aum = spark.read.csv(forex_aum_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None).cast('string')).withColumn("user_pocket_id",lit(None).cast('string')).select(columns)
    forex_leverage_aum = spark.read.csv(forex_leverage_aum_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None).cast('string')).withColumn("user_pocket_id",lit(None).cast('string')).select(columns)
    fund_aum = spark.read.csv(fund_aum_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None).cast('string')).withColumn("user_pocket_id",lit(None).cast('string')).select(columns)
    global_stock_aum = spark.read.csv(global_stock_aum_path,header=True,inferSchema=True).select(columns)
    gold_aum = spark.read.csv(gold_aum_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).select(columns)
    indo_stock_aum = spark.read.csv(indo_stock_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None).cast('string')).withColumn("user_pocket_id",lit(None).cast('string')).select(columns)
    stock_index_aum = spark.read.csv(stock_index_path,header=True,inferSchema=True).withColumn("options_contract_id",lit(None).cast('long')).withColumn("first_created",lit(None).cast("timestamp")).withColumn("last_updated",lit(None).cast("timestamp")).withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None).cast('string')).withColumn("user_pocket_id",lit(None).cast('string')).select(columns)
    asset_aum = crypto_aum.union(forex_aum).union(fund_aum).union(global_stock_aum).union(gold_aum).union(forex_leverage_aum).union(stock_index_aum)
    asset_aum =  asset_aum.union(indo_stock_aum)
    forex_id = config_data["forex_id"]
    partner_id = config_data["forex_partner_id"]
    current_forex_prices = get_current_forex_price(partner_id,offset)
    current_forex_price = current_forex_price_currency(current_forex_prices,forex_id)
    aum_write_path  =  "s3a://{}{}{}/".format(bucket_name,config_data["aum_snap_path"],str(dt_1))
    asset_aum_usd_to_idr = asset_aum.join(current_forex_price,asset_aum["currency"]==current_forex_price["currency_dup"],"left").drop("currency_dup")
    asset_aum_usd_to_idr = asset_aum_usd_to_idr.withColumn("wallet_cash",lit(0)).withColumn("cash_rewards_cashback",lit(0))
    '''bappebti wallets added in aum'''

    bappebti_wallets_path = "s3a://{}{}{}/*".format(bucket_name,config_data["bappebti_wallets_path"],str(dt_1))
    wallet = calculation_bappebti_wallets(bappebti_wallets_path,dt_1)
    aum_columns = columns + ['wallet_cash','cash_rewards_cashback','usd_to_idr_mid_price','usd_to_idr_buyback_price','usd_to_idr_sell_price']
    asset_aum_usd_to_idr = asset_aum_usd_to_idr.select(aum_columns)
    wallet =wallet.select(aum_columns)
    asset_aum_with_cash = asset_aum_usd_to_idr.union(wallet)
    asset_aum_with_cash = asset_aum_with_cash.withColumn("created_at",lit(dt_1)).withColumn("updated_at",lit(dt_1))
    asset_aum_with_cash.coalesce(1).write.mode("overwrite").csv(aum_write_path,header=True)

if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"aum_calculation")

