'''
calculated quantity from crypto transaction from -
crypto_currency_transactions
crypto_currencies
crypto_currency_mission_rewards
crypto_currency_pluangcuan_yields
crypto_currency_wallet_transfers
<EMAIL>
'''

from aum_common import *
from aum_structs import *
import argparse

bucket_name = config_data["bucket_name"]
config_crypto_path  = config_data["crypto"]
config_crypto_mission  = config_crypto_path["crypto_currency_mission_rewards"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["crypto"]["asset_folder"]

spark = spark_session_create("gtv_crypto_currency")
logging.info("spark session create")


'''
input parmeter- current and previous date
filter - (status=success and transaction type- buy,sell and airdrop)
get only daily level transaction
return daily gtv for crypto currency transaction
'''
def daily_crypto_currency_transactions(execute_date,execute_previous_date):
    crypto_currency_transactions_path = config_crypto_path["crypto_currency_transactions"]
    read_path  ="s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,crypto_currency_transactions_path["write_crypto_currency_transactions_path"],str(execute_date))
    logging.info("read only daily_crypto_currency_transactions")
    current_date_transaction = spark.read.csv(read_path,header=True,inferSchema=True)
    crypto_currency_transactions_t2_path = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,crypto_currency_transactions_path["crypto_currency_transactions_t2_path"])
    logging.info("read snapshot of till previous day")
    crypto_currency_transactions_t2 = spark.read.csv(crypto_currency_transactions_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    current_date_transaction_id = current_date_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin("BUY","SELL")).select("id")
    snapshot_transaction_till_previous_date_id  = crypto_currency_transactions_t2.filter((col("status")=="SUCCESS") & col("transaction_type").isin("BUY","SELL")).select("id")
    logging.info("remove update transaction from current data record")
    current_day_id = current_date_transaction_id.subtract(snapshot_transaction_till_previous_date_id)
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = current_date_transaction.filter(col("id").isin(daily_transaction_id_list))
    daily_gtv_crypto_transaction = only_current_day_transaction.withColumn("total_value",(round(col("excute_quantity")*col("executed_unit_price"),2))).withColumn("execute_date",lit(execute_date))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("is_recurring",when((col("recurring_transaction_id").isNull()) ,lit(False)).otherwise(True))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("is_pocket",lit(False)).withColumn("user_pocket_id",lit(None).cast('string'))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("asset_subtype",lit("crypto_transactions"))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.groupBy("id","account_id","user_id","client_id","crypto_currency_id","partner_id","asset_subtype","is_pocket","transaction_type","user_pocket_id","recurring_transaction_id","is_recurring","created","updated","execute_date").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")))
    return daily_gtv_crypto_transaction

def daily_crypto_currency_pocket_transactions(execute_date,execute_previous_date):
    crypto_currency_pocket_transactions_path = config_crypto_path["crypto_currency_pocket_transactions"]
    read_path  ="s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,crypto_currency_pocket_transactions_path["write_crypto_currency_pocket_transactions_path"],str(execute_date))
    logging.info("read only daily_crypto_currency_pocket_transactions")
    current_date_transaction = spark.read.csv(read_path,header=True,inferSchema=True)
    crypto_currency_transactions_t2_path = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,crypto_currency_pocket_transactions_path["crypto_currency_pocket_transactions_t2_path"])
    logging.info("read snapshot of till previous day of pocket transaction")
    crypto_currency_transactions_t2 = spark.read.csv(crypto_currency_transactions_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    current_date_transaction_id = current_date_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin("BUY","SELL")).select("id")
    snapshot_transaction_till_previous_date_id  = crypto_currency_transactions_t2.filter((col("status")=="SUCCESS") & col("transaction_type").isin("BUY","SELL")).select("id")
    logging.info("remove update transaction from current data record")
    current_day_id = current_date_transaction_id.subtract(snapshot_transaction_till_previous_date_id)
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = current_date_transaction.filter(col("id").isin(daily_transaction_id_list))
    daily_gtv_crypto_transaction = only_current_day_transaction.withColumn("total_value",(round(col("excute_quantity")*col("executed_unit_price"),2))).withColumn("execute_date",lit(execute_date))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("is_recurring",when((col("recurring_transaction_id").isNull()) ,lit(False)).otherwise(True))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("asset_subtype",lit("crypto_pocket_transactions")).withColumn("is_pocket",lit(True))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.groupBy("id","account_id","user_id","client_id","crypto_currency_id","partner_id","asset_subtype","execute_date","is_pocket","transaction_type","user_pocket_id","recurring_transaction_id","is_recurring","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")))
    return daily_gtv_crypto_transaction

'''
input parmeter- current and previous date
filter - (status=SUCCESS) and transaction type- ("DEPOSIT","WITHDRAWAL")
get only daily level wallet transafer
return daily gtv for crypto currency yield 
'''
def daily_crypto_currency_wallet_transfers(execute_date,execute_previous_date):
    crypto_currency_wallet_transfers_path = config_crypto_path["crypto_currency_wallet_transfers"]
    read_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,crypto_currency_wallet_transfers_path["write_crypto_currency_wallet_transfers_path"],str(execute_date))
    logging.info("read only daily_crypto_currency_wallet_transfers")
    current_date_transaction = spark.read.csv(read_path,header=True,inferSchema=True)
    crypto_currency_transactions_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,crypto_currency_wallet_transfers_path["crypto_currency_wallet_transfers_t2_path"])
    logging.info("read snapshot of till previous day of wallet transfer")
    crypto_currency_transactions_t2 = spark.read.csv(crypto_currency_transactions_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    current_date_transaction_id = current_date_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin("DEPOSIT","WITHDRAWAL")).select("id")
    snapshot_transaction_till_previous_date_id  = crypto_currency_transactions_t2.filter((col("status")=="SUCCESS") & col("transaction_type").isin("DEPOSIT","WITHDRAWAL")).select("id")
    logging.info("remove update transaction from current data record")
    current_day_id = current_date_transaction_id.subtract(snapshot_transaction_till_previous_date_id)
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = current_date_transaction.filter(col("id").isin(daily_transaction_id_list))
    daily_gtv_crypto_transaction = only_current_day_transaction.withColumn("total_value",(round(col("excute_quantity")*col("unit_price"),2))).withColumn("execute_date",lit(execute_date))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("is_recurring",lit(False)).withColumn("recurring_transaction_id",lit(None)).withColumn("user_pocket_id",lit(None))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumn("asset_subtype",lit("crypto_wallet_transfer")).withColumn("is_pocket",lit(False))
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.withColumnRenamed("created_at","created").withColumnRenamed("updated_at","updated")
    daily_gtv_crypto_transaction = daily_gtv_crypto_transaction.groupBy("id","account_id","user_id","client_id","crypto_currency_id","partner_id","asset_subtype","execute_date","is_pocket","transaction_type","user_pocket_id","recurring_transaction_id","is_recurring","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")))
    return daily_gtv_crypto_transaction


'''
input parmeter- current and previous date
union - transaction and yield data
group by  data
get only daily level gtv
write gtv data
'''
def execution_processing(execute_date,execute_previous_date):
    columns = ["id","account_id","user_id","client_id","crypto_currency_id","partner_id","asset_subtype","execute_date","is_pocket","transaction_type","user_pocket_id","recurring_transaction_id","is_recurring","created","updated","excute_quantity","total_value"]
    snap_crypto_currency_transactions = daily_crypto_currency_transactions(execute_date,execute_previous_date).select(columns)
    snap_crypto_currency_pocket_transactions = daily_crypto_currency_pocket_transactions(execute_date,execute_previous_date).select(columns)
    snap_crypto_currency_wallet_transfers = daily_crypto_currency_wallet_transfers(execute_date,execute_previous_date).select(columns)
    logging.info("union of all transaction")
    crypto_currency_gtv = snap_crypto_currency_transactions.union(snap_crypto_currency_wallet_transfers).union(snap_crypto_currency_pocket_transactions)
    crypto_currency_gtv = crypto_currency_gtv.withColumn("total_value",f.abs("total_value")).withColumn("excute_quantity",f.abs("excute_quantity"))
    crypto_currency_gtv = crypto_currency_gtv.groupBy("id","account_id","user_id","client_id","crypto_currency_id","partner_id","asset_subtype","execute_date","is_pocket","transaction_type","user_pocket_id","recurring_transaction_id","is_recurring","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")))
    crypto_currency_gtv =crypto_currency_gtv.withColumn("currency",lit("IDR"))
    snap_crypto_currency_with_price = crypto_currency_gtv.withColumn("asset_type",lit("crypto")).withColumnRenamed("crypto_currency_id","product_id")
    
    snap_crypto_currency_with_price = snap_crypto_currency_with_price.withColumnRenamed("excute_quantity","quantity").withColumn("total_value_usd", f.lit(None).cast('string'))
    logging.info("write crypto gtv on s3")
    write_snap_crypto_currency_path =   "s3a://{}{}{}/".format(bucket_name,config_crypto_path["crypto_currency_gtv_path"],str(execute_date))
    snap_crypto_currency_with_price.coalesce(2).write.mode("overwrite").csv(write_snap_crypto_currency_path,header=True)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    offset = config_data["offset"]
    execute_date = get_date(offset)
    execute_previous_date = get_date(offset+1)
    start_time =datetime.now()
    execution_processing(execute_date,execute_previous_date)
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"gtv_crypto_currency")
    