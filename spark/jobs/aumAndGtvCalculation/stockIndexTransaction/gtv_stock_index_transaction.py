'''
calculated quantity from stock index transaction from - stock index transaction
<EMAIL>
'''
from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
stock_index_path = config_data["stock_index"]["stock_index_transaction"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["stock_index"]["asset_folder"]
spark = spark_session_create("gtv_stock_index_transaction")

'''
input = current and previous day
filter status is "success" and transaction_type - "BUY","SELL"
total value = quantity  * unit price
'''
def calculation_stock_index_transaction(execute_date,execute_previous_date):
    read_current_day_transaction_path = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,stock_index_path["write_stock_index_transaction_path"],str(execute_date))
    stock_index_trans_t1 = spark.read.csv(read_current_day_transaction_path,header=True,inferSchema=True)
    stock_index_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,stock_index_path["stock_index_transaction_t2_path"])
    stock_index_t2 = spark.read.csv(stock_index_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    current_date_transaction_id = stock_index_trans_t1.filter((col("status").isin("SUCCESS")) & col("transaction_type").isin("BUY","SELL")).select("id")
    snapshot_transaction_till_previous_date_id   = stock_index_t2.filter((col("status").isin("SUCCESS")) & col("transaction_type").isin("BUY","SELL")).select("id")
    current_day_id = current_date_transaction_id.subtract(snapshot_transaction_till_previous_date_id).distinct()
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = stock_index_trans_t1.filter(col("id").isin(daily_transaction_id_list))
    daily_gtv_transaction = only_current_day_transaction.withColumn("total_value",when((col("currency")=="USD"),round((col("quantity")*col("unit_price")*col("currency_to_idr")),2)).otherwise(col("quantity")*col("unit_price"))).withColumn("created_at",lit(execute_date)).fillna({"currency_to_idr":1})
    daily_gtv_transaction = daily_gtv_transaction.withColumn("total_value_usd",when((col("currency")=="USD"),round((col("quantity")*col("unit_price")),8)).otherwise(round(((col("quantity")*col("unit_price"))/col("currency_to_idr")),8)))
    return daily_gtv_transaction


def execute_processing(execute_date,execute_previous_date):
    daily_gtv_transaction = calculation_stock_index_transaction(execute_date,execute_previous_date)
    daily_gtv_transaction= daily_gtv_transaction.withColumn("excute_quantity",f.abs("excute_quantity")).withColumn("total_value",f.abs("total_value")).withColumn("total_value_usd",f.abs("total_value_usd"))
    daily_gtv_transaction = daily_gtv_transaction.groupBy("account_id","client_id","stock_index_id","partner_id","user_id","created_at","transaction_type").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")),(round(sum("total_value_usd"),8).alias("total_value_usd")))
    snap_stock_index_trans = daily_gtv_transaction.withColumn("asset_type",lit("stock_index")).withColumn("asset_subtype",lit("stock_index")).withColumnRenamed("stock_index_id","product_id").withColumnRenamed("excute_quantity","quantity")
    snap_stock_index_trans = snap_stock_index_trans.withColumn("currency",lit("IDR")).withColumn("is_pocket",lit(False))
    write_snap_path =   "s3a://{}{}{}/".format(bucket_name,stock_index_path["write_snap_path_gtv"],str(execute_date))
    snap_stock_index_trans.coalesce(2).write.mode("overwrite").csv(write_snap_path,header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    offset = config_data["offset"]
    execute_date = get_date(offset)
    execute_previous_date = get_date(offset+1)
    execute_processing(execute_date,execute_previous_date)
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"gtv_stock_index_transaction")
    
