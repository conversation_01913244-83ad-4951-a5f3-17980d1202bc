'''
calculated quantity from stock index transaction from - stock index transaction
<EMAIL>
'''

from aum_common import *
from aum_structs import *

raw_bucket_name = config_data["read_raw_bucket"] + config_data["raw_data_folder"]
bucket_name = config_data["bucket_name"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["stock_index"]["asset_folder"]
stock_index_transaction_path = config_data["stock_index"]["stock_index_transaction"]
delete_record_date = "1970-01-01"


lowerbound = get_date_for_query(config_data["offset"]+1)
upperbound = datetime.now()
spark = spark_session_create("aum_stock_index_transaction")

def calculation_stock_index_transaction(offset):
    dt = get_date(offset)
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,stock_index_transaction_path["read_stock_index_transaction_path"],str(dt))
    s3_delete_path  = "s3a://{}{}{}/*".format(raw_bucket_name,stock_index_transaction_path["read_stock_index_transaction_path"],str(delete_record_date))
    try:
        logging.info("read stock index transaction data from " + s3_path)
        stock_index_transaction = spark.read.json(s3_path)
        order_column = stock_index_transaction_path["order_column"]
        columns  = stock_index_transaction_path["columns"]
        columns = columns
        primary_keys = config_data["primary_keys"]
        try:
            delete_record = spark.read.json(s3_delete_path,modifiedAfter=lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                            modifiedBefore=upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*").filter(col("__op")=="d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        stock_index_trans,df_deleted_list = read_raw_data_saved_dedup(stock_index_transaction,order_column,columns,primary_keys,delete_record)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        stock_index_trans = spark.createDataFrame(emptyRDD,schema_for_stock_index_transaction)
        df_deleted_list =[]
    stock_index_trans = stock_index_trans.withColumn("excute_quantity",when((col("status")=="SUCCESS") & (col("transaction_type")=="BUY"), col("quantity")).when((col("status")=="SUCCESS") & (col("transaction_type")=="SELL"), -col("quantity")).otherwise(0))
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,stock_index_transaction_path["write_stock_index_transaction_path"],str(dt))
    stock_index_trans.coalesce(1).write.mode("overwrite").csv(write_path,header=True)
    return stock_index_trans,df_deleted_list


def get_current_stock_index_price(offset):
    try:
        zone = pytz.timezone("Asia/Jakarta")
        offset = config_data["offset"]
        t_1 = get_date(offset)
        s3_path  = "s3a://{}/{}/dt=".format(bucket_name,config_data["stock_index"]["stock_index_price_path"])
        current_stock_index_prices = spark.read.csv( s3_path + str(t_1) +"/",header=True,inferSchema=True)
        current_stock_index_prices  = current_stock_index_prices.withColumn("mid_price",round(col("mid_price"),2))
        #current_stock_index_prices = current_stock_index_prices.withColumn("buy_back_price",lit(0)).withColumn("sell_price",lit(0))
        current_stock_index_prices = current_stock_index_prices.withColumn("buy_back_price",round(col("buy_back_price"),2)).withColumn("sell_price",round(col("sell_price"),2))
        current_stock_index_prices = current_stock_index_prices.select(
            col("_id").alias("stock_index_id"), col("mid_price").alias("mid_price"),"buy_back_price","sell_price")
        logging.info("Successfully loaded partner prices")
        return current_stock_index_prices
    except Exception as e:
        logging.error("An error has occurred while loading stock index partner_prices: {}".format(repr(e)))
        logging.exception(e)
        raise e


def start_processing():
    logging.info("Starting execution for cashin Snapshotting")
    offset = config_data["offset"]
    dt_2 = get_date(offset+1)
    dt = get_date(offset)
    order_column = stock_index_transaction_path["order_column"]
    columns  = stock_index_transaction_path["columns"]
    primary_keys = config_data["primary_keys"]
    stock_index_trans_t1,df_deleted_list = calculation_stock_index_transaction(offset)
    stock_index_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,stock_index_transaction_path["stock_index_transaction_t2_path"])
    stock_index_t2 = spark.read.csv(stock_index_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    columns = columns + ["excute_quantity"]
    stock_index_trans_all = dedup_t1_and_t2(stock_index_trans_t1,stock_index_t2,order_column,columns,primary_keys)
    stock_index_trans_all = stock_index_trans_all.filter(~col("id").isin(df_deleted_list))
    stock_index_trans_all.coalesce(2).write.mode("overwrite").csv(stock_index_t2_path + str(dt)+"/",header=True)
    snap_stock_index_trans = stock_index_trans_all.groupBy("account_id","client_id","stock_index_id","partner_id","user_id").agg(round(sum("excute_quantity"),8).alias("excute_quantity"))
    stock_index_price  = get_current_stock_index_price(offset)
    stock_index_price = stock_index_price.withColumnRenamed("stock_index_id","stock_index_id_dup")

    snap_stock_index_trans = snap_stock_index_trans.join(stock_index_price,snap_stock_index_trans["stock_index_id"]==stock_index_price["stock_index_id_dup"],"left").drop("stock_index_id_dup").withColumn("currency",lit("USD"))
    snap_stock_index_trans = snap_stock_index_trans.withColumn("asset_type",lit("stock_index")).withColumn("asset_subtype",lit("stock_index")).withColumnRenamed("stock_index_id","product_id").withColumnRenamed("excute_quantity","quantity")
    snap_stock_index_trans = snap_stock_index_trans.withColumnRenamed("mid_price","product_mid_price").withColumnRenamed("buy_back_price","product_buy_back_price").withColumnRenamed("sell_price","product_sell_price").withColumn("is_pocket",lit(False))
    write_snap_path =   "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,stock_index_transaction_path["write_snap_path"],str(dt))
    snap_stock_index_trans.coalesce(2).write.mode("overwrite").csv(write_snap_path,header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"aum_stock_index_transaction")
    