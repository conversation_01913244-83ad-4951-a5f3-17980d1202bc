'''
calculation of us stocks tax report
'''

from aum_common import *
from aum_structs import *
import argparse


class SnapCashoutGtv(object):

    def __init__(self):
        self.offset = config_data["offset"]
        self.execute_date = get_date(self.offset)
        self.execute_previous_date = get_date(self.offset+1)
        self.spark = spark_session_create("snap_cashout_gtv")
        self.bucket_name = config_data["bucket_name"]
        self.partner_id = config_data["forex_partner_id"]

    def current_day_gtv(self):
        crypto_gtv_path = "s3a://{}{}{}/*".format(self.bucket_name, config_data["crypto"]["crypto_currency_gtv_path"], str(self.execute_date))
        forex_gtv_path = "s3a://{}{}{}/*".format(self.bucket_name, config_data["forex"]["forex_gtv_path"], str(self.execute_date))
        global_stock_gtv_path = "s3a://{}{}{}/*".format(self.bucket_name, config_data["global_stock"]["global_stock_gtv_path"], str(self.execute_date))
        gold_gtv_path = "s3a://{}{}{}/*".format(self.bucket_name, config_data["gold"]["gold_gtv_path"], str(self.execute_date))

        logging.info("read daily asset of gtv")
        crypto_gtv = self.spark.read.csv(crypto_gtv_path, header=True, inferSchema=True)
        forex_gtv = self.spark.read.csv(forex_gtv_path, header=True, inferSchema=True)
        global_stock_gtv = self.spark.read.csv(global_stock_gtv_path, header=True, inferSchema=True)
        gold_gtv = self.spark.read.csv(gold_gtv_path, header=True, inferSchema=True)

        logging.info("filter gtv crypto transaction")
        daily_crypto_transaction = crypto_gtv.filter((col("asset_subtype") == "crypto_transactions") & (col("partner_id") == self.partner_id)) \
            .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("daily_total_crypto_buy"),
                    (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("daily_total_crypto_sell")
                    )
        daily_crypto_transaction = daily_crypto_transaction.groupBy("account_id").agg(sum("daily_total_crypto_buy").alias("daily_total_crypto_buy"), sum("daily_total_crypto_sell").alias("daily_total_crypto_sell"))

        logging.info("filter gtv crypto pocket transaction")
        daily_crypto_pocket_transaction = crypto_gtv.filter((col("asset_subtype") == "crypto_pocket_transactions") & (col("partner_id") == self.partner_id)) \
            .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("daily_total_crypto_pocket_buy"),
                    (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("daily_total_crypto_pocket_sell")
                    )
        daily_crypto_pocket_transaction = daily_crypto_pocket_transaction.groupBy("account_id").agg(sum("daily_total_crypto_pocket_buy").alias("daily_total_crypto_pocket_buy"), sum("daily_total_crypto_pocket_sell").alias("daily_total_crypto_pocket_sell"))

        logging.info("filter gtv forex transaction")
        daily_forex_transaction = forex_gtv.filter((col("asset_subtype") == "forex_transactions") & (col("partner_id") == self.partner_id)) \
            .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("daily_total_forex_buy"),
                    (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("daily_total_forex_sell")
                    )
        daily_forex_transaction = daily_forex_transaction.groupBy("account_id").agg(sum("daily_total_forex_buy").alias("daily_total_forex_buy"), sum("daily_total_forex_sell").alias("daily_total_forex_sell"))

        logging.info("filter gtv global stock transaction")
        daily_global_stock_transaction = global_stock_gtv.filter((col("asset_subtype") == "global_stock_transactions") & (col("partner_id") == self.partner_id)) \
            .select("account_id", "stock_type", "trading_hours", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("daily_total_global_stock_buy"),
                    (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("daily_total_global_stock_sell")
                    )
        daily_global_stock_transaction = daily_global_stock_transaction.withColumn("leverage", when((col("stock_type") == "CFD_LEVERAGE") & (col("trading_hours") == "INTRADAY"), 4)
                                                                                   .when((col("stock_type") == "CFD_LEVERAGE"), 2)
                                                                                   .otherwise(1)) \
            .withColumn("daily_total_global_stock_buy", (col("daily_total_global_stock_buy")/col("leverage")).alias("long")) \
            .withColumn("daily_total_global_stock_sell", (col("daily_total_global_stock_sell")/col("leverage")).alias("long"))
        daily_global_stock_transaction = daily_global_stock_transaction.groupBy("account_id").agg(sum("daily_total_global_stock_buy").alias("daily_total_global_stock_buy"), sum("daily_total_global_stock_sell").alias("daily_total_global_stock_sell"))

        logging.info("filter gtv options transaction")
        daily_options_contract_transaction = global_stock_gtv.filter((col("asset_subtype") == "global_stock_options_transactions") & (col("partner_id") == self.partner_id)) \
            .select("account_id", (when(col("transaction_type").isin(["LONG_OPEN"]), col("total_value")).otherwise(0)).alias("daily_total_options_contract_buy"),
                    (when(col("transaction_type").isin(["LONG_CLOSE"]), col("total_value")).otherwise(0)).alias("daily_total_options_contract_sell")
                    )
        daily_options_contract_transaction = daily_options_contract_transaction.groupBy("account_id").agg(sum("daily_total_options_contract_buy").alias("daily_total_options_contract_buy"), sum("daily_total_options_contract_sell").alias("daily_total_options_contract_sell"))

        logging.info("filter gtv gold transaction")
        daily_gold_transaction = gold_gtv.filter((col("asset_subtype") == "gold_transaction") & (col("partner_id") == self.partner_id)) \
            .select("account_id", "partner_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("daily_total_gold_buy"),
                    (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("daily_total_gold_sell")
                    )
        daily_gold_transaction = daily_gold_transaction.groupBy("account_id").agg(sum("daily_total_gold_buy").alias("daily_total_gold_buy"), sum("daily_total_gold_sell").alias("daily_total_gold_sell"))

        logging.info("filter gtv gold loan transaction")

        logging.info("join gtv of all assets")
        daily_gtv = daily_crypto_transaction.join(daily_crypto_pocket_transaction, on=["account_id"], how="full") \
            .join(daily_forex_transaction, on=["account_id"], how="full") \
            .join(daily_gold_transaction, on=["account_id"], how="full") \
            .join(daily_options_contract_transaction, on=["account_id"], how="full") \
            .join(daily_global_stock_transaction, on=["account_id"], how="full").fillna(0)
        daily_gtv = daily_gtv.withColumn("account_id", col("account_id").alias("long"))
        logging.info("Count of current days is: {}".format(daily_gtv.count()))
        return daily_gtv

    def create_agg_cashout_gtv(self, daily_cashout_gtv, previous_agg_cashout_gtv, current_time):
        logging.info("joining agg cashout gtv and current day gtv")
        agg_cashout_gtv = previous_agg_cashout_gtv.join(daily_cashout_gtv, on=["account_id"], how="full") \
            .fillna(0) \
            .withColumn("created", when(col("created").isNull(), lit(current_time)).otherwise(col("created"))) \
            .withColumn("total_crypto_buy", (col("total_crypto_buy") + col("daily_total_crypto_buy")).cast("long")) \
            .withColumn("total_crypto_sell", (col("total_crypto_sell") + col("daily_total_crypto_sell")).cast("long")) \
            .withColumn("total_crypto_pocket_buy", (col("total_crypto_pocket_buy") + col("daily_total_crypto_pocket_buy")).cast("long")) \
            .withColumn("total_crypto_pocket_sell", (col("total_crypto_pocket_sell") + col("daily_total_crypto_pocket_sell")).cast("long")) \
            .withColumn("total_forex_buy", (col("total_forex_buy") + col("daily_total_forex_buy")).cast("long")) \
            .withColumn("total_forex_sell", (col("total_forex_sell") + col("daily_total_forex_sell")).cast("long")) \
            .withColumn("total_gold_buy", (col("total_gold_buy") + col("daily_total_gold_buy")).cast("long")) \
            .withColumn("total_gold_sell", (col("total_gold_sell") + col("daily_total_gold_sell")).cast("long")) \
            .withColumn("total_global_stock_buy", (col("total_global_stock_buy") + col("daily_total_global_stock_buy")).cast("long")) \
            .withColumn("total_global_stock_sell", (col("total_global_stock_sell") + col("daily_total_global_stock_sell")).cast("long")) \
            .withColumn("total_options_contract_buy", (col("total_options_contract_buy") + col("daily_total_options_contract_buy")).cast("long")) \
            .withColumn("total_options_contract_sell", (col("total_options_contract_sell") + col("daily_total_options_contract_sell")).cast("long")) \
            .withColumn("total_stock_index_buy", (col("total_stock_index_buy")).cast("long")) \
            .withColumn("total_stock_index_sell", (col("total_stock_index_sell")).cast("long")) \
            .select("account_id", "total_crypto_buy", "total_crypto_sell", "total_crypto_pocket_buy", "total_crypto_pocket_sell", "total_forex_buy", "total_forex_sell", "total_gold_buy", "total_gold_sell", "total_global_stock_buy", "total_global_stock_sell", "total_stock_index_buy", "total_stock_index_sell", "total_options_contract_buy", "total_options_contract_sell", "created") \
            .withColumn("total_buy", (col("total_crypto_buy") + col("total_crypto_pocket_buy") + col("total_forex_buy") + col("total_gold_buy") + col("total_global_stock_buy") + col("total_stock_index_buy") + col("total_options_contract_buy")).cast("long")) \
            .withColumn("total_sell", (col("total_crypto_sell") + col("total_crypto_pocket_sell") + col("total_forex_sell") + col("total_gold_sell") + col("total_global_stock_sell") + col("total_stock_index_sell") + col("total_options_contract_sell")).cast("long"))
        return agg_cashout_gtv

    def mongo_write_cashout_gtv(self, cashout_gtv):
        original_columns = cashout_gtv.columns
        for cols in original_columns:
            input_str = cols
            renamed_col = convert_snake_to_camel_case(input_str)
            cashout_gtv = cashout_gtv.withColumnRenamed(cols, renamed_col)
        uri = "{}.{}?authSource=admin".format(common_config_data["reporting_uri"], config_data["cashout_gtv_mongo_collection"])
        num_of_partition = config_data["num_of_partition"]
        sleep_time_mongo = config_data["sleep_time_mongo"]
        logging.info("started writing cashout gtv in mongo in {} partitions with sleep time {}".format(num_of_partition, sleep_time_mongo))
        for i in range(0, num_of_partition):
            logging.info("writing partition number {} in mongo".format(i))
            df = cashout_gtv.filter(col("accountId") % num_of_partition == i)
            df.write.format("mongo").option("replaceDocument", "false").option('shardKey', '{"accountId": 1}').option("uri", uri).option("batchsize", config_data["batch_size"]).mode("append").save()
            time.sleep(sleep_time_mongo)

    def start_processing(self):
        logging.info("Starting execution for cashout gtv calculations")
        current_time = datetime.now()

        daily_cashout_gtv = self.current_day_gtv()
        previous_agg_cashout_gtv = self.spark.read.csv("s3a://{}/{}/dt={}".format(self.bucket_name, config_data["cashout_gtv_path"], str(self.execute_previous_date)), header=True, inferSchema=True) \
            .select("account_id", "total_crypto_buy", "total_crypto_sell", "total_crypto_pocket_buy", "total_crypto_pocket_sell", "total_forex_buy", "total_forex_sell", "total_gold_buy", "total_gold_sell", "total_global_stock_buy", "total_global_stock_sell", "total_stock_index_buy", "total_stock_index_sell", "total_options_contract_buy", "total_options_contract_sell", "total_forex_top_ups", "total_buy", "total_sell", "created")

        agg_cashout_gtv = self.create_agg_cashout_gtv(daily_cashout_gtv, previous_agg_cashout_gtv, current_time)

        cashin = self.spark.read.parquet("s3a://{}/{}/dt={}/*".format(self.bucket_name, config_data["topup_path"], str(self.execute_date))).select("account_id", "cashin").withColumnRenamed("cashin", "total_topup")
        cashout = self.spark.read.parquet("s3a://{}/{}/dt={}/*".format(self.bucket_name, config_data["cashout_path"], str(self.execute_date))).select("account_id", "cashout").withColumnRenamed("cashout", "total_cashout")
        cash = cashin.join(cashout, on=["account_id"], how="full")

        forex_top_ups = self.spark.read.csv("s3a://{}/{}/dt={}/*".format(self.bucket_name, config_data["forex_top_ups_path"], str(self.execute_date)), header=True, inferSchema=True).filter((col("status").isin(["COMPLETED"])) & (col("partner_id") == self.partner_id)).select("account_id", (col("final_amount")*col("unit_price")).alias("total_forex_top_ups")) \
            .groupBy(["account_id"]).agg(sum("total_forex_top_ups").alias("total_forex_top_ups"))

        gold_loan_installments = self.spark.read.csv("s3a://{}/{}/dt={}/*".format(self.bucket_name, config_data["installment_payments_path"], str(self.execute_date)), header=True, inferSchema=True).filter((col("status").isin(["PAID", "CANCEL_HAS_PAID"])) & (col("partner_id") == self.partner_id))

        gold_loans = self.spark.read.csv("s3a://{}{}{}{}{}/*".format(self.bucket_name, config_data["aum_folder"], config_data["gold"]["asset_folder"], config_data["gold"]["gold_loans"]["gold_loans_t2_path"], str(self.execute_date)), header=True, inferSchema=True).select(col("id").alias("gold_loan_id"), "account_id").distinct()

        gold_loan_installments = gold_loan_installments.join(gold_loans, on=["gold_loan_id"], how="left")
        gold_loan_installments = gold_loan_installments.groupBy(["account_id"]).agg(sum("money_paid").alias("total_gold_loan_paid"))

        cashout_gtv = agg_cashout_gtv.join(cash, on=["account_id"], how="full") \
            .join(forex_top_ups, on=["account_id"], how="full") \
            .join(gold_loan_installments, on=["account_id"], how="full") \
            .fillna(0)

        cashout_gtv = cashout_gtv.withColumn("updated", lit(current_time)) \
            .withColumn("total_buy", col("total_buy") + col("total_gold_loan_paid"))
        account = self.spark.read.csv("s3a://{}/{}/dt={}/*".format(self.bucket_name, config_data["account_path"], str(self.execute_date)), header=True, inferSchema=True).select("id", "user_id").withColumnRenamed("id", "account_id").distinct()
        cashout_gtv = cashout_gtv.join(account, on=["account_id"], how="inner")
        cashout_gtv = cashout_gtv.withColumn("account_id", col("account_id").cast(LongType()))
        cashout_gtv.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/dt={}".format(self.bucket_name, config_data["cashout_gtv_path"], str(self.execute_date)), header=True)

        logging.info("cashout gtv successfully written in s3")
        cashout_gtv = cashout_gtv.select("account_id", "user_id", "created", "updated", "total_buy", "total_sell", "total_forex_top_ups", "total_cashout", "total_topup", "total_crypto_buy","total_crypto_pocket_buy", "total_forex_buy", "total_global_stock_buy", "total_gold_buy", "total_gold_loan_paid", "total_stock_index_buy")
        self.mongo_write_cashout_gtv(cashout_gtv)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data["offset"] = int(offset)
    obj = SnapCashoutGtv()
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "snap_cashout_gtv")
