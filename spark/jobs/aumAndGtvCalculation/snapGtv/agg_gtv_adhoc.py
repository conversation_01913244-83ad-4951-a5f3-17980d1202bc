'''
crypto_transaction
crypto_pocket_transaction
gold transaction
gold loan
forex transaction
global stock transaction
stock index transaction
'''

from aum_common import *
from aum_structs import *
import argparse

spark = spark_session_create("agg_gtv")
bucket_name = config_data["bucket_name"]
partner_id = config_data["forex_partner_id"]

def start_processing():
    offset = config_data["offset"]
    dt = get_date(offset)

    crypro_transaction  = spark.read.csv("s3a://{}/aum_calculation/crypto_currency/crypto_currency_transactions/t_2/dt={}".format(bucket_name,str(dt)),header=True,inferSchema=True)
    crypro_pocket_transaction  = spark.read.csv("s3a://{}/aum_calculation/crypto_currency/crypto_currency_pocket_transactions/t_2/dt={}".format(bucket_name,str(dt)),header=True,inferSchema=True)
    forex_transaction  = spark.read.csv("s3a://{}/aum_calculation/forex/forex_transactions/t_2/dt={}".format(bucket_name,str(dt)),header=True,inferSchema=True)
    gold_transaction  = spark.read.csv("s3a://{}/aum_calculation/gold/gold_transactions/t_2/dt={}".format(bucket_name,str(dt)),header=True,inferSchema=True)
    global_stock_transaction  = spark.read.csv("s3a://{}/aum_calculation/global_stock/global_stock_transactions/t_2/dt={}".format(bucket_name,str(dt)),header=True,inferSchema=True)
    option_contract_transaction = spark.read.csv("s3a://{}/aum_calculation/global_stock/options_contract_transactions/t_2/dt={}".format(bucket_name,str(dt)),header=True,inferSchema=True)
    stock_index_transaction  = spark.read.csv("s3a://{}/aum_calculation/stock_index/stock_index_transaction/t_2/dt={}".format(bucket_name,"2024-06-06"),header=True,inferSchema=True)

    crypro_transaction = crypro_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin(["BUY", "SELL"])) \
        .filter(col("partner_id") == partner_id) \
        .withColumn("total_value", round((col("executed_quantity")*col("executed_unit_price")), 2)) \
        .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("total_crypto_buy"),
                (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("total_crypto_sell")
                )
    crypro_transaction = crypro_transaction.groupBy("account_id").agg(sum("total_crypto_buy").alias("total_crypto_buy"), sum("total_crypto_sell").alias("total_crypto_sell"))

    crypro_pocket_transaction = crypro_pocket_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin(["BUY", "SELL"])) \
        .filter(col("partner_id") == partner_id) \
        .withColumn("total_value", round((col("executed_quantity")*col("executed_unit_price")), 2)) \
        .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("total_crypto_pocket_buy"),
                (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("total_crypto_pocket_sell")
                )
    crypro_pocket_transaction = crypro_pocket_transaction.groupBy("account_id").agg(sum("total_crypto_pocket_buy").alias("total_crypto_pocket_buy"), sum("total_crypto_pocket_sell").alias("total_crypto_pocket_sell"))

    forex_transaction = forex_transaction.filter((col("status")=="SUCCESS") & col("transaction_type").isin(["BUY", "SELL"])) \
        .filter(col("partner_id") == partner_id) \
        .withColumn("total_value", round((col("quantity")*col("unit_price")), 2)) \
        .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("total_forex_buy"),
                (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("total_forex_sell")
                )
    forex_transaction = forex_transaction.groupBy("account_id").agg(sum("total_forex_buy").alias("total_forex_buy"), sum("total_forex_sell").alias("total_forex_sell"))

    gold_transaction = gold_transaction.filter((col("status")=="SUCCESS") & col("transaction_type").isin(["BUY", "SELL"])) \
        .filter(col("partner_id") == partner_id) \
        .withColumn("total_value", round((col("quantity")*col("unit_price")), 2)) \
        .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("total_gold_buy"),
                (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("total_gold_sell")
                )
    gold_transaction = gold_transaction.groupBy("account_id").agg(sum("total_gold_buy").alias("total_gold_buy"), sum("total_gold_sell").alias("total_gold_sell"))

    global_stock_transaction = global_stock_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin(["BUY", "SELL"])) \
        .filter(col("partner_id") == partner_id) \
        .withColumn("leverage", when((col("stock_type") == "CFD_LEVERAGE") & (col("trading_hours") == "INTRADAY"), 4)
                    .when((col("stock_type") == "CFD_LEVERAGE"), 2)
                    .otherwise(1)) \
        .withColumn("total_value",round((col("executed_quantity")*col("executed_unit_price")*col("usd_to_idr"))/col("leverage"),2)) \
        .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("total_global_stock_buy"),
                (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("total_global_stock_sell")
                ) \
        .groupBy("account_id").agg(sum("total_global_stock_buy").alias("total_global_stock_buy"), sum("total_global_stock_sell").alias("total_global_stock_sell"))

    stock_index_transaction = stock_index_transaction.filter((col("status").isin("SUCCESS")) & col("transaction_type").isin(["BUY", "SELL"])) \
        .filter(col("partner_id") == partner_id) \
        .fillna({"currency_to_idr":1}) \
        .withColumn("total_value",when((col("currency")=="USD"),round((col("quantity")*col("unit_price")*col("currency_to_idr")),2)).otherwise(col("quantity")*col("unit_price"))) \
        .select("account_id", (when(col("transaction_type").isin(["BUY"]), col("total_value")).otherwise(0)).alias("total_stock_index_buy"),
                (when(col("transaction_type").isin(["SELL"]), col("total_value")).otherwise(0)).alias("total_stock_index_sell")
                ) \
        .groupBy("account_id").agg(sum("total_stock_index_buy").alias("total_stock_index_buy"), sum("total_stock_index_sell").alias("total_stock_index_sell"))

    option_contract_transaction = option_contract_transaction.filter((col("status").isin("SUCCESS","PARTIALLY_FILLED")) & col("transaction_type").isin(["LONG_OPEN", "LONG_CLOSE"])) \
        .filter(col("partner_id") == partner_id) \
        .withColumn("total_value", (round(col("executed_quantity")*col("executed_unit_price")*col("usd_to_idr"),2))) \
        .select("account_id", (when(col("transaction_type").isin(["LONG_OPEN"]), col("total_value")).otherwise(0)).alias("total_options_contract_buy"),
                (when(col("transaction_type").isin(["LONG_CLOSE"]), col("total_value")).otherwise(0)).alias("total_options_contract_sell")
                ) \
        .groupBy("account_id").agg(sum("total_options_contract_buy").alias("total_options_contract_buy"), sum("total_options_contract_sell").alias("total_options_contract_sell"))

    agg_gtv = crypro_transaction.join(crypro_pocket_transaction,on=["account_id"],how="full").fillna(0)
    agg_gtv = agg_gtv.join(forex_transaction,on=["account_id"],how="full").fillna(0)
    agg_gtv = agg_gtv.join(gold_transaction,on=["account_id"],how="full").fillna(0)
    agg_gtv = agg_gtv.join(global_stock_transaction,on=["account_id"],how="full").fillna(0)
    agg_gtv = agg_gtv.join(stock_index_transaction,on=["account_id"],how="full").fillna(0)
    agg_gtv = agg_gtv.join(option_contract_transaction,on=["account_id"],how="full").fillna(0)


    cashin = spark.read.parquet("s3a://{}/cashin/dt={}/*".format(bucket_name,str(dt))).select("account_id","cashin").withColumnRenamed("cashin","total_topup")
    cashout = spark.read.parquet("s3a://{}/cashouts/dt={}/*".format(bucket_name,str(dt))).select("account_id","cashout").withColumnRenamed("cashout","total_cashout")
    cash = cashin.join(cashout,on=["account_id"],how="full").fillna(0)

    forex_top_ups = spark.read.csv("s3a://{}/{}/dt={}/*".format(bucket_name, "forex_top_ups", str(dt)), header=True, inferSchema=True).filter((col("status").isin(["COMPLETED"])) & (col("partner_id") == partner_id)).select("account_id", (col("final_amount")*col("unit_price")).alias("total_forex_top_ups")) \
        .groupBy(["account_id"]).agg(sum("total_forex_top_ups").alias("total_forex_top_ups"))

    gold_loan_installments = spark.read.csv("s3a://{}/{}/dt={}/*".format(bucket_name, "installment_payments", str(dt)), header=True, inferSchema=True).filter((col("status").isin(["PAID", "CANCEL_HAS_PAID"])) & (col("partner_id") == partner_id))
    gold_loans = spark.read.csv("s3a://{}/{}/dt={}/*".format(bucket_name, "aum_calculation/gold/gold_loans/t_2", str(dt)), header=True, inferSchema=True).select(col("id").alias("gold_loan_id"), "account_id")
    gold_loan_installments = gold_loan_installments.join(gold_loans, on=["gold_loan_id"], how="left")
    gold_loan_installments = gold_loan_installments.groupBy(["account_id"]).agg(sum("money_paid").alias("total_gold_loan_paid"))

    cashout_gtv = agg_gtv.join(cash,on=["account_id"],how="full") \
        .join(forex_top_ups, on=["account_id"], how="full") \
        .join(gold_loan_installments, on=["account_id"], how="full") \
        .fillna(0)

    cashout_gtv= cashout_gtv.withColumn("total_buy",round((col("total_crypto_buy")+col("total_crypto_pocket_buy") + col("total_forex_buy") + col("total_gold_buy") + col("total_gold_loan_paid") + col("total_global_stock_buy") + col("total_stock_index_buy")), 2)) \
        .withColumn("total_sell", round((col("total_crypto_sell") + col("total_crypto_pocket_sell") + col("total_forex_sell") + col("total_gold_sell") + col("total_global_stock_sell") + col("total_stock_index_sell") + col("total_options_contract_sell")), 2))

    current_time = datetime.now()
    cashout_gtv = cashout_gtv.withColumn("created",lit(current_time)).withColumn("updated",lit(current_time))
    logging.info(str(cashout_gtv.count()))
    account = spark.read.csv("s3a://{}/accounts/snapshots/dt={}/*".format(bucket_name,str(dt)),header=True,inferSchema=True).select("id","user_id").withColumnRenamed("id","account_id").distinct()
    cashout_gtv  = cashout_gtv.join(account,on=["account_id"],how="inner")
    logging.info(str(cashout_gtv.count()))
    cashout_gtv.coalesce(1).write.mode("overwrite").csv("s3a://{}/gtv_calculation/bappebti_wallet_gtv1/dt={}".format(bucket_name,str(dt)),header=True)



if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)

    start_processing()

