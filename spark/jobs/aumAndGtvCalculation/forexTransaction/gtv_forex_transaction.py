'''
calculated quantity from forex transaction from - forex payment transaction and forex transaction
<EMAIL>
'''


from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
forex = config_data["forex"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["forex"]["asset_folder"]

spark = spark_session_create("gtv_forex_transaction")
logging.info("spark session create")

'''
input parmeter- current and previous date
filter - (status=success), transaction type- (buy,sell)
get only daily level forex trnasaction
total_value = quantity*unit_price
return daily gtv for forex transaction
'''
def calculation_forex_transaction(execute_date,execute_previous_date):
    forex_transaction_path = forex["forex_transaction"]
    forex_transaction_read_path_t1 = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,forex_transaction_path["write_forex_transaction_path"],str(execute_date))
    forex_transaction_t1 = spark.read.csv(forex_transaction_read_path_t1,header=True,inferSchema=True)
    forex_transaction_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,forex_transaction_path["forex_transactions_t2_path"])
    forex_transaction_t2 = spark.read.csv(forex_transaction_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    current_date_transaction_id = forex_transaction_t1.filter((col("status")=="SUCCESS") & col("transaction_type").isin("BUY","SELL")).select("id")
    snapshot_transaction_till_previous_date_id  = forex_transaction_t2.filter((col("status")=="SUCCESS") & col("transaction_type").isin("BUY","SELL")).select("id")
    current_day_id = current_date_transaction_id.subtract(snapshot_transaction_till_previous_date_id).distinct()
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = forex_transaction_t1.filter(col("id").isin(daily_transaction_id_list))
    daily_gtv_forex_transaction = only_current_day_transaction.withColumn("total_value",(round(col("excute_quantity")*col("unit_price"),2))).withColumn("execute_date",lit(execute_date))
    daily_gtv_forex_transaction = daily_gtv_forex_transaction.withColumn("asset_subtype",lit("forex_transactions"))
    daily_gtv_forex_transaction = daily_gtv_forex_transaction.groupBy("id","account_id","user_id","client_id","forex_id","partner_id","asset_subtype","execute_date","transaction_type","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")))
    return daily_gtv_forex_transaction


def execute_start_processing(execute_date,execute_previous_date):
    logging.info("Starting execution for forex gtv")
    snap_forex_transaction = calculation_forex_transaction(execute_date,execute_previous_date)
    snap_forex  = snap_forex_transaction
    snap_forex = snap_forex.withColumn("total_value",f.abs("total_value")).withColumn("excute_quantity",f.abs("excute_quantity"))
    snap_forex = snap_forex.groupBy("id","account_id","user_id","client_id","forex_id","partner_id","asset_subtype","execute_date","transaction_type","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")))
    snap_forex_with_price = snap_forex.withColumn("asset_type",lit("forex")).withColumnRenamed("forex_id","product_id")
    snap_forex_with_price = snap_forex_with_price.withColumnRenamed("excute_quantity","quantity").withColumn("currency",lit("IDR")).withColumn("total_value_usd", f.lit(None).cast('string')).withColumn("is_pocket",lit(False))
    write_snap_forex_with_price_path =   "s3a://{}{}{}/".format(bucket_name,forex["forex_gtv_path"],str(execute_date))
    snap_forex_with_price.coalesce(1).write.mode("overwrite").csv(write_snap_forex_with_price_path,header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    offset = config_data["offset"]
    execute_date = get_date(offset)
    execute_previous_date = get_date(offset+1)
    execute_start_processing(execute_date,execute_previous_date)
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"gtv_forex_transaction")
