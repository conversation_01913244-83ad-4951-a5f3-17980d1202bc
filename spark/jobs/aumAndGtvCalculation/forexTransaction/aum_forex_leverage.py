'''
calculated quantity from forex leverage transaction from - leverage payment transaction and leverage wallet and night fees
<EMAIL>
'''


from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
aum_folder = config_data["aum_folder"]
asset_folder = config_data["forex_leverage"]["asset_folder"]
raw_bucket_name = config_data["read_raw_bucket"] + config_data["raw_data_folder"]
global_stock_returns_path = config_data["forex_leverage"]["global_stock_returns_path"]
leverage_wallet_account_folder = config_data["forex_leverage"]["leverage_wallet_account"]
delete_record_date = "1970-01-01"

spark = spark_session_create("aum_forex_leverage")
logging.info("spark session create")


lowerbound = get_date_for_query(config_data["offset"]+1)
upperbound = datetime.now()

def read_from_kafka_topic_stock_list(bootstrap_servers, topics):
    logging.info("reading data from {} kafka topic/topics".format(topics))
    df = spark.read.format("kafka") \
        .option("kafka.security.protocol", config_data["kafka_security_protocol"]) \
        .option("kafka.ssl.truststore.location", config_data["kafka_truststore_location"]) \
        .option("kafka.ssl.truststore.password", config_data["kafka_truststore_password"]) \
        .option("kafka.ssl.keystore.location", config_data["kafka_keystore_location"]) \
        .option("kafka.ssl.keystore.password", config_data["kafka_keystore_password"]) \
        .option("kafka.ssl.key.password", config_data["kafka_key_password"]) \
        .option("kafka.ssl.keystore.type", config_data["kafka_keystore_type"]) \
        .option("kafka.bootstrap.servers", bootstrap_servers) \
        .option("subscribe", topics) \
        .load()
    df = df.selectExpr("CAST(value AS STRING)")
    df = df.withColumn("value", f.from_json(df.value, MapType(StringType(), StringType())))
    stock_list = df.filter((col("value.status") == "ACTIVE") & (col("value.stock_type") == "CFD_LEVERAGE")).select("value.id").distinct()
    logging.info("Successfully read {} data".format(topics))
    return stock_list

def get_global_stock_return(dt_1,global_stock_list):
    s3_path = "s3a://{}{}{}/*".format(bucket_name,global_stock_returns_path,str(dt_1))
    logging.info("read global stock return for unrealised gain")
    df = spark.read.csv(s3_path,header=True,inferSchema=True).select("global_stock_id","account_id","unrealised_gain")
    leverage_account = df.join(global_stock_list,df["global_stock_id"]==global_stock_list["global_stock_id_dup"],"inner").drop("global_stock_id_dup")
    leverage_account_gain = leverage_account.groupBy("account_id").agg(round(sum("unrealised_gain"),8).alias("unrealised_gain"))
    return leverage_account_gain


def leverage_wallet_account_calculation(offset):
    dt = get_date(offset)
    s3_path  = "s3a://{}{}{}/*".format(raw_bucket_name,leverage_wallet_account_folder["read_leverage_wallet_account_path"],str(dt))
    s3_delete_path = "s3a://{}{}{}/*".format(raw_bucket_name,leverage_wallet_account_folder["read_leverage_wallet_account_path"],str(delete_record_date))
    try:
        logging.info("read leverage wallet account path data from " + s3_path)
        leverage_wallet_account = spark.read.json(s3_path)
        order_column = leverage_wallet_account_folder["order_column"]
        columns  = leverage_wallet_account_folder["columns"]
        primary_keys = config_data["primary_keys"]
        leverage_wallet_accounts = read_raw_data_saved_dedup_t1(leverage_wallet_account,order_column,columns,primary_keys)
    except Exception as e:
        emptyRDD = spark.sparkContext.emptyRDD()
        leverage_wallet_accounts = spark.createDataFrame(emptyRDD,schema_for_leverage_wallet_account)
    delete_record = get_delete_record(s3_delete_path,schema_for_leverage_wallet_account,lowerbound,upperbound)
    leverage_wallet_accounts = leverage_wallet_accounts.join(delete_record,leverage_wallet_accounts["id"]==delete_record["delete_id"],"leftanti")
    write_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder, leverage_wallet_account_folder["write_leverage_wallet_account_path"],str(dt))
    leverage_wallet_accounts.coalesce(2).write.mode("overwrite").csv(write_path,header=True)
    return leverage_wallet_accounts,delete_record

def calculation_leverage_wallet_account(offset,dt_1,dt_2):
    columns  = leverage_wallet_account_folder["columns"]
    leverage_wallet_account_t1,delete_record= leverage_wallet_account_calculation(offset)
    leverage_wallet_account_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,leverage_wallet_account_folder["leverage_wallet_account_t2_path"])
    leverage_wallet_account_t2 = spark.read.csv(leverage_wallet_account_t2_path + str(dt_2) + "/*",header=True,inferSchema=True)
    leverage_wallet_account_t2 = leverage_wallet_account_t2.select(columns)
    leverage_wallet_account_t1 = leverage_wallet_account_t1.select(columns)
    snap_leverage_wallet_account = leverage_wallet_account_t1.union(leverage_wallet_account_t2)
    snap_leverage_wallet_account = de_dupe_dataframe(snap_leverage_wallet_account,["account_id","user_id"],"updated")
    snap_leverage_wallet_account = snap_leverage_wallet_account.join(delete_record,snap_leverage_wallet_account["id"]==delete_record["delete_id"],"leftanti")
    snap_leverage_wallet_account.coalesce(2).write.mode("overwrite").csv(leverage_wallet_account_t2_path + str(dt_1)+"/",header=True)
    return snap_leverage_wallet_account


def calculate_withdraw_balance(leverage_account_balance_with_gain):
    leverage_account_withdraw = leverage_account_balance_with_gain.fillna({'balance': 0}).fillna({'locked_margin': 0}).fillna(
        {'trading_margin': 0}).fillna({'total_overnight_fee': 0}).fillna({'unrealised_gain': 0})
    leverage_account_withdraw  = leverage_account_withdraw.filter(col("eligibility_status")=="ELIGIBLE").withColumn("excute_quantity",when((col("unrealised_gain")>0), (col("balance") - col("locked_margin") - col("trading_margin"))).when((col("unrealised_gain")<=0), (col("balance") - col("locked_margin") - col("trading_margin") + col("unrealised_gain"))).otherwise(0))
    leverage_account_withdraw = leverage_account_withdraw.withColumn("excute_quantity",when((col("excute_quantity")>0),col("excute_quantity")).otherwise(0))
    leverage_account_withdraw = leverage_account_withdraw.withColumn("forex_id",lit(10000))
    win= Window.partitionBy("account_id","user_id","partner_id","client_id")
    leverage_account_withdraw = leverage_account_withdraw.withColumn("first_created",f.min(col("created")).over(win)).withColumn("last_updated",f.max(col("updated")).over(win))
    snap_leverage_forex = leverage_account_withdraw.groupBy("account_id","user_id","client_id","forex_id","partner_id","first_created","last_updated").agg(sum("excute_quantity").alias("excute_quantity"))
    return snap_leverage_forex


def start_processing():
    logging.info("Starting execution for forex leverage")
    offset = config_data["offset"]
    dt_1 = get_date(offset)
    dt_2 = get_date(offset+1)
    bootstrap_servers = config_data["bootstrap_servers"]
    topics = config_data["global_stock_topic"]
    global_stock_list = read_from_kafka_topic_stock_list(bootstrap_servers, topics)
    global_stock_list = global_stock_list.drop("status","stock_type").withColumnRenamed("id", "global_stock_id_dup")
    leverage_account = get_global_stock_return(dt_1,global_stock_list)
    leverage_account = leverage_account.withColumnRenamed("account_id","account_id_dup")
    snap_leverage_wallet_account = calculation_leverage_wallet_account(offset,dt_1,dt_2)
    leverage_account_balance_with_gain =  snap_leverage_wallet_account.join(leverage_account,snap_leverage_wallet_account["account_id"]==leverage_account["account_id_dup"],"full").drop("account_id_dup")
    leverage_account_balance_with_unrealized_gain_path = "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,config_data["forex_leverage"]["leverage_account_balance_with_unrealized_gain_path"],str(dt_1))
    leverage_account_balance_with_gain.coalesce(1).write.mode("overwrite").csv(leverage_account_balance_with_unrealized_gain_path,header=True)
    snap_leverage_forex = calculate_withdraw_balance(leverage_account_balance_with_gain)

    partner_id = config_data["forex"]["forex_partner_id"]
    forex_price = get_current_forex_price(partner_id,offset)

    forex_price = forex_price.select(col("forex_id").alias("forex_id_dup"),
                                                       col("mid_price").alias("forex_mid_price"),col("buy_back_price").alias("forex_buyback_price"),col("sell_price").alias("forex_sell_price"))

    snap_leverage_forex_with_price = snap_leverage_forex.join(forex_price,snap_leverage_forex["forex_id"]==forex_price["forex_id_dup"],"left").drop("forex_id_dup")
    snap_leverage_forex_with_price = snap_leverage_forex_with_price.withColumn("asset_type",lit("forex")).withColumn("asset_subtype",lit("forex_leverage")).withColumnRenamed("forex_id","product_id")
    snap_leverage_forex_with_price = snap_leverage_forex_with_price.withColumnRenamed("forex_mid_price","product_mid_price").withColumnRenamed("forex_buyback_price","product_buy_back_price").withColumnRenamed("forex_sell_price","product_sell_price")
    snap_leverage_forex_with_price = snap_leverage_forex_with_price.withColumnRenamed("excute_quantity","quantity").withColumn("currency",lit("IDR")).withColumn("is_pocket",lit(False))
    write_snap_leverage_forex_with_price_path =   "s3a://{}{}{}{}{}/".format(bucket_name,aum_folder,asset_folder,config_data["forex_leverage"]["forex_leverage_aum_path"],str(dt_1))
    snap_leverage_forex_with_price.coalesce(1).write.mode("overwrite").csv(write_snap_leverage_forex_with_price_path,header=True)

if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"aum_forex_leverage")
    