'''
calculated quantity from fund transaction from - fund transaction and fund switching
<EMAIL>
'''

from aum_common import *
from aum_structs import *

bucket_name = config_data["bucket_name"]
fund = config_data["fund"]
aum_folder = config_data["aum_folder"]
asset_folder = fund["asset_folder"]
forex_id = config_data["forex_id"]
partner_id = config_data["forex_partner_id"]
fund_columns = ["id","account_id","client_id","fund_id","currency","partner_id","user_id","asset_subtype","execute_date","excute_quantity","total_value","total_value_usd","transaction_type","created","updated"]

spark = spark_session_create("gtv_fund_transaction")
'''
get forex price for fund switching
for currency - IDR , price 1.0
for usd - midprice
input- current and previous date
filter - status-approved and transaction type - buy,sell
total value = quantity* unit price and based on currency multiple by usd_to_idr
return daily level fund transaction
'''
def calculation_fund_transaction(execute_date,execute_previous_date):
    fund_transaction_path = fund["fund_transaction"]
    fund_transaction_t1_path  = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,fund_transaction_path["write_fund_transaction_path"],str(execute_date))
    fund_transaction_t1 = spark.read.csv(fund_transaction_t1_path,header=True,inferSchema=True)
    fund_transaction_t1_id = fund_transaction_t1.filter((col("status")=="APPROVED") & (col("transaction_type").isin("BUY","SELL"))).select("id").distinct()
    fund_transaction_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,fund_transaction_path["fund_transaction_t2_path"])
    fund_transaction_t2 = spark.read.csv(fund_transaction_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    fund_transaction_t2_id = fund_transaction_t2.filter((col("status")=="APPROVED") & (col("transaction_type").isin("BUY","SELL"))).select("id").distinct()
    current_day_id = fund_transaction_t1_id.subtract(fund_transaction_t2_id).distinct()
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = fund_transaction_t1.filter(col("id").isin(daily_transaction_id_list))
    daily_gtv_fund_transaction = only_current_day_transaction.withColumn("total_value_usd",(round(col("excute_quantity")*col("unit_price"),8))).withColumn("execute_date",lit(execute_date))
    daily_gtv_fund_transaction = daily_gtv_fund_transaction.withColumn("total_value",when((col("currency")=="IDR"), col("total_value_usd")).when((col("currency")=="USD"), col("total_value_usd")*col("usd_to_idr")))
    daily_gtv_fund_transaction = daily_gtv_fund_transaction.withColumn("asset_subtype",lit("fund_transactions"))
    daily_gtv_fund_transaction = daily_gtv_fund_transaction.withColumn("total_value_usd",when((col("currency")=="USD"), col("total_value_usd")).otherwise(0))
    daily_gtv_fund_transaction = daily_gtv_fund_transaction.groupBy("id","account_id","client_id","fund_id","currency","partner_id","user_id","asset_subtype","execute_date","transaction_type","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")),(round(sum("total_value_usd"),8).alias("total_value_usd")))
    return daily_gtv_fund_transaction


''' 
get currecny of fund_id and price from forex
'''
def fund_swtiching_currency_and_price(fund_swtich_transaction,execute_date,offset):
    fund_transaction_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,fund["fund_transaction"]["fund_transaction_t2_path"])
    fund_transaction_t2 = spark.read.csv(fund_transaction_t2_path + str(execute_date) + "/*",header=True,inferSchema=True)
    fund_currency = fund_transaction_t2.select("fund_id","currency").withColumnRenamed("fund_id","fund_id_dup").distinct()
    snap_fund_transaction_switch = fund_swtich_transaction.join(fund_currency,fund_swtich_transaction["fund_id"]==fund_currency["fund_id_dup"],"left").drop("fund_id_dup")
    forex_price = get_current_forex_price(partner_id,offset)
    forex_price = forex_price.filter(col("forex_id")==forex_id)
    forex_price = forex_price.select(col("mid_price").alias("usd_to_idr"),col("buy_back_price").alias("usd_to_idr_buyback_price"),col("sell_price").alias("usd_to_idr_sell_price"))
    forex_price = forex_price.withColumn("currency_dup",lit("USD")).distinct()
    arr =[("IDR",1.0)]
    columns_name = ["currency_dup","usd_to_idr"]
    idr_df  = spark.createDataFrame(data=arr,schema=columns_name)
    forex_price= forex_price.select(columns_name)
    forex_price = forex_price.union(idr_df)
    
    snap_fund_transaction_switch_currency = snap_fund_transaction_switch.join(forex_price,snap_fund_transaction_switch["currency"]==forex_price["currency_dup"],"left").drop("currency_dup")
    return snap_fund_transaction_switch_currency


'''
input - execution  and previous date
filter - status- APPROVED and transaction type - "SWITCH_IN","SWITCH_OUT"
price - take from forex price 
total_value - quantity and price
return daily fund_switching transaction
'''
def calculation_fund_transaction_switch(execute_date,execute_previous_date,offset):
    fund_transaction_switch_path  = fund["fund_transaction_switch"]
    fund_transaction_switch_t1_path  = "s3a://{}{}{}{}{}/*".format(bucket_name,aum_folder,asset_folder,fund_transaction_switch_path["write_fund_transaction_switch_path"],str(execute_date))
    fund_transaction_switch_t1 = spark.read.csv(fund_transaction_switch_t1_path,header=True,inferSchema=True)
    fund_transaction_switch_t1_id = fund_transaction_switch_t1.filter((col("status")=="APPROVED") & (col("transaction_type").isin("SWITCH_IN","SWITCH_OUT"))).select("id").distinct()
    fund_transaction_switch_t2_path  = "s3a://{}{}{}{}".format(bucket_name,aum_folder,asset_folder,fund_transaction_switch_path["fund_transaction_switch_t2_path"])
    fund_transaction_switch_t2 = spark.read.csv(fund_transaction_switch_t2_path + str(execute_previous_date) + "/*",header=True,inferSchema=True)
    fund_transaction_switch_t2_id = fund_transaction_switch_t2.filter((col("status")=="APPROVED") & (col("transaction_type").isin("SWITCH_IN","SWITCH_OUT"))).select("id").distinct()
    current_day_id = fund_transaction_switch_t1_id.subtract(fund_transaction_switch_t2_id).distinct()
    daily_transaction_id_list =  current_day_id.rdd.flatMap(lambda x:x).collect()
    only_current_day_transaction  = fund_transaction_switch_t1.filter(col("id").isin(daily_transaction_id_list))
    only_current_day_transaction_currency_price = fund_swtiching_currency_and_price(only_current_day_transaction,execute_date,offset)
    daily_gtv_fund_transaction = only_current_day_transaction_currency_price.withColumn("total_value",(round(col("excute_quantity")*col("unit_price")*col("usd_to_idr"),8))).withColumn("execute_date",lit(execute_date))
    daily_gtv_fund_transaction = daily_gtv_fund_transaction.withColumn("total_value_usd",when((col("currency")=="USD"), (round(col("excute_quantity")*col("unit_price"),8))).otherwise(0))
    daily_gtv_fund_transaction = daily_gtv_fund_transaction.withColumn("asset_subtype",lit("fund_switch_transactions"))
    daily_gtv_fund_switch_transaction = daily_gtv_fund_transaction.groupBy("id","account_id","client_id","fund_id","currency","partner_id","user_id","asset_subtype","execute_date","transaction_type","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")),(round(sum("total_value_usd"),8).alias("total_value_usd")))
    return daily_gtv_fund_switch_transaction
'''
input- execute date and previous date
union - fund transaction and fund switching
write gtv fund 
'''
def execute_processing(execute_date,execute_previous_date,offset):
    logging.info("Starting execution for fund aum")
    snap_fund_transaction = calculation_fund_transaction(execute_date,execute_previous_date)
    snap_fund_transaction_switch = calculation_fund_transaction_switch(execute_date,execute_previous_date,offset)
    snap_fund_transaction_switch = snap_fund_transaction_switch.select(fund_columns)
    snap_fund_transaction = snap_fund_transaction.select(fund_columns)
    snap_fund = snap_fund_transaction.union(snap_fund_transaction_switch)
    snap_fund = snap_fund.withColumn("excute_quantity",f.abs("excute_quantity")).withColumn("total_value",f.abs("total_value")).withColumn("total_value_usd",f.abs("total_value_usd"))
    snap_fund = snap_fund.groupBy("id","account_id","client_id","fund_id","currency","partner_id","user_id","asset_subtype","execute_date","transaction_type","created","updated").agg((round(sum("excute_quantity"),8).alias("excute_quantity")),(round(sum("total_value"),2).alias("total_value")),(round(sum("total_value_usd"),8).alias("total_value_usd")))
    snap_fund_with_price = snap_fund.withColumn("asset_type",lit("fund")).withColumnRenamed("fund_id","product_id").withColumn("currency",lit("IDR"))
    snap_fund_with_price = snap_fund_with_price.withColumnRenamed("excute_quantity","quantity").withColumn("is_pocket",lit(False))
    write_snap_snap_fund_with_price_path =   "s3a://{}{}{}/".format(bucket_name,fund["fund_gtv_path"],str(execute_date))
    snap_fund_with_price.coalesce(1).write.mode("overwrite").csv(write_snap_snap_fund_with_price_path,header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    offset = config_data["offset"]
    execute_date = get_date(offset)
    execute_previous_date = get_date(offset+1)
    execute_processing(execute_date,execute_previous_date,offset)
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"gtv_fund_transaction")


