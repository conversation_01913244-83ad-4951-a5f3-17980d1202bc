from common import *
from structs import *
from config import *


spark = spark_session_create("snap_asset")


def read_data(path, format, **kwargs):
    df = None
    if format is None or format == "csv":
        if kwargs.get("schema") is not None:
            logging.info("Reading data using schema")
            df = read_csv_file(path, None, False, kwargs.get("schema"))
        else:
            df = read_csv_file(path, None, False, None)
    elif format == "raw_json":
        df = read_json_data(path, is_raw=True)
    elif format == "json":
        df = read_json_data(path)
    elif format == "parquet":
        df = read_parque_file(path)
    else:
        logging.info("No Valid file format is present")
    return df


def write_data(df, path, format):
    if format is None or format == "csv":
        df.coalesce(1).write.mode('overwrite').csv(path, header=True)
    elif format == "json":
        df.coalesce(1).write.mode('overwrite').json(path)
    elif format == "parquet":
        df.coalesce(1).write.mode('overwrite').parquet(path)
    else:
        logging.info("No Valid file format is present")
    return df


def get_write_path(asset_config, t_1):
    write_path = asset_config.get("write_t_2_files_folder")
    if write_path is None:
        write_path = asset_config.get("read_t_2_files_folder")
    de_dupe_write_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], write_path, t_1)
    return de_dupe_write_path


def custom_transformation(df_t_1, df_t_2, de_dupe_on):
    if df_t_1 is not None:
        if config_data.get("hour") is not None:
            ts = get_date_for_query(config_data["offset"]).replace(hour=config_data["hour"], minute=config_data["min"], second=config_data["sec"], microsecond=config_data["ms"]).strftime("%Y-%m-%dT%H:%M:%S.000Z")
            logging.info("cutoff time is {}".format(ts))
            df_t_1 = df_t_1.withColumn(de_dupe_on, col(de_dupe_on).cast(TimestampType())).filter(col(de_dupe_on) <= ts)
        t_2_cols = df_t_2.columns
        df_t_1 = df_t_1.select(t_2_cols)
        data_types = df_t_2.dtypes
        for data_type in data_types:
            df_t_1 = df_t_1.withColumn(data_type[0], col(data_type[0]).cast(data_type[1]))
        df_union = df_t_2.union(df_t_1)
    else:
        df_union = df_t_2
    return df_union


def start_processing(snapshot_type):
    assets_to_snap = job_config_data["asset_snapshot"][snapshot_type]
    for asset in assets_to_snap:
        logging.info("Starting snapshoting for {}".format(asset))
        offset = config_data["offset"]
        t_1 = get_date(offset)
        t_2 = get_date(offset+1)
        asset_config = job_config_data["asset_snapshot"]["assets"][asset]
        t_2_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], asset_config["read_t_2_files_folder"], t_2)
        t_1_format = asset_config.get("t_1_format")
        t_2_format = asset_config.get("t_2_format")
        if t_1_format is None:
            t_1_format = "raw_json"
        if t_2_format is None:
            t_2_format = "csv"
        logging.info("reading data for t_2_df from {}".format(t_2_path))
        if asset == "crypto_currency_wallet_transfers":
            df_t_2 = read_data(t_2_path, t_2_format, schema=schema_for_crypto_currency_wallet_transfers)
        else:
            df_t_2 = read_data(t_2_path, t_2_format)
        t_1_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], asset_config["t_1_files_folder"], t_1)
        logging.info("reading data for t_1_df from {}".format(t_1_path))
        df_t_1 = read_data(t_1_path, t_1_format)
        de_dupe_key = asset_config.get("de_dupe_key")
        de_dupe_on = asset_config.get("de_dupe_on")
        if de_dupe_key is None:
            de_dupe_key = ["id"]
        if de_dupe_on is None:
            de_dupe_on = "updated"
        df_union = custom_transformation(df_t_1, df_t_2, de_dupe_on)
        logging.info("Started De dupe operation")
        logging.info("de_dupe_key is {} and de_dupe_on is {}".format(de_dupe_key, de_dupe_on))
        df = de_dupe_dataframe(df_union, de_dupe_key, de_dupe_on)
        logging.info("Completed De dupe operation")
        de_dupe_write_path = get_write_path(asset_config, t_1)
        logging.info("writing data in {}".format(de_dupe_write_path))
        write_data(df, de_dupe_write_path, t_2_format)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--snapshot_type", help="snapshot type")
    args = parser.parse_args()
    snapshot_type = "0000JKT"
    if args.snapshot_type:
        snapshot_type = args.snapshot_type
    cutoff_time = job_config_data["cut_off_time"][snapshot_type]
    config_data["offset"] = cutoff_time["offset"]
    config_data["hour"] = cutoff_time["hour"]
    config_data["min"] = cutoff_time["min"]
    config_data["sec"] = cutoff_time["sec"]
    config_data["ms"] = cutoff_time["ms"]

    start_processing(snapshot_type)
    job_name = "snapshot_{}".format(snapshot_type)
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, job_name)