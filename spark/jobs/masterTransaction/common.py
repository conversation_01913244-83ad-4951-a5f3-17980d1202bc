import json
from datetime import date, timedelta, datetime, timezone
from pyspark.sql import SparkSession
from pyspark.sql import functions as f
from pyspark.sql.types import LongType, StringType, TimestampType, StructType, StructField, IntegerType, DoubleType, NullType
from pyspark.sql.functions import row_number, col, isnan, when, count, lit, ceil, floor, date_format, round, udf, \
    from_utc_timestamp, sum, expr
import pytz
from pyspark.sql.window import Window
import json_logger


with open("master_transaction_config.json") as config_file:
    config_data = json.load(config_file)


logging = json_logger.init_non_web_with_environment(config_data["env"])


def get_date(offset):
    zone = pytz.timezone("Asia/Jakarta")
    return (datetime.now(tz=zone) - timedelta(offset)).date()

offset_date = get_date(config_data["offset"])


def get_spark_session(application_name="spark_application"):
    spark = SparkSession.builder.appName(application_name).getOrCreate()
    logging.info("spark session started with master node: {}".format(spark.sparkContext._conf.get("spark.master")))
    spark._jsc.hadoopConfiguration().set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
    return spark


def get_date_for_query(offset):
    zone = pytz.timezone("Asia/Jakarta")
    return datetime.now(tz=zone) - timedelta(offset)


def read_file_as_dataframe(read_directory=False, latest_id=True, **kwargs):
    spark, asset, entity, schema = kwargs['spark_session'], kwargs['asset_name'], kwargs['entity_name'], kwargs['schema_name']

    if read_directory is False:
        file_path = config_data[asset]["raw_bucket"] + config_data[asset]["entities"][entity]["folder"] + "/dt=" + str(offset_date) + "/"
    else:
        file_path = config_data[asset]["raw_bucket"] + config_data[asset]["entities"][entity]["folder"] + "/*/*.json"
    logging.info("Reading file from {0}".format(file_path))

    try:
        df = spark.read.option("recursiveFileLookup", "true").json(file_path)
        df = df.select("value.*").select(schema.fieldNames()).withColumn("updated", f.to_timestamp(col("updated")))
        if latest_id is True:
            window = Window.partitionBy([col(x) for x in config_data[asset]["primary_keys"]]).orderBy(col("updated").desc())
            df = df.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
    except Exception as e:
        logging.info("Not able to read files from s3 path {0} and error is {1}".format(file_path, repr(e) ) )
        emptyRDD = spark.sparkContext.emptyRDD()
        df = spark.createDataFrame(emptyRDD, schema)

    return df

def get_asset_t_0(asset_t_1, asset_t_2, primary_keys, asset_name, column_order):
    try:
        asset_t_1_columns = asset_t_1.columns
        asset_t_2_columns = asset_t_2.columns
        asset_t_1_columns.sort()
        asset_t_2_columns.sort()

        if len(asset_t_1_columns) != len(asset_t_2_columns):
            raise Exception("number of columns in {}_t_1 and {}_t_2 should be same".format(asset_name, asset_name))

        for i in range(len(asset_t_1_columns)):
            if asset_t_1_columns[i] != asset_t_2_columns[i]:
                raise Exception("name of columns in {}_t_1 and {}_t_2 should be same".format(asset_name, asset_name))

        asset_t_0 = asset_t_2.union(asset_t_1)
        asset_t_0 = asset_t_0.orderBy(col("updated").asc())

        window = Window.partitionBy([col(x) for x in primary_keys]).orderBy(col("updated").desc())
        asset_t_0 = asset_t_0.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")

        # set_of_primary_keys = set(primary_keys)
        # asset_t_0 = asset_t_0.groupBy(*set_of_primary_keys).agg(
        #     *[f.last(col).alias(col) for col in set(asset_t_0.columns) - set_of_primary_keys])

        asset_t_0 = asset_t_0.select(column_order)
        logging.info(
            "successfully calculated {}_t_0 with total number of {} rows".format(asset_name, asset_t_0.count()))
        return asset_t_0
    except Exception as e:
        logging.error("An error has occurred while calculating {}_t_0: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def save_asset_t_0_to_s3(asset_t_0, bucket_name, asset_folder_name, t2_files_folder, date_t_1, asset_name):
    try:
        logging.info(
            "Saving t0 files to path s3a://{}/{}/{}/dt={}".format(bucket_name, asset_folder_name, t2_files_folder,
                                                                  date_t_1))
        asset_t_0.coalesce(1).write.mode('overwrite').save(
            "s3a://{}/{}/{}/dt={}".format(bucket_name, asset_folder_name, t2_files_folder, date_t_1), format="csv",
            header=True)
        logging.info("Successfully saved {}_t_0 to {} bucket".format(asset_name, bucket_name))
    except Exception as e:
        logging.error(
            "An error has occurred while saving {}_t_0 to {} bucket: {}".format(asset_name, bucket_name, repr(e)))
        logging.exception(e)
        raise e


def drop_unnecessary_columns(asset, list_of_columns):
    try:
        asset = asset.drop(*list_of_columns)
        logging.info("Successfully dropped {} columns".format(",".join(list_of_columns)))
        return asset
    except Exception as e:
        logging.error("An error has occurred while dropping {} columns: {}".format(",".join(list_of_columns), repr(e)))
        logging.exception(e)
        raise e


def convert_snake_to_camel_case(input_str):
    splitted_str = input_str.split("_")
    return splitted_str[0] + ''.join(word.title() for word in splitted_str[1:])


def write_asset_returns_to_mongo_array_format(asset_returns_t_0, mongo_config, asset_name,write_format,shardkey):
    try:
        current_timestamp = get_date_for_query(config_data["offset"]).date()

        original_columns = asset_returns_t_0.columns

        for col in original_columns:
            input_str = col
            if col == "created":
                input_str = input_str + "_at"

            renamed_col = convert_snake_to_camel_case(input_str)
            asset_returns_t_0 = asset_returns_t_0.withColumnRenamed(col, renamed_col)

        cols = asset_returns_t_0.drop("accountId").columns
        asset_returns_t_0 = asset_returns_t_0.withColumn("returns",f.struct(cols))
        asset_returns_t_0 = asset_returns_t_0.select("accountId","returns")
        asset_returns_t_0 = asset_returns_t_0.groupBy("accountId").agg(f.collect_list("returns").alias("returns"))
        asset_returns_t_0 = asset_returns_t_0.withColumn("createdAt", lit(current_timestamp))
        if write_format == "update":
            asset_returns_t_0.write.format("mongo").option("replaceDocument", "false").option("shardKey", shardkey).option("uri", mongo_config["uri"]).option("batchsize",mongo_config["batch_size"]).\
                mode(mongo_config["mode"]).save()
        else:
            asset_returns_t_0.write.format("mongo").option("uri", mongo_config["uri"]).option("batchsize",mongo_config["batch_size"]). \
                mode(mongo_config["mode"]).save()
        logging.info("Successfully saved {} documents to {} collection".format(asset_returns_t_0.count(),
                                                                               mongo_config["collection"]))
    except Exception as e:
        logging.error("An error has occurred while writing {} returns snap to mongo: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e

def write_asset_returns_to_mongo(asset_returns_t_0, mongo_config, asset_name,write_format,shardkey):
    try:
        current_timestamp = get_date_for_query(config_data["offset"]).date()
        asset_returns_t_0 = asset_returns_t_0.withColumn("created", lit(current_timestamp))

        original_columns = asset_returns_t_0.columns

        for col in original_columns:
            input_str = col
            if col == "created":
                input_str = input_str + "_at"

            renamed_col = convert_snake_to_camel_case(input_str)
            asset_returns_t_0 = asset_returns_t_0.withColumnRenamed(col, renamed_col)

        if write_format == "update":
            asset_returns_t_0.write.format("mongo").option("replaceDocument", "false").option("shardKey", shardkey).option("uri", mongo_config["uri"]).option("batchsize",
                                                                                          mongo_config[
                                                                                              "batch_size"]).mode(mongo_config["mode"]).save()
        else:
            asset_returns_t_0.write.format("mongo").option("uri", mongo_config["uri"]).option("batchsize",mongo_config["batch_size"]). \
                mode(mongo_config["mode"]).save()
        logging.info("Successfully saved {} documents to {} collection".format(asset_returns_t_0.count(),
                                                                               mongo_config["collection"]))
    except Exception as e:
        logging.error("An error has occurred while writing {} returns snap to mongo: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e

def write_portfolio_to_mongo(asset_returns_t_0, mongo_config, asset_name, i,write_format,shardkey):
    try:
        current_timestamp = get_date_for_query(config_data["offset"]).date()
        asset_returns_t_0 = asset_returns_t_0.withColumn("created", lit(current_timestamp))

        original_columns = asset_returns_t_0.columns

        for col in original_columns:
            input_str = col
            if col == "created":
                input_str = input_str + "_at"

            renamed_col = convert_snake_to_camel_case(input_str)
            asset_returns_t_0 = asset_returns_t_0.withColumnRenamed(col, renamed_col)
        if write_format == "update":
            asset_returns_t_0.write.format("mongo").option("replaceDocument", "false").option("shardKey", shardkey).option("uri", mongo_config["partitioned_uri"]+mongo_config["collection"]+"_snapshot_"+str(i)+"?authSource=admin").option("batchsize",mongo_config["batch_size"]).mode(mongo_config["mode"]).save()
        else:
            asset_returns_t_0.write.format("mongo").option("uri", mongo_config["partitioned_uri"]+mongo_config["collection"]+"_snapshot_"+str(i)+"?authSource=admin").option("batchsize",mongo_config["batch_size"]).mode(mongo_config["mode"]).save()
        logging.info("Successfully saved {} documents to {} collection".format(asset_returns_t_0.count(),mongo_config["collection"]+"_snapshot_"+str(i)))
    except Exception as e:
        logging.error("An error has occurred while writing {} returns snap to mongo: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def create_master_table_signature(spark, final_df, asset_name):
    logging.info("Create master table signature for {0}".format(asset_name))
    try:
        final_df.createOrReplaceTempView("final_df")
        df = spark.sql("""
            SELECT
            created,
            user_id,
            account_id,
            asset_type,
            product,
            product_id,
            asset_subtype,
            CASE
                WHEN partner_id = 1000002 AND client_id = 3 THEN "Pluang"
                WHEN partner_id = 1000002 AND client_id = 21 THEN "Paham"
                WHEN partner_id is NULL AND client_id = 21 THEN "Paham"
                WHEN asset_type = 'idss' AND date(created) = '2022-02-25' THEN "Paham"
                WHEN partner_id = 1000002 AND client_id = 6 THEN "Gojek"
                WHEN partner_id = 1000003 AND client_id = 3 THEN "Dana"
                WHEN partner_id = 1000005 AND client_id = 3 THEN "Tokopedia"
                WHEN partner_id = 92 AND client_id = 3 THEN 'Indoalliz'
                WHEN partner_id = 1000001 AND client_id = 3 THEN "Bukalapak"
                WHEN partner_id = 1000004 AND client_id = 3 THEN "KPBI"
                ELSE "Other B2B"
            END AS company,
            status,
            transaction_type,
            partner_id,
            client_id,
            currency,
            ref_id,
            ref_id_hedge,    
            quantity,  
            net_quantity,
            unit_price,
            unit_price_usd,
            gtv,
            gtv_usd,
            net_gtv,
            ref_table
             FROM final_df
            """)
        df = df.withColumn("created", from_utc_timestamp("created", "Asia/Jakarta"))
        df = df.select([
            lit(None).cast('string').alias(i.name)
            if isinstance(i.dataType, NullType)
            else i.name
            for i in df.schema
        ])
        return df
    except Exception as e:
        logging.error("Unable to Read and transformed Global Stock Inventory Orders :  {}".format(repr(e)))
        logging.exception(e)
        raise e


def save_calculated_asset_returns_to_s3(asset_returns_t_0, bucket_name, asset_folder_name, date_t_1, asset_name):
    try:
        current_timestamp = get_date_for_query(0)
        asset_returns_t_0 = asset_returns_t_0.withColumn("load_date", lit(current_timestamp))

        asset_returns_t_0.coalesce(1).write.mode('overwrite').save(
            "s3a://{}/{}/dt={}".format(bucket_name, asset_folder_name,date_t_1), header=True, format="csv")
            #"/Users/<USER>/PycharmProjects/pluang-spark-jobs/spark/jobs/masterTransaction/gtv/crypto", header=True, format="csv")
        logging.info(
            "Successfully saved csv file with {} rows to {} bucket".format(asset_returns_t_0.count(), bucket_name))
    except Exception as e:
        logging.error(
            "An error has occurred while saving {} returns snap to {} bucket: {}".format(asset_name, bucket_name,
                                                                                         repr(e)))
        logging.exception(e)
        raise e


def save_calculated_asset_returns_t_0_to_s3_split_by_column(asset_returns_t_0, bucket_name, asset_folder_name, snapshot_folder,
                                                            date_t_1, asset_name,column):
    try:

        current_timestamp = get_date_for_query(config_data["offset"]-1)
        asset_returns_t_0 = asset_returns_t_0.withColumn("created", lit(current_timestamp))
        #bucket_name = "portfolio-snapshots/snapshot-test-data"
        asset_returns_t_0.coalesce(1).write.mode('overwrite').partitionBy(column).save(
            "s3a://{}/{}/{}/dt={}".format(bucket_name, asset_folder_name, snapshot_folder, date_t_1), format="csv",
            header=True)
        logging.info(
            "Successfully saved csv file with {} rows to {} bucket".format(asset_returns_t_0.count(), bucket_name))
    except Exception as e:
        logging.error(
            "An error has occurred while saving {} returns snap to {} bucket: {}".format(asset_name, bucket_name,
                                                                                         repr(e)))
        logging.exception(e)
        raise e


def custom_exception(**kwargs):
    logging.error("Unable to transformed asset : {0}, entity: {1}, message : {2}".format(kwargs['asset_name'], kwargs['entity_name'], repr(kwargs['exception'])))
    logging.exception(kwargs['exception'])
    raise kwargs['exception']