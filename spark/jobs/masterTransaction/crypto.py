from pyspark.sql.functions import row_number, col, isnan, when, count, lit, ceil, floor, date_format, round, udf, \
from_utc_timestamp, sum, expr, to_timestamp, datediff, expr, get_json_object, from_json, to_json, to_date, date_add, sum, concat
from pyspark.sql import SparkSession
import pytz
from pyspark.sql.types import TimestampType, DoubleType, ArrayType, DateType, StringType, LongType, StructType, \
    StructField, NullType
from datetime import date, timedelta, datetime, timezone
from common import *
from structs import *

#It will create a spark session in common.py file
spark =  get_spark_session("master_transaction_crypto_gtv")
asset = "crypto"


def read_and_transform_crypto_currency_transactions():
    logging.info("Reading and  Transforming Crypto Transaction Data")
    entity = "crypto_currency_transactions"
    try:
        crypto_transactions = read_file_as_dataframe(spark_session=spark, asset_name=asset, entity_name=entity,  schema_name=schema_for_crypto_currency_transactions)
        crypto_transactions = crypto_transactions.withColumn("effectiveSpread", get_json_object(col("price_info"), "$.effectiveSpread"))
        crypto_transactions = crypto_transactions.withColumn("effectiveSpread", col("effectiveSpread").cast('float'))
        crypto_transactions.createOrReplaceTempView("crypto_transactions")

        crypto_transactions = spark.sql("""
        SELECT user_id,
        account_id,
        'crypto' AS asset_type, 
        a.crypto_currency_id AS product_id, 
        'crypto' AS asset_subtype, 
        quantity, 
        executed_quantity,
        CASE WHEN status in ('SUCCESS', 'PARTIALLY_FILLED') THEN CASE WHEN transaction_type in ('BUY','AIRDROP') then quantity ELSE -quantity END ELSE NULL END AS net_quantity, 
        a.created, 
        a.updated,
        cast(null as TIMESTAMP) as effective_date,
        a.status, transaction_type, 
        partner_id, 
        client_id, 
        'IDR' AS currency, 
        unit_price, 
        executed_unit_price,
        cast(null as string) AS unit_price_usd, 
        total_price, 
        executed_total_price, 
        a.id AS ref_id,
        'crypto_currency_transactions' as ref_table
        FROM crypto_transactions as a
        """)
        return crypto_transactions
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e )


# Read crypto_mission_rewards files and transform the data
def read_and_transform_crypto_mission_rewards():
    logging.info("Reading and  Transforming Crypto Currency Mission Rewards Data")
    entity = "crypto_mission_rewards"
    try:
        crypto_mission_rewards = read_file_as_dataframe(spark_session=spark, asset_name=asset, entity_name=entity,
                                                     schema_name=schema_for_crypto_mission_rewards)
        crypto_mission_rewards.createOrReplaceTempView("crypto_mission_rewards")
        crypto_mission_rewards = spark.sql("""
                SELECT user_id,
                account_id,
                'crypto' AS asset_type, 
                a.crypto_currency_id AS product_id, 
                'crypto' AS asset_subtype, 
                quantity,
                cast(null as float) as executed_quantity, 
                quantity AS net_quantity, 
                a.created, 
                a.updated,
                cast(null as TIMESTAMP) as effective_date,
                a.status, 
                'BUY' AS transaction_type, 
                partner_id, 
                client_id, 
                'IDR' AS currency, 
                unit_price, 
                cast(null as float) AS executed_unit_price, 
                cast(null as string) AS unit_price_usd, 
                total_price, 
                cast(null as DECIMAL(38,8) ) AS executed_total_price,
                a.id AS ref_id,
                 'crypto_currency_mission_rewards' AS ref_table 
                FROM crypto_mission_rewards as a 
                WHERE status in ('CLAIMED', 'UNLOCKED')
                """)
        return crypto_mission_rewards
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e )


# Read crypto_currency_pluangcuan_yields files and transform the data
def read_and_transform_crypto_currency_pluangcuan_yields():
    logging.info("Reading and Transforming Crypto Pluang Cuan Data")
    entity = "crypto_currency_pluangcuan_yields"
    try:
        crypto_currency_pluangcuan_yields = read_file_as_dataframe(spark_session=spark, asset_name=asset, entity_name=entity,
                                                        schema_name=schema_for_crypto_currency_pluangcuan_yields)

        crypto_currency_pluangcuan_yields = crypto_currency_pluangcuan_yields.createOrReplaceTempView("crypto_currency_pluangcuan_yields")
        crypto_currency_pluangcuan_yields = spark.sql("""
                        SELECT user_id,account_id,'crypto' AS asset_type, 
                        a.crypto_currency_id AS product_id, 
                        'crypto' AS asset_subtype, 
                        yield_quantity as quantity, 
                        cast(null as float) executed_quantity,
                        yield_quantity AS net_quantity, 
                        a.created, 
                        a.updated,
                        cast(a.effective_date as INT) as effective_date,
                        a.status, 
                        'BUY' AS transaction_type, 
                        partner_id, client_id, 
                        cast(null as string) AS currency, 
                        unit_price, 
                        cast(null as float) AS executed_unit_price, 
                        cast(null as string) AS unit_price_usd, 
                        cast(null as DECIMAL(38,8) ) AS total_price, 
                        cast(null as DECIMAL(38,8) ) AS executed_total_price,  
                        a.id AS ref_id, 
                        'crypto_currency_pluangcuan_yields' AS ref_table, 
                        "1970-01-01" as epoch_date_string
                        FROM  crypto_currency_pluangcuan_yields as a 
                        WHERE status = 'COMPLETED'
                        """)
        crypto_currency_pluangcuan_yields = crypto_currency_pluangcuan_yields.withColumn("effective_date", expr("date_add(to_date(epoch_date_string, 'yyyy-mm-dd' ), effective_date)")).drop("epoch_date_string").withColumn('effective_date',concat(date_format('effective_date',"yyyy-MM-dd"), lit('T00:00:00Z')))
        return crypto_currency_pluangcuan_yields
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e )


def create_final_dataframe_for_writestream(union_transaction_mission_cuan):
    logging.info("Create final data for write stream at line 136")
    entity = "Union all crypto entities"
    try:
        crypto_currencies = read_file_as_dataframe(True, spark_session=spark, asset_name=asset,
                                                   entity_name="crypto_currencies",
                                                   schema_name=schema_for_crypto_currencies)
        crypto_currencies = crypto_currencies.select("id", "symbol")
        union_transaction_mission_cuan_join_currencies = union_transaction_mission_cuan.join(crypto_currencies,
                                                                                                 union_transaction_mission_cuan.product_id == crypto_currencies.id,
                                                                                                 "left")
        union_transaction_mission_cuan_join_currencies.createOrReplaceTempView("union_transaction_mission_cuan_join_currencies")
        df = spark.sql("""select INT(user_id) user_id, 
        INT(account_id) account_id, 
        asset_type, symbol as product, 
        INT(product_id) as product_id, 
        asset_subtype, 
        cast(quantity as FLOAT) as quantity,
        cast(executed_quantity as FLOAT ) as executed_quantity,
        cast(net_quantity as FLOAT ) as net_quantity, 
        created, 
        updated,
        effective_date,
        status, 
        transaction_type, 
        INT(partner_id) as partner_id, 
        INT(client_id) as client_id, 
        currency, 
        cast(unit_price as FLOAT ) as unit_price,
        cast(executed_unit_price as FLOAT ) as executed_unit_price,
        STRING(unit_price_usd) as unit_price_usd, 
        cast(total_price as DECIMAL(38,8) ) as total_price, 
        cast(executed_total_price as DECIMAL(38,8) ) as executed_total_price,  
        INT(ref_id) as ref_id, 
        ref_table 
          from union_transaction_mission_cuan_join_currencies""")
        column_order = ["created", "updated", "effective_date", "user_id", "account_id", "asset_type", "product",
                        "product_id",
                        "asset_subtype", "status", "transaction_type", "partner_id", "client_id", "currency", "ref_id",
                        "quantity", "executed_quantity", "net_quantity", "unit_price", "executed_unit_price",
                        "unit_price_usd", "total_price", "executed_total_price", "ref_table"]

        df = df.withColumn("created", from_utc_timestamp("created", "Asia/Jakarta")) \
            .withColumn("updated", from_utc_timestamp("updated", "Asia/Jakarta")) \
            .withColumn("date_paid_on", lit(None).cast(TimestampType())) \
            .withColumn("effective_date", from_utc_timestamp("effective_date", "Asia/Jakarta")) \
            .select(config_data['column_order'])
        return df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e )


if __name__ == "__main__":
    logging.info("Starting execution for Crypto Asset Data for date {0}".format(str(offset_date)))
    crypto_currency_transactions = read_and_transform_crypto_currency_transactions()
    crypto_mission_rewards = read_and_transform_crypto_mission_rewards()
    crypto_currency_pluangcuan_yields = read_and_transform_crypto_currency_pluangcuan_yields()
    logging.info("Union crypto_currency_transactions, crypto_mission_rewards and  crypto_currency_pluangcuan_yields")
    union_transaction_mission_cuan = crypto_currency_transactions.union(crypto_mission_rewards).union(crypto_currency_pluangcuan_yields)
    final_df  = create_final_dataframe_for_writestream(union_transaction_mission_cuan)
    final_df.printSchema()
    save_calculated_asset_returns_to_s3(final_df,
                                        config_data[asset]["gtv"]["bucket"],
                                        config_data[asset]["gtv"]["asset_folder"],
                                        get_date(config_data["offset"]),
                                        config_data[asset]["asset_name"])



