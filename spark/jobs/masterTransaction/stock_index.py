from pyspark.sql.functions import row_number, col, isnan, when, count, lit, ceil, floor, date_format, round, udf, \
from_utc_timestamp, sum, expr, to_timestamp, datediff, expr, get_json_object
from pyspark.sql import SparkSession
import pytz
from pyspark.sql.types import TimestampType, DoubleType, ArrayType, DateType, StringType, LongType, StructType, \
    StructField
from pyspark.sql.window import Window
from datetime import date, timedelta, datetime, timezone
from common import *
from structs import *

#It will create a spark session in common.py file
spark = get_spark_session("master_transaction_indo_stock_gtv")
asset = "stock_index"


def read_and_transform_stock_index_transactions():
    logging.info("Reading and Transforming Stock Index Transaction Data")
    entity = "stock_index_transactions"
    try:
        stock_index_transactions_df = read_file_as_dataframe(spark_session=spark, asset_name=asset,
                                                        entity_name="stock_index_transactions",
                                                        schema_name=schema_for_stock_index_transactions)
        stock_index_transactions_df = stock_index_transactions_df.withColumn("executed_unit_price", lit(None).cast(DoubleType()))
        stock_index_transactions_df.createOrReplaceTempView("stock_index_transactions")
        df = spark.sql("""
            SELECT
                sit.user_id, 
                sit.account_id, 
                'stock_index' AS asset_type,
                CASE
                    WHEN sit.stock_index_id = 10000 THEN 'S&P'
                    WHEN sit.stock_index_id = 10001 THEN 'NASDAQ' 
                END AS product,
                sit.stock_index_id AS product_id,
                'stock_index' AS asset_subtype,
                'transaction' AS activity,
                sit.quantity,
                cast(null as float) as executed_quantity,
                CASE 
                    WHEN status = 'SUCCESS' THEN CASE
                        WHEN transaction_type = 'BUY' then sit.quantity
                        ELSE -sit.quantity
                    END
                    ELSE cast(null as string)
                END AS net_quantity,
                sit.created, 
                sit.updated, 
                sit.status, 
                sit.transaction_type, 
                sit.partner_id, 
                sit.client_id, 
                sit.currency, 
                sit.unit_price * currency_to_idr as unit_price,
                cast(null as float) as executed_unit_price,
                sit.unit_price as unit_price_usd,
                sit.total_price as total_price, 
                sit.total_price_idr as executed_total_price,
                id as ref_id,
                'stock_index_transactions' as ref_table
                FROM stock_index_transactions sit
        """)
        return df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def create_final_dataframe_for_writestream(stock_index_transaction_df):
    stock_index_transaction_df.createOrReplaceTempView("stock_index")
    df = spark.sql("""select 
        INT(user_id) user_id, 
        INT(account_id) account_id, 
        asset_type, 
        product, 
        INT(product_id) as product_id, 
        asset_subtype, 
        cast(quantity as float ) as quantity,
        cast(executed_quantity as float ) as executed_quantity,
        cast(net_quantity as float) as net_quantity, 
        created, 
        updated, 
        status, 
        transaction_type, 
        INT(partner_id) as partner_id, 
        INT(client_id) as client_id, 
        currency, 
        cast(unit_price as float ) as unit_price,
        cast(executed_unit_price as float ) as executed_unit_price,
        STRING(unit_price_usd) as unit_price_usd, 
        cast(total_price as DECIMAL(38,8)) as total_price, 
        cast(executed_total_price as decimal(38,8) ) as executed_total_price, 
        INT(ref_id) as ref_id, 
        ref_table from stock_index""")

    df = df.withColumn("created", from_utc_timestamp("created", "Asia/Jakarta")) \
        .withColumn("updated", from_utc_timestamp("updated", "Asia/Jakarta")) \
        .withColumn("date_paid_on", lit(None).cast(TimestampType())) \
        .withColumn("effective_date", lit(None).cast(TimestampType())) \
        .select(config_data['column_order'])
    return df


if __name__ == "__main__":
    logging.info("Starting execution for Stock Index Asset Data")
    stock_index_transaction_df      = read_and_transform_stock_index_transactions()
    final_df = create_final_dataframe_for_writestream(stock_index_transaction_df)
    final_df.printSchema()
    save_calculated_asset_returns_to_s3(final_df,
                                        config_data[asset]["gtv"]["bucket"],
                                        config_data[asset]["gtv"]["asset_folder"],
                                        get_date(config_data["offset"]),
                                        config_data[asset]["asset_name"])