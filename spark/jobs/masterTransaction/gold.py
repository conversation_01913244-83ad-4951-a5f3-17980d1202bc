from pyspark.sql.functions import row_number, col, isnan, when, count, lit, ceil, floor, date_format, round, udf, \
from_utc_timestamp, sum, expr, to_timestamp, datediff, expr, get_json_object, coalesce, concat, lower, explode
from pyspark.sql import SparkSession
import pytz
from pyspark.sql.types import TimestampType, DoubleType, ArrayType, DateType, StringType, LongType, StructType, \
    StructField
from pyspark.sql.window import Window
from datetime import date, timedelta, datetime, timezone
from common import *
from structs import *

#It will create a spark session in common.py file
spark = get_spark_session("master_transaction_indo_stock_gtv")
asset = "gold"


def  calculate_gold_spreads():
    logging.info("Calculate gold spreads Using Master Gold Prices and Partner Gold Prices")
    entity = "Calculate gold spreads"
    try:

        master_gold_prices_df = read_file_as_dataframe(False, False, spark_session=spark, asset_name=asset,
                                                        entity_name="master_gold_prices",
                                                        schema_name=schema_for_master_gold_prices)

        partner_gold_prices_df = read_file_as_dataframe(False, False, spark_session=spark, asset_name=asset,
                                                        entity_name="partner_gold_prices",
                                                        schema_name=schema_for_partner_gold_prices)

        partner_gold_prices_df = partner_gold_prices_df.select("created", "partner_id", "buy_back_price", "sell_price",
                                                               "installment", "master_gold_price_id")
        master_gold_prices_df = master_gold_prices_df.select("id", "created", "buy_back_price", "sell_price",
                                                             "installment")

        partner_gold_prices_df.createOrReplaceTempView("partner_gold_prices")
        master_gold_prices_df.createOrReplaceTempView("master_gold_prices")

        # master_gold_prices_df.join(partner_gold_prices_df, master_gold_prices_df.id == partner_gold_prices_df.master_gold_price_id, "left").show(10000, False)
        # exit(0)

        gold_spreads_df = spark.sql("""
         SELECT *
            FROM (
                SELECT
                    pgp.created,
                    LEAD(pgp.created) OVER (
                        PARTITION BY partner_id
                        ORDER BY
                            pgp.created ASC
                    ) next_created,
                    pgp.partner_id,
                    pgp.buy_back_price,
                    pgp.sell_price,
                    pgp.installment,
                    CAST( (pgp.sell_price-pgp.buy_back_price) AS DECIMAL(38,8) ) / CAST(pgp.sell_price AS DECIMAL(38,8) ) spread,
                    (mgp.buy_back_price + mgp.sell_price) / 2 AS mgp_mid_price,
                    (pgp.buy_back_price + pgp.sell_price) / 2 AS pgp_mid_price,
                    (mgp.buy_back_price + mgp.sell_price) / 2
                        * (1 - cast((pgp.sell_price-pgp.buy_back_price) AS DECIMAL(38,8)) / CAST(pgp.sell_price AS DECIMAL(38,8))) buy_back_price,
                    (mgp.buy_back_price + mgp.sell_price) / 2
                        * (1 + CAST((pgp.sell_price-pgp.buy_back_price) AS DECIMAL(38,8)) / CAST(pgp.sell_price AS DECIMAL(38,8))) sell_price,
                    (CAST((mgp.buy_back_price + mgp.sell_price) AS DECIMAL(38,8)) / 2 - pgp.buy_back_price) / pgp.sell_price AS buy_spread,
                    (CAST(pgp.sell_price-(mgp.buy_back_price + mgp.sell_price) / 2 AS DECIMAL(38,8))) / pgp.sell_price AS sell_spread,
                    ROW_NUMBER() OVER (
                        PARTITION BY pgp.created,
                        pgp.partner_id
                        ORDER BY
                            pgp.created ASC
                    ) RN
                FROM
                    `partner_gold_prices` pgp
                    JOIN `master_gold_prices` mgp
                    ON pgp.master_gold_price_id = mgp.id
            )
            WHERE RN = 1
        """)
        # gold_spreads_df.printSchema()
        # gold_spreads_df.show(1000, truncate=False)
        # exit(0)
        return gold_spreads_df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def  calculate_gold_transcation_using_gold_spread( gold_spreads_df):
    logging.info("Calculate gold tranasction using gold spreads")
    entity = "Calculate gold tranasction using gold spreads"
    try:
        gold_transactions_df = read_file_as_dataframe(spark_session=spark, asset_name=asset,
                                                        entity_name="gold_transactions",
                                                        schema_name=schema_for_gold_transactions)
        gold_transactions_df.createOrReplaceTempView("gold_transactions")
        gold_spreads_df.createOrReplaceTempView("gold_spreads")

        gold_transactions_join_using_spreads = spark.sql("""
        SELECT
                gt.user_id,
                gt.account_id,
                'gold' AS asset_type, 
                'gold' AS product,
                 cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'transaction' AS activity,
                gt.quantity,
                cast(null as float) AS executed_quantity,
                CASE
                    WHEN status = 'SUCCESS' THEN CASE
                        WHEN transaction_type = 'BUY' then gt.quantity
                        ELSE -gt.quantity
                    END
                    ELSE cast(null as float)
                END AS net_quantity,
                gt.created,
                gt.updated,
                cast(null as TIMESTAMP) date_paid_on,
                gt.status,
                gt.transaction_type, 
                gt.partner_id, 
                gt.client_id,
                'IDR' AS currency, 
                unit_price, 
                cast(null as float) AS executed_unit_price,
               cast(null as float) AS unit_price_usd,
                gt.final_amount as total_price, 
                cast(null as decimal(38, 8) ) AS executed_total_price,
                gt.id AS ref_id,
                'gold_transactions' as ref_table
            FROM `gold_transactions`gt
            LEFT JOIN gold_spreads
                ON gt.created > gold_spreads.created AND
                gt.created <= gold_spreads.next_created AND
                gt.partner_id = gold_spreads.partner_id
        """)
        return gold_transactions_join_using_spreads
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def read_transformed_gold_withdrawals():
    logging.info("Read Gold Withdrawls from S3")
    entity = "gold_withdrawals"
    try:
        gold_withdrawals_df = read_file_as_dataframe(spark_session=spark, asset_name=asset,
                                                      entity_name=entity,
                                                      schema_name=schema_for_gold_withdrawals)

        gold_withdrawals_df.createOrReplaceTempView("gold_withdrawals")
        gold_withdrawals_all = spark.sql("""
            SELECT
                user_id,account_id,
                'gold' AS asset_type, 
                'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'withdrawal_request' AS activity, 
                net_amount AS quantity,
                cast(null as float) as executed_quantity,
                -net_amount AS net_quantity,
                created, 
                updated,
                cast(null as timestamp) AS date_paid_on,
                status,
                'withdrawal' AS transaction_type, 
                1000002 AS partner_id, 
                client_id,
                'IDR' AS currency, 
                coalesce(unit_price, buy_price) as unit_price,
                cast(null as float) AS executed_unit_price, 
                cast(null as string) as unit_price_usd,
                cast(null as decimal(38,8) ) AS total_price, 
                cast(null as decimal(38,8) ) AS executed_total_price,
                id AS ref_id,
                'gold_withdrawals' as ref_table
                FROM `gold_withdrawals`
        """)

        gold_withdrawals_expired_rejected = spark.sql("""
            SELECT
                user_id,account_id,
                'gold' AS asset_type, 
                'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'withdrawal_cancel' AS activity, 
                net_amount AS quantity,
                cast(null as float) as executed_quantity,
                net_amount AS net_quantity,
                created, 
                updated, 
                cast(null as timestamp) AS date_paid_on,
                status,
                'withdrawal' AS transaction_type, 
                1000002 AS partner_id, 
                client_id,
                'IDR' AS currency, 
                coalesce(unit_price, buy_price) as unit_price, 
                cast(null as float) AS executed_unit_price, 
                cast(null as string) as unit_price_usd,
                cast(null as decimal(38,8) ) AS total_price, 
                cast(null as decimal(38,8) ) AS executed_total_price,
                id AS ref_id,
                'gold_withdrawals' as ref_table
                FROM `gold_withdrawals`
                WHERE status in ('EXPIRED', 'REJECTED')
        """)
        df = gold_withdrawals_all.union(gold_withdrawals_expired_rejected)
        return df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def  calculate_gold_installment_using_loans_spreads(gold_spreads_df, gold_loans_df):
    logging.info("Calculate Gold Installments Using Gold Loans and Spreads")
    entity = "Gold Installment and Gold Loans"
    try:
        gold_installment_payments_df = read_file_as_dataframe(spark_session=spark, asset_name=asset,
                                                      entity_name="installment_payments",
                                                      schema_name=schema_for_gold_installment_payments)

        gold_installment_payments_df.createOrReplaceTempView("installment_payments")
        gold_loans_df.createOrReplaceTempView("gold_loans")
        gold_spreads_df.createOrReplaceTempView("gold_spreads")

        gold_installment_using_loans_spreads_df = spark.sql("""
        SELECT
            b.user_id,
            b.account_id,
            b.gold_loan_amount,
            a.date_paid_on AS loan_start, 
            b.status,
            'BUY' AS transaction_type, 
            b.partner_id as gold_loan_partner_id, 
            b.client_id, 
            b.issued_price,
            b.down_payment,
            b.id,
            b.tenure,
            b.total_principal,
            b.total_installment,
            a.installment,
            gold_spreads.* ,
            --except (rn, partner_id),
            a.installment_index AS installment_rn,
            a.created as installment_created,
            b.updated
        FROM `installment_payments` a
        LEFT JOIN `gold_loans` b
        ON a.gold_loan_id = b.id
        LEFT JOIN gold_spreads
        ON b.created > gold_spreads.created AND
        b.created <= gold_spreads.next_created AND
        b.partner_id = gold_spreads.partner_id
        """)
        gold_installment_using_loans_spreads_df = gold_installment_using_loans_spreads_df.drop("rn", "partner_id")
        gold_installment_using_loans_spreads_df.createOrReplaceTempView("gold_installment_using_loans_spreads")
        gold_loan_installment_spreads = spark.sql("""
        SELECT
                user_id,account_id,
                'gold' AS asset_type,
                'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                CASE
                    WHEN installment_rn = 1 THEN 'loan_firstinstallment'
                    WHEN installment_rn > 1 THEN 'loan_installment'
                    WHEN installment_rn is NULL and status = 'rejected' THEN 'loan_reject'
                    WHEN status = 'expired' THEN 'loan_expire'
                    ELSE concat('loan_pending_', status)
                END AS activity,
                CASE
                    WHEN installment_rn = 1 THEN gold_loan_amount
                    ELSE cast(null as float)
                END AS quantity,
                cast(null as float) as executed_quantity,
                CASE
                    WHEN status IN ('PAID_OFF','ACCEPTED','CANCELLED') THEN CASE
                        WHEN installment_rn = 1 THEN gold_loan_amount
                        ELSE cast(null as float)
                    END
                    ELSE cast(null as float)
                END AS net_quantity,      
                installment_created AS created,  
                updated,
                loan_start as date_paid_on,
                status,
                'BUY' AS transaction_type, 
                gold_loan_partner_id, 
                client_id,
                'IDR' AS currency, 
                issued_price AS unit_price, 
                cast(null as float) AS executed_unit_price,
                cast(null as string) AS unit_price_usd,
                down_payment + total_principal as total_price,
                cast(null as decimal(38,8) ) as executed_total_price,
                id AS ref_id,
                'installment_payments' as ref_table
                from gold_installment_using_loans_spreads
        """)
        return gold_loan_installment_spreads
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def  gold_loans_transformed(gold_loans_df, gold_spreads_df):
    logging.info("Transformed Gold Loans")
    entity = "gold loans transformed"
    try:
        gold_loans_df.createOrReplaceTempView("gold_loans")
        gold_spreads_df.createOrReplaceTempView("gold_spreads")

        gold_loans_all = spark.sql("""
        SELECT
                user_id,
                account_id,
                'gold' AS asset_type, 
                'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'loan_apply' AS activity, 
                b.gold_loan_amount AS quantity,
                cast(null as float) as executed_quantity,
                -b.gold_loan_amount as net_quantity,
                created, 
                updated,
                cast(null as timestamp) AS date_paid_on,
                cast(null as string) as status, 
                'BUY' AS transaction_type, 
                partner_id, 
                client_id,
                'IDR' AS currency, 
                issued_price AS unit_price,
                cast(null as float) AS executed_unit_price, 
                cast(null as string) AS unit_price_usd,
                (down_payment + total_principal) as total_price,
                cast(null as decimal(38,8) ) as executed_total_price,
                id as ref_id, 
                'gold_loans' as ref_table
            FROM `gold_loans` b
        """)

        gold_loans_cancelled = spark.sql("""
        SELECT
                user_id,
                account_id,
                'gold' AS asset_type, 
                'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'loan_cancel' AS activity, 
                b.gold_loan_amount AS quantity,
                cast(null as float) AS executed_quantity,
                -b.gold_loan_amount as net_quantity,
                b.created,
                b.updated, 
                cast(null as timestamp) AS date_paid_on,
                status, 
                'SELL' AS transaction_type, 
                b.partner_id, 
                b.client_id,
                'IDR' AS currency, 
                issued_price AS unit_price,
                cast(null as float) AS executed_unit_price, 
                cast(null as string) AS unit_price_usd,
                total_principal + down_payment as total_price, 
                cast(null as decimal(38,8) ) AS executed_total_price,
                id AS ref_id,
                'gold_loans' as ref_table
            FROM `gold_loans` b
            LEFT JOIN gold_spreads
                ON b.created > gold_spreads.created AND
                b.created <= gold_spreads.next_created AND
                b.partner_id = gold_spreads.partner_id
            WHERE b.status = 'CANCELLED'
        """)
        df = gold_loans_all.union(gold_loans_cancelled)
        return df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def gold_gift_transactions_transformed():
    logging.info("Transformed Gold Gift Transactions")
    entity = "gold_gift_transactions"
    try:
        gold_gift_transactions_df = read_file_as_dataframe(spark_session=spark, asset_name=asset,
                                                              entity_name="gold_gift_transactions",
                                                              schema_name=schema_for_gold_gift_transactions)
        gold_gift_transactions_df.createOrReplaceTempView("gold_gift_transactions")

        gold_gift_transactions_all = \
            spark.sql("""
        SELECT
                user_id,account_id,'gold' AS asset_type, 'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'gold_gift_request' AS activity,
                quantity as quantity,
                cast(null as float) as executed_quantity,
                CASE
                    WHEN transaction_type = 'RECEIVE' then quantity
                    ELSE -quantity
                END AS net_quantity,
                created,
                updated,
                cast(null as timestamp) AS date_paid_on,
                status,
                transaction_type, 
                1000002 AS partner_id, 
                3 AS client_id,
                'IDR' AS currency, 
                unit_price, 
                cast(null as float) as executed_unit_price,
                cast(null as string) AS unit_price_usd,
                final_amount AS total_price, 
                cast(null as decimal(38,8) ) as executed_total_price,
                id AS ref_id,
                'gold_gift_transactions' as ref_table
            FROM `gold_gift_transactions`
        """)

        gold_gift_transactions_cancelled = \
            spark.sql("""
        SELECT
                user_id,account_id,
                'gold' AS asset_type, 
                'gold' AS product,
                cast(null as integer) AS product_id,
                'gold' AS asset_subtype,
                'gold_gift_cancel' AS activity,
                quantity as quantity,
                cast(null as float) as executed_quantity,
                CASE
                    WHEN transaction_type = 'RECEIVE' then -quantity
                    ELSE quantity
                END AS net_quantity,
                created, 
                updated,
                cast(null as timestamp) AS date_paid_on,
                status,
                transaction_type, 
                1000002 AS partner_id, 
                3 AS client_id,
                'IDR' AS currency, 
                unit_price,
                cast(null as float) as executed_unit_price, 
                cast(null as string) AS unit_price_usd,
                final_amount AS total_price,
                cast(null as decimal(38,8) ) AS executed_total_price, 
                id AS ref_id,
                'gold_gift_transactions' as ref_table
            FROM `gold_gift_transactions` WHERE status in ('CANCELLED', 'EXPIRED', 'REJECTED')
        """)
        df = gold_gift_transactions_all.union(gold_gift_transactions_cancelled)
        return df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)

def create_final_dataframe_for_writestream(final_gold_df):
    logging.info("Create final dataframe for writestream at line 461")
    final_gold_df.createOrReplaceTempView("gold")
    df = spark.sql("""select INT(user_id) user_id, INT(account_id) account_id, asset_type, product, 
    INT(product_id) as product_id, 
    asset_subtype, 
            cast(quantity as float ) as quantity, 
            cast(executed_quantity as float ) as executed_quantity,
                    cast(net_quantity as float) as net_quantity, 
                    created, 
                    updated,
                    date_paid_on,
                    status, 
                    transaction_type, 
                    INT(partner_id) as partner_id, 
                    INT(client_id) as client_id, currency, 
                    cast(unit_price as float) as unit_price,
                    cast(executed_unit_price as float) as executed_unit_price,
                    STRING(unit_price_usd) as unit_price_usd, 
                    cast(total_price as DECIMAL(38,8)) as total_price, 
                    cast(executed_total_price as DECIMAL(38,8)) as executed_total_price, 
                     INT(ref_id) as ref_id, ref_table from gold""")

    df = df.withColumn("created", from_utc_timestamp("created", "Asia/Jakarta")) \
        .withColumn("updated", from_utc_timestamp("updated", "Asia/Jakarta")) \
        .withColumn("date_paid_on", from_utc_timestamp("date_paid_on", "Asia/Jakarta")) \
        .withColumn("effective_date", lit(None).cast(TimestampType())) \
        .select(config_data['column_order'])
    return df

if __name__ == "__main__":
    logging.info("Starting execution for Gold Assets")
    #Calculate Gold Spreads from Partner and Master Gold Prices
    gold_spreads_df = calculate_gold_spreads()
    #Calculate Gold Transaction using Gold Spreads
    gold_transactions_using_spreads = calculate_gold_transcation_using_gold_spread(gold_spreads_df)
    gold_withdrawals = read_transformed_gold_withdrawals()
    gold_loans_df = read_file_as_dataframe(spark_session=spark, asset_name=asset, entity_name="gold_loans", schema_name=schema_for_gold_loans)
    # Calculate Gold Installments using Gold Spreads and Loans
    gold_loan_installment_spreads = calculate_gold_installment_using_loans_spreads(gold_spreads_df, gold_loans_df)
    gold_loans_transformed = gold_loans_transformed(gold_loans_df, gold_spreads_df)
    gold_gift_transactions_transformed = gold_gift_transactions_transformed()
    final_gold_df = gold_transactions_using_spreads.union(gold_withdrawals).union(gold_loan_installment_spreads) \
                    .union(gold_loans_transformed).union(gold_gift_transactions_transformed)
    final_df = create_final_dataframe_for_writestream(final_gold_df)
    save_calculated_asset_returns_to_s3(final_df,
                                        config_data[asset]["gtv"]["bucket"],
                                        config_data[asset]["gtv"]["asset_folder"],
                                        get_date(config_data["offset"]),
                                        config_data[asset]["asset_name"])
