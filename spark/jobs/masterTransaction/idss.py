from pyspark.sql.functions import row_number, col, isnan, when, count, lit, ceil, floor, date_format, round, udf, \
from_utc_timestamp, sum, expr, to_timestamp, datediff, expr, get_json_object, coalesce, regexp_replace
from pyspark.sql import SparkSession
from pyspark.sql.window import Window
import pytz
from pyspark.sql.types import TimestampType, DoubleType, ArrayType, DateType, StringType, LongType, StructType, \
    StructField
from datetime import date, timedelta, datetime, timezone
from common import *
from structs import *

#It will create a spark session in common.py file
spark = get_spark_session("master_transaction_indo_stock_gtv")
asset = "indo_stock"

# Read crypto_currency_transactions files and transform the data
def read_and_transform_indo_stock_transactions():
    logging.info("Reading and  Transforming Indo Stock Transaction Data")
    entity = "indo_stock_transactions"
    try:
        indo_stock_transactions = read_file_as_dataframe(spark_session=spark, asset_name=asset, entity_name=entity,
                                                     schema_name=schema_for_indo_stock_transactions)

        indo_stock_transactions = indo_stock_transactions \
                                    .withColumn("activity",
                                                when(col("order_type") == "MARKET", "transaction")
                                                .otherwise("limit_transaction_created")) \
                                    .withColumn("quantity",
                                                when(col("order_type") == "MARKET", col("executed_quantity") * 100)
                                                .otherwise(col("ordered_quantity") * 100)) \
                                    .withColumn("net_quantity", expr(
                                    "CASE WHEN order_type = 'MARKET' THEN CASE " +
                                    "WHEN transaction_type = 'BUY' then executed_quantity * 100 " +
                                    "ELSE -executed_quantity * 100 END " +
                                    "ELSE NULL END")) \
                                    .withColumn("partner_id", lit(1000002).cast(IntegerType())) \
                                    .withColumn("currency", lit("IDR")) \
                                    .withColumn("unit_price_usd", lit(None).cast(StringType()))
        indo_stock_transactions = indo_stock_transactions.select("user_id", "account_id", lit("idss").alias("asset_type"),
                                                                 "stock_id", lit("idss").alias("asset_subtype"), "activity",
                                                                 "quantity", "executed_quantity",  "net_quantity",
                                                                 "created", "updated" \
                                                                 , col("user_status").alias("status"), "transaction_type",
                                                                 "partner_id", "client_id", "currency",
                                                                 col("average_price").alias("unit_price"), \
                                                                 lit(None).cast(DoubleType()).alias("executed_unit_price"), \
                                                                 "unit_price_usd" \
                                                                 , "total_price" \
                                                                 , lit(None).cast(DoubleType()).alias("executed_total_price") \
                                                                 , col("id").alias("ref_id") \
                                                                 , lit("indo_stock_transactions").alias("ref_table")
                                                                 )
        # indo_stock_transactions.printSchema()
        # indo_stock_transactions.show(truncate=False)
        # indo_stock_transactions.select("*").filter("executed_unit_price is NULL").show(truncate=False)
        # exit(0)
        return indo_stock_transactions
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e )


# Read crypto_mission_rewards files and transform the data
def read_and_transform_idss_txn_history():
    logging.info("Reading and  Transforming Idss Txn History Data")
    entity = "indo_stock_transaction_history"
    file_path = config_data[asset]["raw_bucket"] + config_data[asset]["entities"][entity]["folder"] + "/dt=" + str(offset_date) + "/"
    try:
        try:
            idss_txn_history = spark.read.option("recursiveFileLookup", "true").json(file_path)
            idss_txn_history = idss_txn_history.select("value.*").select("user_id", "account_id", "stock_id", "updated", "created", \
                                                       get_json_object(col("to_state"), "$.orderType").alias("order_type"),
                                                       get_json_object(col("to_state"), "$.executedQuantity").cast("float").alias("executed_quantity"), \
                                                       get_json_object(col("to_state"), "$.orderedQuantity").cast("float").alias("ordered_quantity"), \
                                                       get_json_object(col("to_state"), "$.userStatus").alias("user_status"), \
                                                       get_json_object(col("to_state"), "$.transactionType").alias("transaction_type"), \
                                                       lit(1000002).cast(IntegerType()).alias("partner_id"), \
                                                       get_json_object(col("to_state"), "$.clientId").alias("client_id"), \
                                                       get_json_object(col("to_state"), "$.averagePrice").cast("float").alias("average_price"), \
                                                       get_json_object(col("to_state"), "$.totalPrice").cast("float").alias("total_price"), \
                                                       get_json_object(col("to_state"), "$.id").alias("id"), \
                                                       get_json_object(get_json_object(col("to_state"), "$.promotionInfo"),"$.promotions[0]['brokerageFeePercentage']").alias("brokerage_pct"), \
                                                       get_json_object(col("to_state"), "$.unitPrice").cast("float").alias("unit_price"), \
                                                       get_json_object(col("to_state"), "$.exchangeFee").cast("float").alias("exchange_fee"), \
                                                       get_json_object(col("to_state"), "$.brokerFee").cast("float").alias("broker_fee"), \
                                                       )
            idss_txn_history = idss_txn_history.withColumn("updated", f.to_timestamp(col("updated")))
            window = Window.partitionBy([ col(x) for x in config_data[asset]["primary_keys"]]).orderBy(col("updated").desc())
            idss_txn_history = idss_txn_history.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")

            filter_value_list = ['SUCCESSFUL', 'CANCELLED', 'REJECTED', 'PARTIALLY_FILLED']
            idss_txn_history = idss_txn_history.filter(col("user_status").isin(filter_value_list)) \
                .filter(col("order_type") == "LIMIT")

        except Exception as e:
            emptyRDD = spark.sparkContext.emptyRDD()
            idss_txn_history = spark.createDataFrame(emptyRDD, schema_for_idss_txn_history)

        idss_txn_history = idss_txn_history \
            .withColumn("activity", when((col("user_status") == 'SUCCESSFUL') | (col("user_status") == 'PARTIALLY_FILLED'),
                                         'limit_transaction_filled') \
                        .when((col("user_status") == 'CANCELLED') | (col("user_status") == 'REJECTED'),
                              'limit_transaction_cancelled').otherwise(lit(None).cast(StringType()))) \
            .withColumn("quantity",
                        when(coalesce(col("executed_quantity"), lit(0)) == 0,
                             col("ordered_quantity") * 100)
                        .otherwise(col("executed_quantity") * 100)) \
            .withColumn("net_quantity",
                        when(col("transaction_type") == "BUY", col("executed_quantity") * 100)
                        .otherwise(
                            col("executed_quantity") * -100))

        idss_txn_history = idss_txn_history.select("user_id", "account_id", lit("idss").alias("asset_type"),
                                                   col("stock_id").alias("product_id"), lit("idss").alias("asset_subtype"),
                                                   "activity", "quantity", "executed_quantity",  "net_quantity" \
                                                   , "created", "updated", col("user_status").alias("status"),
                                                   "transaction_type" \
                                                   , lit(1000002).cast(IntegerType()).alias("partner_id"), "client_id",
                                                   lit("IDR").alias("currency"), col("average_price").alias("unit_price") \
                                                   , lit(None).cast(DoubleType()).alias("executed_unit_price") \
                                                   , lit(None).cast(StringType()).alias("unit_price_usd"),
                                                   "total_price",
                                                   lit(None).cast(StringType()).alias("executed_total_price") \
                                                   , col("id").alias("ref_id") \
                                                   , lit("indo_stock_transaction_history").alias("ref_table") \
                                                   )
        return idss_txn_history
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


# Read crypto_currency_pluangcuan_yields files and transform the data
def read_and_transform_indo_stock_e_ipo_transactions():
    logging.info("Reading and Transforming Indo Stock E Ipo Transaction Data")
    entity = "indo_stock_e_ipo_transactions"
    try:
        indo_stock_e_ipo_transactions = read_file_as_dataframe(spark_session=spark, asset_name=asset,
                                                               entity_name=entity,
                                                               schema_name=schema_for_indo_stock_e_ipo_transactions)
        indo_stock_e_ipo_transactions = indo_stock_e_ipo_transactions.select("user_id", "account_id",
                                                                             lit("idss").alias("asset_type"),
                                                                             col("stock_id").alias("product_id"),
                                                                             lit("idss").alias("asset_subtype"),
                                                                             lit("ipo").alias("activity"),
                                                                             (col("quantity") * 100).alias("quantity"),
                                                                             lit(None).cast(StringType()).alias("executed_quantity"),
                                                                             (col("quantity") * 100).alias("net_quantity"),
                                                                             "created",
                                                                             "updated",
                                                                             lit('SUCCESS').alias("status"),
                                                                             lit("BUY").alias('transaction_type'),
                                                                             lit(1000002).cast(IntegerType()).alias("partner_id")
                                                                             , lit(21).alias("client_id"),
                                                                             lit('IDR').alias('currency'),
                                                                             "unit_price"
                                                                             , lit(None).cast(DoubleType()).alias('executed_unit_price')
                                                                             , lit(None).cast(StringType()).alias('unit_price_usd')
                                                                             , lit(None).cast(StringType()).alias('total_price')
                                                                             , lit(None).cast(StringType()).alias('executed_total_price')
                                                                             , col("id").alias("ref_id")
                                                                             , lit("indo_stock_e_ipo_transactions").alias("ref_table")
                                                                             )
        return indo_stock_e_ipo_transactions
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


def create_final_dataframe_for_writestream(union_transaction_and_history_e_ipo):
    logging.info("Create final data for write stream")
    entity = "indo_stocks"
    try:
        indo_stocks = read_file_as_dataframe(spark_session=spark, asset_name=asset, entity_name=entity,
                                                        schema_name=schema_for_indo_stocks)
        indo_stocks = indo_stocks.select("id", "code")

        union_transaction_and_history_e_ipo_join_code = union_transaction_and_history_e_ipo.join(indo_stocks,
                                                                                       union_transaction_and_history_e_ipo.stock_id == indo_stocks.id,
                                                                                       "left")

        union_transaction_and_history_e_ipo_join_code.createOrReplaceTempView("union_transaction_and_history_e_ipo_join_code")

        df = spark.sql("""select INT(user_id) user_id, INT(account_id) account_id, asset_type, code as product, INT(stock_id) as product_id, asset_subtype, cast(quantity as DECIMAL(38,8)) as quantity,
         cast(executed_quantity as DECIMAL(38,8)) as executed_quantity, cast(net_quantity as DECIMAL(38,8) ) as net_quantity, 
         created, updated, status, transaction_type, partner_id, INT(client_id) as client_id, currency, 
         cast(unit_price as DECIMAL(38,8) ) as unit_price,
         executed_unit_price,
         cast(unit_price_usd as string) as unit_price_usd, 
         cast(total_price as DECIMAL(38,8) ) as total_price, 
         executed_total_price, 
         INT(ref_id) as ref_id, 
         ref_table 
         from union_transaction_and_history_e_ipo_join_code""")

        df = df.withColumn("created", from_utc_timestamp("created", "Asia/Jakarta")) \
            .withColumn("updated", from_utc_timestamp("updated", "Asia/Jakarta")) \
            .withColumn("date_paid_on", lit(None).cast(TimestampType())) \
            .withColumn("effective_date", lit(None).cast(TimestampType())) \
            .select(config_data['column_order'])
        return df
    except Exception as e:
        custom_exception(asset_name=asset, entity_name=entity, exception=e)


if __name__ == "__main__":
    # logging.info("Starting execution for Idss Asset Data")
    indo_stock_transactions = read_and_transform_indo_stock_transactions()
    idss_txn_history = read_and_transform_idss_txn_history()
    indo_stock_e_ipo_transactions = read_and_transform_indo_stock_e_ipo_transactions()
    logging.info("Union indo_stock_transactions, idss_txn_history and  indo_stock_e_ipo_transactions")
    union_transaction_and_history_e_ipo = indo_stock_transactions.union(idss_txn_history).union(indo_stock_e_ipo_transactions)
    final_df = create_final_dataframe_for_writestream(union_transaction_and_history_e_ipo)
    save_calculated_asset_returns_to_s3(final_df,
                                        config_data[asset]["gtv"]["bucket"],
                                        config_data[asset]["gtv"]["asset_folder"],
                                        get_date(config_data["offset"]),
                                        config_data[asset]["asset_name"])



