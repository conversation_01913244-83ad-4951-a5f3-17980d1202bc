'''
snapshot of
user tag mapping
'''

from common import *
from structs import *
from config import *

spark = spark_session_create("snapshot_of_user_tag_mappings")
bucket_name = config_data["bucket"]
user_tag_mappings_config = job_config_data["user_tag_mappings_snapshot"]
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()


def start_processing():
    logging.info("Starting snapshoting for user_tag_mappings")
    offset = config_data["offset"]
    dt_time = get_date_for_query(offset)
    t_1 = get_date(offset)
    t_2 = get_date(offset+1)
    t_2_path = "s3a://{}/{}/{}/dt={}/".format(bucket_name,user_tag_mappings_config["snapshot_folder"],user_tag_mappings_config["t_2_files_folder"], t_2)
    logging.info("reading data for t_2_df from {}".format(t_2_path))
    df_t_2 = read_csv_file(t_2_path, None, False, None)
    df_t_2 = df_t_2.drop("snapshot_time")
    t_1_path = "s3a://{}/{}/dt={}/".format(bucket_name, user_tag_mappings_config["t_1_files_folder"], t_1)
    logging.info("reading data for t_1_df from {}".format(t_1_path))
    df_t_1 = read_json_data(t_1_path)
    delete_record_path = "s3a://{}/{}/dt=".format(bucket_name,user_tag_mappings_config["t_1_files_folder"])
    delete_record = read_deleted_record(delete_record_path,"id",lowerbound_ts,upperbound_ts)
    if df_t_1 is not None:
        df_t_1 = df_t_1.select("value.*")
        df_t_1 = df_t_1.filter(~col("id").isin(delete_record))
        order_key = user_tag_mappings_config["order_key"]
        primary_key = user_tag_mappings_config["primary_keys"]
        df_union = get_union_and_de_dupe(df_t_1, df_t_2,primary_key,order_key)
    else:
        df_union = df_t_2
    df_union = df_union.filter(~col("id").isin(delete_record))
    df_union  = df_union.withColumn("snapshot_time",lit(dt_time))
    logging.info("writing snapshot into s3")
    df_union.coalesce(5).write.mode("overwrite").csv("s3a://{}/{}/{}/dt={}/".format(bucket_name,user_tag_mappings_config["snapshot_folder"],user_tag_mappings_config["t_2_files_folder"], t_1), header=True)
    logging.info("successfully written the data into s3")



if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snapshot_of_user_tag_mappings")



