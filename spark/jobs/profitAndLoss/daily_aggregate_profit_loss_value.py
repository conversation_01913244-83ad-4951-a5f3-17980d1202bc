from common import *
from structs import *

spark = spark_session_create("daily_aggregate_profit_loss_value")


def get_crypto_withdrawals_t_1(df_crypto_transfers):
    df_crypto_withdrawals = df_crypto_transfers.filter((col("status") == 'PENDING') & (col("transaction_type") == "WITHDRAWAL"))
    df_crypto_withdrawals = df_crypto_withdrawals.select("id", "account_id", "user_id", "client_id", "partner_id", "crypto_currency_id", "quantity", "total_quantity", "unit_price", "status", "created_at", "updated_at")
    df_crypto_withdrawals = df_crypto_withdrawals.withColumn("non_zero_weighted_cost", lit(0))
    df_crypto_withdrawals = de_dupe_dataframe(df_crypto_withdrawals, ["id"], "updated_at", type="asc")
    return df_crypto_withdrawals


def get_crypto_returns(t_1, t_2):
    logging.info("reading crypto currency returns for T-2 for the date {}".format(t_2))
    df_crypto_returns_t_2 = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["dailyAggProfitAndLoss"]["crypto_currency_returns_t_2_folder"], t_2), None, False, None)
    cols_to_select_from_returns = ["account_id", "user_id", "crypto_currency_id", "weighted_cost", "created", "updated"]
    df_crypto_returns_t_2 = df_crypto_returns_t_2.select(cols_to_select_from_returns)
    logging.info("reading delta crypto returns for the date {}".format(t_1))
    df_crypto_returns_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["dailyAggProfitAndLoss"]["crypto_currency_returns_t_1_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_returns)
    df_crypto_returns_t_1 = df_crypto_returns_t_1.select(cols_to_select_from_returns).filter(col("weighted_cost") != 0)
    df_crypto_returns = df_crypto_returns_t_1.union(df_crypto_returns_t_2)
    return df_crypto_returns


def get_crypto_withdrawal_snapshot(df_crypto_withdrawals, df_crypto_returns, t_1, t_2):
    logging.info("assiging weighted cost to crypto withdrawals")
    df_crypto_withdrawal_snapshot_t_1 = df_crypto_withdrawals.join(df_crypto_returns, on=["account_id", "user_id", "crypto_currency_id"], how="full").filter(col("non_zero_weighted_cost").isNotNull())
    df_crypto_withdrawal_snapshot_t_1 = df_crypto_withdrawal_snapshot_t_1.filter(col("created_at") > col("updated"))
    df_crypto_withdrawal_snapshot_t_1 = de_dupe_dataframe(df_crypto_withdrawal_snapshot_t_1, ["id"], "updated")
    df_crypto_withdrawal_snapshot_t_1 = df_crypto_withdrawal_snapshot_t_1.withColumn("non_zero_weighted_cost", col("weighted_cost"))
    df_crypto_withdrawal_snapshot_t_1 = df_crypto_withdrawal_snapshot_t_1.withColumn("total_withdrawal_amount", (col("non_zero_weighted_cost")*col("total_quantity")).cast(LongType()))
    df_crypto_withdrawal_snapshot_t_1 = df_crypto_withdrawal_snapshot_t_1.drop("created", "updated", "weighted_cost")
    logging.info("reading crypto withdrawals snapshot fot T-2 for the date {}".format(t_2))
    df_crypto_withdrawal_snapshot_t_2 = read_csv_file("s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["dailyAggProfitAndLoss"]["crypto_currency_withdrawal_snapshot"], t_2), None, False, None)
    df_crypto_withdrawal_snapshot_t_1 = df_crypto_withdrawal_snapshot_t_1.select(df_crypto_withdrawal_snapshot_t_2.columns)
    df_crypto_withdrawal_snapshot = df_crypto_withdrawal_snapshot_t_1.union(df_crypto_withdrawal_snapshot_t_2)
    df_crypto_withdrawal_snapshot = de_dupe_dataframe(df_crypto_withdrawal_snapshot, ["id"], "updated_at", type="asc")
    logging.info("crypto withdrawal snapshot is created for the date {}".format(t_1))
    return df_crypto_withdrawal_snapshot


def mark_crypto_withdrawals_success(df_crypto_withdrawal_snapshot, df_crypto_transfers, t_1):
    logging.info("marking withdrawals success which got success at {}".format(t_1))
    crypto_currency_wallets_success_withdrawals = df_crypto_transfers.filter((col("status") == 'SUCCESS') & (col("transaction_type") == "WITHDRAWAL"))
    crypto_currency_wallets_success_withdrawals = de_dupe_dataframe(crypto_currency_wallets_success_withdrawals, ["id"], "updated_at").select("id", "status").withColumnRenamed("status", "updated_status")
    df_crypto_withdrawal_snapshot = df_crypto_withdrawal_snapshot.join(crypto_currency_wallets_success_withdrawals, on=["id"], how="full")
    df_crypto_withdrawal_snapshot = df_crypto_withdrawal_snapshot.filter(col("status").isNotNull())
    df_crypto_withdrawal_snapshot = df_crypto_withdrawal_snapshot.withColumn("status", when(col("updated_status").isNotNull(), col("updated_status")).otherwise(col("status"))).drop("updated_status")
    return df_crypto_withdrawal_snapshot


def calculate_crypto_withdrawals(t_1, t_2):
    logging.info("starting calculation of crypto withdrawals")
    df_crypto_transfers = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["dailyAggProfitAndLoss"]["crypto_currency_transfers_t_1_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_wallet_transfers)
    df_crypto_withdrawals = get_crypto_withdrawals_t_1(df_crypto_transfers)
    df_crypto_returns = get_crypto_returns(t_1, t_2)
    df_crypto_withdrawal_snapshot = get_crypto_withdrawal_snapshot(df_crypto_withdrawals, df_crypto_returns, t_1, t_2)
    df_crypto_withdrawal_snapshot = mark_crypto_withdrawals_success(df_crypto_withdrawal_snapshot, df_crypto_transfers, t_1)
    logging.info("writing updated crypto withdrawals snapshot")
    df_crypto_withdrawal_snapshot.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["dailyAggProfitAndLoss"]["crypto_currency_withdrawal_snapshot"], t_1), header=True)
    logging.info("successfully written crypto withdrawals snapshot")
    return df_crypto_withdrawal_snapshot


def start_processing():
    logging.info("Starting execution for daily aggregate profit and loss value calculations")
    offset = config_data["offset"]
    t_1 = str(get_date(offset))
    t_2 = str(get_date(offset+1))
    logging.info("t_1 date is {}".format(t_1))
    logging.info("t_2 date is {}".format(t_2))
    cashout_s3_path = config_data["dailyAggProfitAndLoss"]["cashout_s3_path"] + t_1 + "/*"
    cashin_s3_path = config_data["dailyAggProfitAndLoss"]["cashin_s3_path"] + t_1 + "/*"
    portfilo_s3_path = config_data["dailyAggProfitAndLoss"]["portfilo_s3_path"] + t_1 + "/*"
    profit_loss_path = config_data["dailyAggProfitAndLoss"]["profit_loss_path"] + t_1
    forex_cashin_path = config_data["dailyAggProfitAndLoss"]["forex_cashin_path"] + t_1 + "/*"
    forex_cashout_path = config_data["dailyAggProfitAndLoss"]["forex_cashout_path"] + t_1 + "/*"
    crypto_currency_wallets_transfers_path = "s3a://"+config_data["bucket"]+"/"+config_data["dailyAggProfitAndLoss"]["crypto_currency_wallet_transfers_de_dupe"] + "/dt=" + str(t_1) + "/"
    try:
        # cashouts
        logging.info("Reading cashouts from path {}".format(cashout_s3_path))
        cashout = spark.read.parquet(cashout_s3_path).select("account_id", "cashout")

        # cashin
        logging.info("Reading cashin from path {}".format(cashin_s3_path))
        cashin = spark.read.parquet(cashin_s3_path).select("account_id", "cashin")

        # portfolio
        logging.info("Reading portfolio from path {}".format(portfilo_s3_path))
        postion = read_csv_file(portfilo_s3_path, None, False, None)
        postion = postion.select("account_id", "portfolioValue")

        # forex cashouts
        logging.info("Reading forex cashouts from path {}".format(forex_cashout_path))
        forex_cashout = spark.read.csv(forex_cashout_path, header=True, inferSchema=True)
        forex_cashout = forex_cashout.filter(col("status")=="COMPLETED").select("account_id","withdrawal_amount","unit_price")
        forex_cashout =forex_cashout.withColumn("forex_cashout", col("withdrawal_amount")*col("unit_price"))
        snap_forex_cashout = forex_cashout.groupBy("account_id").agg(sum("forex_cashout").cast(LongType()).alias("forex_cashout"))

        # forex cashin
        logging.info("Reading forex cashin from path {}".format(forex_cashin_path))
        forex_cashin = spark.read.csv(forex_cashin_path, header=True, inferSchema=True)
        forex_cashin = forex_cashin.filter(col("status")=="COMPLETED").select("account_id","final_amount","unit_price")
        forex_cashin = forex_cashin.withColumn("forex_cashin", col("final_amount")*col("unit_price"))
        snap_forex_cashin = forex_cashin.groupBy("account_id").agg(sum("forex_cashin").cast(LongType()).alias("forex_cashin"))
        forex_cash = snap_forex_cashout.join(snap_forex_cashin,["account_id"],"full").fillna({'forex_cashin': 0}).fillna({'forex_cashout': 0})

        # crypto currency wallets transfers (add crypto withdrawals and remove crypto deposits)
        logging.info("Reading crypto currency wallets transfers from path {}".format(crypto_currency_wallets_transfers_path))
        crypto_currency_transfer = read_csv_file(crypto_currency_wallets_transfers_path, None, False, None)
        crypto_currency_transfer = crypto_currency_transfer.filter(col("status").isin(["SUCCESS"]))
        crypto_currency_deposit = crypto_currency_transfer.filter(col("transaction_type") == "DEPOSIT").withColumn("total_crypto_receive", (col("unit_price") * col("total_quantity")).cast(LongType())).select("account_id", "total_crypto_receive").groupBy("account_id").agg(sum("total_crypto_receive").alias("total_crypto_receive"))
        crypto_currency_withdrawal = calculate_crypto_withdrawals(t_1, t_2)
        crypto_currency_withdrawal = crypto_currency_withdrawal.filter(col("status") == "SUCCESS").select("account_id", "total_withdrawal_amount").groupBy("account_id").agg(sum("total_withdrawal_amount").cast(LongType()).alias("total_crypto_send"))
        # Join
        pl = cashin.join(cashout, ["account_id"], "full").join(postion, ["account_id"], "full").join(forex_cash,["account_id"], "full").join(crypto_currency_deposit, ["account_id"], "full").join(crypto_currency_withdrawal, ["account_id"], "full").fillna(0)
        # calculating pl_value
        profit = pl.withColumn("pl_value", col("portfolioValue") + col("cashout") + col("forex_cashout") + col("total_crypto_send") - col("total_crypto_receive") - col("cashin") -col("forex_cashin") ).withColumn("pl_value_percentage", col("pl_value") * 100 / (col("cashin") + col("forex_cashin") + col("total_crypto_receive"))).withColumn("dt", lit(t_1))
        profit.coalesce(1).write.mode('overwrite').parquet(profit_loss_path)
    except Exception as e:
        logging.exception(e)
        raise e


if __name__ == "__main__":
    start_time= datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"daily_aggregate_profit_loss_value")

