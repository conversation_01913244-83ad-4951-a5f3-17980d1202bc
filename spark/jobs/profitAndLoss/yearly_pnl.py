from common import *


spark = spark_session_create("yearly_pnl")
def start_processing():
    offset = config_data["offset"]
    t_1 = get_date(offset)
    parser = argparse.ArgumentParser()
    parser.add_argument("--execute_date", help="date for which this job should run.")
    parser.add_argument("--prev_date", help="previous date for which this job should run.")
    args = parser.parse_args()
    if args.execute_date:
        t_1 = datetime.strptime(args.execute_date, '%Y-%m-%d').date()
    dt = datetime.strptime(str(t_1), '%Y-%m-%d')
    t_2 = (dt.replace(month=1, day=1) + timedelta(days=-1)).date()
    if args.prev_date:
        t_2 = datetime.strptime(args.prev_date, '%Y-%m-%d').date()
    logging.info("Starting execution of yearly profit and loss calculation")
    profit_loss_intermediate_t1 = profit_loss_intermediate(config_data["yearly_profit_and_loss"]["s3_intermediate_file_path"], str(t_1))
    profit_loss_intermediate_t2 = profit_loss_intermediate(config_data["yearly_profit_and_loss"]["s3_intermediate_file_path"], str(t_2))
    profit_loss_join = profit_loss_intermediate_t1.join(profit_loss_intermediate_t2, profit_loss_intermediate_t1.account_id == profit_loss_intermediate_t2.account_id, "full")
    yearly_profit_loss = profit_loss_join.select(profit_loss_intermediate_t1.account_id.alias("account_id"), profit_loss_intermediate_t1.dt.alias("dt"), profit_loss_intermediate_t2.pl_value.alias("startTimePNLCash"), profit_loss_intermediate_t2.pl_value_percentage.alias("startTimePNL"), profit_loss_intermediate_t1.pl_value.alias("endTimePNLCash"), profit_loss_intermediate_t1.pl_value_percentage.alias("endTimePNL"), profit_loss_intermediate_t1.cashin,profit_loss_intermediate_t1.forex_cashin,profit_loss_intermediate_t1.total_crypto_receive).fillna({'endTimePNLCash': 0, 'endTimePNL': 0, 'startTimePNL': 0, 'startTimePNLCash': 0, 'cashin': 0,'forex_cashin':0, 'total_crypto_receive': 0}).withColumn("pnlDiffCash", col("endTimePNLCash") - col("startTimePNLCash")).withColumn("pnlDiff", 100*((col("endTimePNLCash") - col("startTimePNLCash"))/(col("cashin") + col("forex_cashin") + col("total_crypto_receive")))).fillna({'pnlDiff': 0})

    startTime = (datetime.strptime(str(t_2), '%Y-%m-%d') + timedelta(days=1))
    endTime = datetime.strptime(str(t_1), '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)
    zone = pytz.timezone("UTC")
    current_ts = datetime.now(timezone.utc)
    yearly_profit_loss = yearly_profit_loss.withColumn("granularity", lit("yearly"))
    yearly_profit_loss = yearly_profit_loss.withColumn("endTime", lit(endTime))
    yearly_profit_loss = yearly_profit_loss.withColumn("startTime", lit(startTime))
    yearly_profit_loss = yearly_profit_loss.withColumn("updatedAt", lit(current_ts))
    yearly_profit_loss = yearly_profit_loss.drop("dt")
    save_profit_loss(yearly_profit_loss, str(t_1), config_data["yearly_profit_and_loss"]["s3_output_path"])

if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"yearly_pnl")
 