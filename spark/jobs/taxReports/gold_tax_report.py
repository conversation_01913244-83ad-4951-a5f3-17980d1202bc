'''
calculation of us stocks tax report
'''
from common import *
from structs import *
from config import *


class GoldTaxReport(object):

    def __init__(self):
        self.job_config = job_config_data["tax_report"]
        self.offset = config_data['offset']
        t_1 = get_date(self.offset)
        self.tax_period_end_date = t_1.replace(year=t_1.year , month=12, day=31)
        self.tax_period_start_date = t_1.replace(year=t_1.year - 1, month=12, day=31)
        self.tax_year = self.tax_period_end_date.year
        if config_data.get("tax_year") is not None:
            self.tax_year = config_data.get("tax_year")
            self.tax_period_end_date = t_1.replace(year=self.tax_year, month=12, day=31)
            self.tax_period_start_date = t_1.replace(year=self.tax_year - 1, month=12, day=31)
        self.spark = spark_session_create("gold_tax_report")



    def get_gold_realised_gain(self):
        # read gold return file for tax year end
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["gold"]["gold_returns_folder"])
        read_account_s3_path = "s3a://{}/accounts/snapshots/dt=".format(config_data["bucket"])
        accounts  = read_csv_file("{}{}".format(read_account_s3_path, self.tax_period_end_date), None, False, None)
        accounts = accounts.select("id","user_id").withColumnRenamed("id","account_id")
        df_period_end = read_csv_file("{}{}".format(s3_path, self.tax_period_end_date), None, False, None).select("account_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain")
        df_period_start = read_csv_file("{}{}".format(s3_path, self.tax_period_start_date), None, False, None).select("account_id", "realised_gain").withColumnRenamed("realised_gain", "start_realised_gain")
        df_period_end = df_period_end.join(accounts, on=["account_id"],how="left")
        df_period_start = df_period_start.join(accounts, on=["account_id"],how="left")
        df_period_realised_gain = df_period_end.join(df_period_start, on=["user_id", "account_id"], how="left").fillna(0)
        df_period_realised_gain = df_period_realised_gain.filter(col("user_id")!=0)
        df_period_realised_gain = df_period_realised_gain.withColumn("realised_gain", col("end_realised_gain") - col("start_realised_gain"))
        return df_period_realised_gain



    def start_processing(self):
        gold_realised_gain = self.get_gold_realised_gain()
        total_gold_realised_gain = gold_realised_gain.groupBy(["user_id", "account_id"]).agg(sum("realised_gain").alias("total_realised_gain"))
        df_gold_tax = total_gold_realised_gain.withColumn("financial_year", lit(self.tax_period_end_date.year))
        df_gold_tax = df_gold_tax.withColumn("category",lit("gold"))
        df_gold_tax.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], self.job_config["tax_report_folder"], self.job_config["gold"]["s3_output_folder"], self.tax_period_end_date))
        mongo_config = {"batch_size": 500, "uri": "{}.{}?authSource=admin".format(config_data["reporting_uri"], self.job_config["gold"]["mongo_collection"]), "collection": self.job_config["gold"]["mongo_collection"], "mode": "append"}
        shardkey = "{'accountId':1, 'userId':1, 'financialYear':1}"
        write_asset_returns_to_mongo(df_gold_tax, mongo_config, "Gold Tax Report", self.job_config["mongo_write_format"], shardkey)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--tax_year", help="tax year")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data["offset"] = int(offset)
    if args.tax_year:
        config_data["tax_year"] = int(args.tax_year)
    obj = GoldTaxReport()
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"gold_tax_report")




