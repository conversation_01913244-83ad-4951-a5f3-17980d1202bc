'''
calculation of us stocks tax report
'''
from common import *
from structs import *
from config import *


class UsStocksTaxReport(object):
    def __init__(self):
        self.job_config = job_config_data["tax_report"]
        self.jkt_month_name_mapping = job_config_data["jkt_month_name_mapping"]
        self.crypto_futures = job_config_data["crypto_preps"]
        self.offset = config_data['offset']
        t_1 = get_date(self.offset)
        self.tax_period_end_date = t_1.replace(year=t_1.year, month=12, day=31)
        self.tax_year = self.tax_period_end_date.year
        self.tax_period_start_date = t_1.replace(year=t_1.year - 1, month=12, day=31)
        if config_data.get("tax_year") is not None:
            self.tax_year = config_data.get("tax_year")
            self.tax_period_end_date = t_1.replace(year=self.tax_year, month=12, day=31)
            self.tax_period_start_date = t_1.replace(year=self.tax_year - 1, month=12, day=31)
        logging.info("period start dates is {} and end date is {}".format(self.tax_period_start_date, self.tax_period_end_date))
        self.spark = spark_session_create("us_stocks_tax_report")


    def get_global_stock_codes(self):
        logging.info("reading global stock codes from kafka topic")
        df_gss_code = get_global_stock()
        df_gss_code = df_gss_code.withColumnRenamed("id", "stock_id").withColumnRenamed("pluang_company_code", "symbol").select("stock_id", "symbol")
        logging.info("successfully read global stock codes from kafka")
        return df_gss_code

    def get_global_stock_realised_gain(self):
        # read global_stock_returns file for tax year end
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["us_stocks"]["global_stock_returns_folder"])
        df_gss_period_end = read_csv_file("{}{}".format(s3_path, self.tax_period_end_date), None, False, None).select("user_id", "account_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain")
        df_gss_period_start = read_csv_file("{}{}".format(s3_path, self.tax_period_start_date), None, False, None).select("user_id", "account_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "start_realised_gain")
        df_gss_period_realised_gain = df_gss_period_end.join(df_gss_period_start, on=["user_id", "account_id", "global_stock_id"], how="full").fillna(0)
        df_gss_period_realised_gain = df_gss_period_realised_gain.withColumn("realisedGain", col("end_realised_gain") - col("start_realised_gain")).filter(col("realisedGain") != 0)
        df_gss_period_realised_gain = df_gss_period_realised_gain.withColumnRenamed("global_stock_id", "stock_id")

        # read global_pocket_stock_returns file for tax year end
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["us_stocks"]["global_stock_pocket_returns_folder"])
        df_gss_pocket_period_end = read_csv_file("{}{}".format(s3_path, self.tax_period_end_date), None, False, None).select("user_id", "account_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain").groupBy(["user_id", "account_id", "global_stock_id"]).agg(f.sum("end_realised_gain").alias("end_realised_gain"))
        df_gss_pocket_period_start = read_csv_file("{}{}".format(s3_path, self.tax_period_start_date), None, False, None).select("user_id", "account_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "start_realised_gain").groupBy(["user_id", "account_id", "global_stock_id"]).agg(f.sum("start_realised_gain").alias("start_realised_gain"))
        df_gss_pocket_period_realised_gain = df_gss_pocket_period_end.join(df_gss_pocket_period_start, on=["user_id", "account_id", "global_stock_id"], how="full").fillna(0)
        df_gss_pocket_period_realised_gain = df_gss_pocket_period_realised_gain.withColumn("realisedGain", f.round(col("end_realised_gain") - col("start_realised_gain"), 2)).filter(col("realisedGain") != 0)
        df_gss_pocket_period_realised_gain = df_gss_pocket_period_realised_gain.withColumnRenamed("global_stock_id", "stock_id")

        # intraday accounts
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["us_stocks"]["global_stock_intraday_accounts_folder"])
        df_intraday_period_end = read_csv_file("{}{}".format(s3_path, self.tax_period_end_date), None, False, None).select("user_id", "account_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain")
        df_intraday_period_start = read_csv_file("{}{}".format(s3_path, self.tax_period_start_date), None, False, None).select("user_id", "account_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "start_realised_gain")
        df_intraday_period_realised_gain = df_intraday_period_end.join(df_intraday_period_start, on=["user_id", "account_id", "global_stock_id"], how="full").fillna(0)
        df_intraday_period_realised_gain = df_intraday_period_realised_gain.withColumn("realisedGain", col("end_realised_gain") - col("start_realised_gain")).filter(col("realisedGain") != 0)
        df_intraday_period_realised_gain = df_intraday_period_realised_gain.withColumnRenamed("global_stock_id", "stock_id")

        #union all
        cols_to_select = ["user_id", "account_id", "stock_id", "realisedGain"]
        df_gss_period_realised_gain = df_gss_period_realised_gain.select(cols_to_select)
        df_intraday_period_realised_gain = df_intraday_period_realised_gain.select(cols_to_select)
        df_gss_pocket_period_realised_gain = df_gss_pocket_period_realised_gain.select(cols_to_select)
        df_period_realised_gain = df_gss_period_realised_gain.union(df_intraday_period_realised_gain).union(df_gss_pocket_period_realised_gain) \
            .withColumn("user_id", col("user_id").cast(LongType())) \
            .withColumn("account_id", col("account_id").cast(LongType())) \
            .withColumn("stock_id", col("stock_id").cast(LongType()))
        df_period_realised_gain = df_period_realised_gain.groupBy(["user_id", "account_id", "stock_id"]).agg(round(sum("realisedGain"), 2).alias("realisedGain"))
        df_gss_codes = self.get_global_stock_codes()
        df_period_realised_gain = df_period_realised_gain.join(df_gss_codes, on=["stock_id"], how="left").withColumnRenamed("stock_id", "id")
        return df_period_realised_gain

    def get_stock_index_realised_gain(self):
        # read stock_index_returns file for tax year end
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["us_stocks"]["stock_index_returns_folder"])
        df_period_end = read_csv_file("s3a://{}/{}/{}/".format(config_data["bucket"], job_config_data["stock_index"]["folder"], job_config_data["stock_index"]["t_2_returns"]), None, False, None).select("user_id", "account_id", "stock_index_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain")
        df_period_start = read_csv_file("{}{}".format(s3_path, self.tax_period_start_date), None, False, None).select("user_id", "account_id", "stock_index_id", "realised_gain").withColumnRenamed("realised_gain", "start_realised_gain")
        df_period_realised_gain = df_period_end.join(df_period_start, on=["user_id", "account_id", "stock_index_id"], how="full").fillna(0)
        df_period_realised_gain = df_period_realised_gain.withColumn("realisedGain", round(col("end_realised_gain") - col("start_realised_gain"), 2)).filter(col("realisedGain") != 0)
        df_period_realised_gain = df_period_realised_gain.withColumnRenamed("stock_index_id", "stock_id")
        df_period_realised_gain = df_period_realised_gain.withColumn("symbol", when(col("stock_id") == 10000, "SNP500").otherwise("NASDAQ100")).withColumnRenamed("stock_id", "id")
        return df_period_realised_gain

    def get_gss_dividends(self):
        # read gss dividends from t_2 snapshot file
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["us_stocks"]["dividend_transaction_folder"])
        df_gss_dividend = read_csv_file("{}{}".format(s3_path, self.tax_period_end_date), None, False, None).filter(col("dividend_transaction_status") == "SUCCESS").select("user_id", "account_id", "global_stock_id", "net_amount", "created").withColumnRenamed("global_stock_id", "stock_id")
        df_gss_dividend = df_gss_dividend.withColumn("dt", f.from_utc_timestamp(f.to_timestamp("created"), "Asia/Jakarta").cast("date"))
        df_gss_dividend = df_gss_dividend.withColumn("financial_year", f.year(col("dt")))
        df_gss_dividend = df_gss_dividend.filter(col("financial_year") == self.tax_period_end_date.year).select("user_id", "account_id", "stock_id", "net_amount")
        df_gss_dividend = df_gss_dividend.groupBy("account_id", "user_id", "stock_id").agg(round(sum("net_amount"), 2).alias("dividend"))
        df_gss_codes = self.get_global_stock_codes()
        df_gss_dividend = df_gss_dividend.join(df_gss_codes, on=["stock_id"], how="left").withColumnRenamed("stock_id", "id")
        return df_gss_dividend

    def get_forex_yield(self):
        # read forex yield from aum t_2 snapshot file
        s3_path = "s3a://{}/{}/dt=".format(config_data["bucket"], self.job_config["us_stocks"]["forex_yield_folder"])
        df_forex_yield = read_csv_file("{}{}".format(s3_path, self.tax_period_end_date), None, False, None).select("user_id", "account_id", "yield_qty_received", "created")
        df_forex_yield = df_forex_yield.withColumn("dt", f.from_utc_timestamp(f.to_timestamp("created"), "Asia/Jakarta").cast("date"))
        df_forex_yield = df_forex_yield.withColumn("financial_year", f.year(col("dt")))
        df_forex_yield = df_forex_yield.withColumn("month", f.month(col("dt")))
        df_forex_yield = df_forex_yield.filter(col("financial_year") == self.tax_period_end_date.year).select("user_id", "account_id", col("yield_qty_received").alias("yield"), "month")
        df_month_text = create_month_text_df(self.tax_year)
        df_account =  df_forex_yield.select("account_id","user_id").distinct()
        df_month_account = df_account.join(df_month_text)
        df_forex_yield = df_forex_yield.join(df_month_account, on=["month","account_id","user_id"], how="full").fillna(0)
        df_forex_yield = df_forex_yield.groupBy(["account_id", "user_id", "month", "monthText"]).agg(round(sum("yield"), 2).alias("yield"))
        return df_forex_yield

    def get_global_stock_options_realised_gain(
        self,
        global_stock_options_snapshot_start_s3_path,
        global_stock_options_snapshot_end_s3_path,
        global_stock_options_contracts_s3_path
        ):
        """
        Calculate the realised gains for global stock options.
        :param global_stock_options_snapshot_start_s3_path: S3 path for snapshot start file (1 Jan 2024)
        :param global_stock_options_snapshot_end_s3_path: S3 path for snapshot end file (31 Dec 2024)
        :param global_stock_options_contracts_s3_path: S3 path for global stock options contracts CSV file
        :return: Two DataFrames, aggregated by (user_id, account_id, options_contract_id, global_stock_id)
        and (user_id, account_id)
        """
        snapshot_start_df = read_csv_file(path=global_stock_options_snapshot_start_s3_path, schema=None, is_multiline=False,
            schema_for_empty_dataframe=None). \
            withColumn("account_id", col("account_id").cast(IntegerType())).withColumn("user_id", col("user_id").cast(IntegerType())). \
            withColumn("options_contract_id", col("options_contract_id").cast(IntegerType())). \
            withColumn("global_stock_id", col("global_stock_id").cast(IntegerType())). \
            withColumn("start_realised_gain", col("realised_gain").cast(DoubleType())). \
            select("user_id", "account_id", "options_contract_id", "global_stock_id", "start_realised_gain")

        snapshot_end_df = read_csv_file(path=global_stock_options_snapshot_end_s3_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None).select("user_id", "account_id", "options_contract_id", "global_stock_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain")

        gain_with_user_id_df = (snapshot_start_df.join(snapshot_end_df,on=["user_id", "account_id", "options_contract_id", "global_stock_id"],how="full") \
            .fillna(0).withColumn("realised_gain", col("end_realised_gain") - col("start_realised_gain")).filter(col("realised_gain") != 0) \
            .select("user_id", "account_id", "options_contract_id", "global_stock_id", "start_realised_gain", "end_realised_gain", "realised_gain"))
        
        contracts_df = read_csv_file(path=global_stock_options_contracts_s3_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None) \
            .select("id", "global_stock_id", "contract_symbol", "contract_type", "strike_price", "expiration_date")
        contracts_df = contracts_df.withColumn("year", f.year("expiration_date")).withColumn("month", f.month("expiration_date")) \
            .withColumn("day", f.dayofmonth("expiration_date"))
        mapping_expr = f.create_map([f.lit(k) for k in chain(*self.jkt_month_name_mapping.items())])
        contracts_df = contracts_df.withColumn("month_name", mapping_expr[col("month")])
        contracts_df = contracts_df.withColumn("expiration_date", f.concat(col("day"), f.lit(" "), col("month_name"), f.lit(" "), col("year"))) \
            .select("id", "global_stock_id", "contract_symbol", "contract_type", "strike_price", "expiration_date")

        contracts_with_symbols_df = contracts_df.join(self.get_global_stock_codes(), contracts_df["global_stock_id"] == col("stock_id"), how="left") \
            .select("id", "stock_id", "symbol", "contract_symbol", "contract_type", "strike_price", "expiration_date")

        gain_with_contracts_df = gain_with_user_id_df.join(contracts_with_symbols_df, gain_with_user_id_df["options_contract_id"] == contracts_with_symbols_df["id"],how="left")
        null_contract_ids_df = gain_with_contracts_df.filter(col("id").isNull()).select("user_id", "options_contract_id").collect()
        null_contract_ids_data = ", ".join(f"{row['user_id']}:{row['options_contract_id'] or 'null'}" for row in null_contract_ids_df)
        logging.warning(f"{UsStocksTaxReport.get_global_stock_options_realised_gain.__name__}: Contract Id's which are not in contract table but exist in options accounts: {null_contract_ids_data}")

        gain_with_contracts_df = gain_with_contracts_df.filter(col("id").isNotNull()) \
            .withColumn("symbol",f.concat(col("symbol"),lit(" $"),col("strike_price").cast("int"),lit(" "),col("contract_type"),lit(" "),col("expiration_date"))) \
            .select("user_id","account_id", col("options_contract_id").alias("id"),"symbol",col("realised_gain").alias("realisedGain"))

        gain_with_contracts_df = gain_with_contracts_df.groupBy("user_id","account_id", "id", "symbol").agg(round(f.sum("realisedGain"),2).alias("realisedGain"))

        us_stocks_options_contract_realised_gain = gain_with_contracts_df.groupBy("user_id", "account_id") \
            .agg(f.collect_list(f.struct("id", "symbol", "realisedGain")).alias("us_stocks_options_realised_gain"))


        total_us_stocks_options_realised_gain_df = (gain_with_contracts_df.groupBy("user_id", "account_id") \
            .agg(sum("realisedGain").alias("global_stock_options_total_realised_gain")))

        return (
            us_stocks_options_contract_realised_gain,
            total_us_stocks_options_realised_gain_df,
        )


    def get_crypto_currency_futures_realised_gain(self, start_t2_path: str, end_t2_path: str):
        """
        Calculates the total crypto futures realised gain by comparing start and end realised PnL.
        
        Args:
            start_t2_path (str): Path to the start CSV file.
            end_t2_path (str): Path to the end CSV file.

        Returns:
            pyspark.sql.DataFrame: DataFrame containing user_id, account_id,
            and total_crypto_futures_realised_gain.
        """
        # Read and preprocess start and end data
        start_df = read_csv_file(path=start_t2_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None). \
            withColumn("user_id", col("user_id").cast(IntegerType())).withColumn("account_id", col("account_id").cast(IntegerType())). \
            withColumn("crypto_future_instrument_id", col("crypto_future_instrument_id").cast(IntegerType())). \
            withColumn("start_realised_pnl", col("realised_pnl").cast(DoubleType())). \
            select("user_id", "account_id", "crypto_future_instrument_id", "start_realised_pnl")

        end_df = read_csv_file(path=end_t2_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None).select("user_id", "account_id", "crypto_future_instrument_id", "realised_pnl").withColumnRenamed("realised_pnl", "end_realised_pnl")

        crypto_future_realised_gain = start_df.join(end_df, on=["user_id", "account_id", "crypto_future_instrument_id"], how="full") \
            .withColumn("end_realised_pnl", col("end_realised_pnl").cast("double")) \
            .withColumn("start_realised_pnl", col("start_realised_pnl").cast("double")).fillna(0) \
            .withColumn("realised_gain", col("end_realised_pnl") - col("start_realised_pnl")) \
            .select("user_id", "account_id", "crypto_future_instrument_id", "realised_gain")
        total_crypto_future_realised_gain = crypto_future_realised_gain.groupBy("user_id", "account_id") \
            .agg(sum("realised_gain").alias("total_crypto_futures_sales"))
        total_crypto_future_realised_gain = total_crypto_future_realised_gain.filter(col("total_crypto_futures_sales") != 0).select("user_id", "account_id", "total_crypto_futures_sales")
        return crypto_future_realised_gain, total_crypto_future_realised_gain

    def get_funding_rate_fee(self, start_t2_path: str, end_t2_path: str):
        """Get Funding Rate Fee

        Args:
            start_t2_path (str): Start T2 date file for funding rate
            end_t2_path (str): End T2 date file for funding rate
        """        
        start_df = read_csv_file(path=start_t2_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None).select("user_id", "account_id", "crypto_future_instrument_id", "fee")

        start_df = read_csv_file(path=start_t2_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None). \
            withColumn("user_id", col("user_id").cast(IntegerType())).withColumn("account_id", col("account_id").cast(IntegerType())). \
            withColumn("crypto_future_instrument_id", col("crypto_future_instrument_id").cast(IntegerType())). \
            withColumn("start_fee", col("fee").cast(DoubleType())). \
            select("user_id", "account_id", "crypto_future_instrument_id", "start_fee")

        end_df = read_csv_file(path=end_t2_path, schema=None, is_multiline=False, schema_for_empty_dataframe=None). \
            withColumn("end_fee", col("fee").cast(DoubleType())). \
            select("user_id", "account_id", "crypto_future_instrument_id", "end_fee")

        funding_rate_realised_gain = start_df.join(end_df, on=["user_id", "account_id", "crypto_future_instrument_id"], how="full").fillna(0).withColumn("end_fee", col("end_fee").cast("double")).withColumn("start_fee", col("start_fee").cast("double")).withColumn("realised_gain", col("end_fee") - col("start_fee")).select("user_id", "account_id", "crypto_future_instrument_id", "realised_gain")
        total_funding_rate_transactions = funding_rate_realised_gain.groupBy("user_id", "account_id").agg(sum("realised_gain").alias("total_funding_rate_fee")) \
        .filter(col("total_funding_rate_fee") != 0).select("user_id", "account_id", "total_funding_rate_fee")
        return funding_rate_realised_gain, total_funding_rate_transactions

    def start_processing(self):
        stock_index_realised_gain = self.get_stock_index_realised_gain()
        total_stock_index_realised_gain = stock_index_realised_gain.groupBy(["user_id", "account_id"]).agg(sum("realisedGain").alias("stock_index_total_realised_gain"))

        gss_realised_gain = self.get_global_stock_realised_gain()
        total_gss_realised_gain = gss_realised_gain.groupBy(["user_id", "account_id"]).agg(round(sum("realisedGain"), 2).alias("global_stock_total_realised_gain"))

        # Fetching GSS Options Reliased Gain
        us_stocks_options_contract_realised_gain, total_us_stocks_options_realised_gain_df = self.get_global_stock_options_realised_gain(global_stock_options_snapshot_start_s3_path="s3a://{}/{}/dt={}".format(config_data["bucket"],self.job_config["us_stocks"]["options_accounts_t2_files_folder"],self.tax_period_start_date),global_stock_options_snapshot_end_s3_path="s3a://{}/{}/dt={}".format(config_data["bucket"],self.job_config["us_stocks"]["options_accounts_t2_files_folder"],self.tax_period_end_date), global_stock_options_contracts_s3_path="s3a://{}/{}/dt={}".format(config_data["bucket"],self.job_config["us_stocks"]["options_contracts_t2_files_folder"],self.tax_period_end_date))
        total_us_stocks_options_realised_gain_df = total_us_stocks_options_realised_gain_df \
            .withColumn("global_stock_options_total_realised_gain", round("global_stock_options_total_realised_gain", 2))
        
        # Get Crypto Futures Realised Gain
        crypto_currency_future_realised_gain, total_crypto_currency_futures_realised_gain = self.get_crypto_currency_futures_realised_gain(start_t2_path="s3a://{}/{}/{}/dt={}".format(config_data["bucket"], self.crypto_futures["accounts_folder"], self.crypto_futures["t_2_files_folder"], self.tax_period_start_date),end_t2_path="s3a://{}/{}/{}/dt={}".format(config_data["bucket"], self.crypto_futures["accounts_folder"], self.crypto_futures["t_2_files_folder"], self.tax_period_end_date))
        total_crypto_currency_futures_realised_gain = total_crypto_currency_futures_realised_gain \
            .withColumn("total_crypto_futures_sales", round("total_crypto_futures_sales", 2))
        
        # Get Funding Rate        
        funding_rate_realised_gain, total_funding_rate_fee = self.get_funding_rate_fee(start_t2_path="s3a://{}/{}/dt={}".format(config_data["bucket"], self.job_config["crypto"]["crypto_futures_funding_transaction_snapshot_folder"], self.tax_period_start_date), end_t2_path="s3a://{}/{}/dt={}".format(config_data["bucket"], self.job_config["crypto"]["crypto_futures_funding_transaction_snapshot_folder"], self.tax_period_end_date))
        total_funding_rate_fee = total_funding_rate_fee.withColumn("total_funding_rate_fee", round("total_funding_rate_fee",2))

        # Combined Crypto Futures & Funding Rate Relaised Profit And Losses
        crypto_future_instruments = get_crypto_future_instruments().select("id", "future_pair_symbol")
        combined_crypto_futures_and_funding_rate_realised_gain = crypto_currency_future_realised_gain.union(funding_rate_realised_gain)
        combined_crypto_futures_and_funding_rate_realised_gain = combined_crypto_futures_and_funding_rate_realised_gain.join(crypto_future_instruments, combined_crypto_futures_and_funding_rate_realised_gain["crypto_future_instrument_id"] == crypto_future_instruments["id"], how="full").fillna(0).select("user_id", "account_id", "id", col("future_pair_symbol").alias("symbol"), col("realised_gain").alias("realisedGain"))
        combined_crypto_futures_and_funding_rate_realised_gain = combined_crypto_futures_and_funding_rate_realised_gain \
            .groupBy("user_id", "account_id", "id", "symbol").agg(round(f.sum("realisedGain"),2).alias("realisedGain"))
        combined_crypto_futures_and_funding_rate_realised_gain = combined_crypto_futures_and_funding_rate_realised_gain.groupBy("user_id", "account_id") \
            .agg(f.collect_list(f.struct("id", "symbol", "realisedGain")).alias("crypto_futures_and_funding_rate_realised_gain")).select("user_id", "account_id", "crypto_futures_and_funding_rate_realised_gain")
        union_cols = gss_realised_gain.columns
        df_us_stock_tax = gss_realised_gain.union(stock_index_realised_gain.select(union_cols))
        df_us_stock_tax = df_us_stock_tax.withColumn("us_stocks_realised_gain", f.struct(["id", "symbol", "realisedGain"]))
        df_us_stock_tax = df_us_stock_tax.groupBy(["user_id", "account_id"]).agg(round(sum("realisedGain"), 2).alias("total_asset_realised_gain"), f.collect_list("us_stocks_realised_gain").alias("us_stocks_realised_gain"))

        df_us_stock_tax = df_us_stock_tax.join(total_gss_realised_gain, on=["user_id", "account_id"], how="full").fillna(0)
        df_us_stock_tax = df_us_stock_tax.join(total_stock_index_realised_gain, on=["user_id", "account_id"], how="full").fillna(0)
        df_us_stock_tax = df_us_stock_tax.join(total_us_stocks_options_realised_gain_df,on=["user_id", "account_id"],how="full").fillna(0)
        df_us_stock_tax = df_us_stock_tax.join(total_crypto_currency_futures_realised_gain,on=["user_id", "account_id"],how="full").fillna(0)
        df_us_stock_tax = df_us_stock_tax.join(total_funding_rate_fee,on=["user_id", "account_id"],how="full").fillna(0)

        gss_dividend = self.get_gss_dividends()
        gss_dividend = gss_dividend.withColumn("us_stocks_dividend", f.struct(["id", "symbol", "dividend"]))
        total_gss_dividend = gss_dividend.groupBy(["user_id", "account_id"]).agg(round(sum("dividend"), 2).alias("total_dividend"), f.collect_list("us_stocks_dividend").alias("us_stocks_dividend"))
        df_us_stock_tax = df_us_stock_tax.join(total_gss_dividend, on=["user_id", "account_id"], how="full")
        df_us_stock_tax = df_us_stock_tax.withColumn("us_stocks_dividend", when(col("us_stocks_dividend").isNull(), f.array()).otherwise(col("us_stocks_dividend")))
        df_us_stock_tax = df_us_stock_tax.withColumn("us_stocks_realised_gain", when(col("us_stocks_realised_gain").isNull(), f.array()).otherwise(col("us_stocks_realised_gain"))).fillna(0)

        forex_yield = self.get_forex_yield()
        forex_yield = forex_yield.withColumn("monthly_yield", f.struct(["month", "monthText", "yield"]))
        forex_yield = forex_yield.groupBy(["user_id", "account_id"]).agg(round(sum("yield"), 2).alias("total_yield"), f.collect_list("monthly_yield").alias("monthly_yield"))
        forex_yield = forex_yield.withColumn("monthly_yield", f.expr("array_sort(monthly_yield)"))
        df_us_stock_tax = df_us_stock_tax.join(forex_yield, on=["user_id", "account_id"], how="full")
        df_us_stock_tax = df_us_stock_tax.withColumn("monthly_yield", when(col("monthly_yield").isNull(), f.array()).otherwise(col("monthly_yield")))
        df_us_stock_tax = df_us_stock_tax.withColumn("us_stocks_realised_gain", when(col("us_stocks_realised_gain").isNull(), f.array()).otherwise(col("us_stocks_realised_gain"))).fillna(0)
        df_us_stock_tax = df_us_stock_tax.join(us_stocks_options_contract_realised_gain, on=["user_id", "account_id"],how="full")
        df_us_stock_tax = df_us_stock_tax.join(combined_crypto_futures_and_funding_rate_realised_gain, on=["user_id", "account_id"],how="full")        
        df_us_stock_tax = df_us_stock_tax.withColumn("us_stocks_options_realised_gain",when(col("us_stocks_options_realised_gain").isNull(), f.array()).otherwise(col("us_stocks_options_realised_gain")))
        df_us_stock_tax = df_us_stock_tax.withColumn("crypto_futures_and_funding_rate_realised_gain",when(col("crypto_futures_and_funding_rate_realised_gain").isNull(), f.array()).otherwise(col("crypto_futures_and_funding_rate_realised_gain")))
        df_us_stock_tax = df_us_stock_tax.withColumn("category", lit("globalStock"))
        df_us_stock_tax = df_us_stock_tax.withColumn("financial_year", lit(self.tax_period_end_date.year))
        df_us_stock_tax = df_us_stock_tax.withColumn("total_asset_realised_gain", 
                round(col("total_asset_realised_gain")+ col("global_stock_options_total_realised_gain"), 2))
        df_us_stock_tax = df_us_stock_tax.withColumn("total_realised_gain", round(col("total_asset_realised_gain") + col("total_dividend") + col("total_yield"),2))
        df_us_stock_tax = df_us_stock_tax.withColumn("total_crypto_futures_realised_profit_and_loss", round(col("total_crypto_futures_sales") + col("total_funding_rate_fee"), 2))
        df_us_stock_tax = df_us_stock_tax.withColumn("account_id", col("account_id").cast(LongType()))
        df_us_stock_tax = df_us_stock_tax.withColumn("user_id", col("user_id").cast(LongType()))
        logging.info("Writing the tax data into s3")
        df_us_stock_tax.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"], self.job_config["tax_report_folder"], self.job_config["us_stocks"]["s3_output_folder"], self.tax_period_end_date))
        logging.info("Data is written into s3. Starting the mongo insert job")
        mongo_config = {"batch_size": self.job_config["batch_size"], "uri": "{}.{}?authSource=admin".format(config_data["reporting_uri"], self.job_config["us_stocks"]["mongo_collection"]), "collection": self.job_config["us_stocks"]["mongo_collection"], "mode": "append"}
        shardkey = "{'accountId':1, 'userId':1, 'financialYear':1}"
        write_asset_returns_to_mongo(df_us_stock_tax, mongo_config, "Us Stocks Tax Report", self.job_config["mongo_write_format"], shardkey)
        logging.info("Data is successfully written into {} collection".format(self.job_config["us_stocks"]["mongo_collection"]))


        


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--tax_year", help="tax year")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data["offset"] = int(offset)
    if args.tax_year:
        config_data["tax_year"] = int(args.tax_year)
    obj = UsStocksTaxReport()
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"us_stocks_tax_report")


