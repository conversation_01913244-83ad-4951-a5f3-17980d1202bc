'''
calculate forex yield
morning 7AM JKT
'''
from common import *
from structs import *
from config import *
import calendar

spark = spark_session_create("forex_yield_calculation")

forex_yield_properties = job_config_data["forex_yield_properties"]
asset_config_forex_accounts = forex_yield_properties["forex_accounts"]
asset_general_items = job_config_data["forex_yield_properties"]["general_items"]
bucket_name = config_data["bucket"]
updateLastMonthYieldFlag = False
tag_name = forex_yield_properties["tag_name"]
disbursementFlag = False
url = "{}/{}".format(config_data["postgres_url"],forex_yield_properties["general_items"]["db_name"])

'''read forex account data for 5 hour '''
def get_forex_accounts_data(s3_path,offset,asset_config_forex_accounts):
    dt_time = get_date_for_query(offset)
    dt = dt_time.date()
    forex_accounts_t_1 = read_json_data("{}{}/".format(s3_path, str(dt)), is_raw=True, schema_for_empty_df=schema_for_forex_accounts)
    logging.info(s3_path +str(dt)+"/")
    hour= asset_config_forex_accounts["filter_time"]["hour"]
    minute= asset_config_forex_accounts["filter_time"]["minute"]
    second= asset_config_forex_accounts["filter_time"]["second"]
    microsecond = asset_config_forex_accounts["filter_time"]["microsecond"]
    dt_utc =dt_time.replace(hour=hour,minute=minute,second=second,microsecond=microsecond).strftime("%Y-%m-%dT%H:%M:%S.000Z")
    forex_accounts_t_1 = forex_accounts_t_1.filter(col("updated")< dt_utc ).distinct()
    return forex_accounts_t_1

'''read forex account data'''
def forex_accounts(asset_config_forex_accounts,offset):
    current_day_s3_path = "{}/{}/dt=".format(config_data["forex"]["raw_bucket"],config_data["forex"]["t1"]["asset_accounts_folder"])
    logging.info(current_day_s3_path)
    forex_accounts_t_0 = get_forex_accounts_data(current_day_s3_path,offset,asset_config_forex_accounts)
    forex_accounts_t_1 = get_asset_t_2(bucket_name,
                                       config_data["forex"]["t2"]["asset_accounts_folder"],
                                       config_data["forex"]["t2"]["files_folder"],
                                       get_date(config_data["offset"]+1),
                                       schema_for_forex_accounts,
                                       config_data["forex"]["asset_name"])
    forex_accounts_balance = get_union_and_de_dupe(forex_accounts_t_0, forex_accounts_t_1, config_data["forex"]["primary_keys"], "updated")
    return forex_accounts_balance


''' check user tagging ,user is pluang plus or not'''
def check_user_type_mappings(asset_user_tag_mappings_path):
    tag_id = forex_yield_properties["user_tag_id"]
    user_type_mappings = spark.read.csv(asset_user_tag_mappings_path,header=True,inferSchema=True)
    user_type_mappings = user_type_mappings.filter(col("tag_name").isin(tag_name))
    user_type_mappings = user_type_mappings.select("user_id","tag_name").distinct()
    user_type_mappings = user_type_mappings.withColumnRenamed("user_id","user_id_dup")
    logging.info("user tag mapping fetch")
    return user_type_mappings


def average_limit_balance(forex_accounts_kyc_mapping):
    general_items = spark.read.format("jdbc").option("user", config_data["postgres_username"]).option("password",config_data["postgres_password"]).option("url", url).option("dbtable", asset_general_items["db_table"]).load()
    limit_balance = general_items.filter(col("key").isin("FOREX_YIELD_PRINCIPAL_MAX_CAPPING")).select("value").collect()[0]["value"]
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_wallet_balance_of_regular_user",round((col("sum_of_account_balance_of_regular_user")/col("yield_days_of_regular_user")),5))
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_wallet_balance_of_pluang_plus_user",round((col("sum_of_account_balance_of_pluang_plus_user")/col("yield_days_of_pluang_plus_user")),5))
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_balance_of_regular_user_for_yield_consider",when(col("average_wallet_balance_of_regular_user")>limit_balance,lit(limit_balance)).otherwise(col("average_wallet_balance_of_regular_user")))
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_balance_of_pluang_plus_user_for_yield_consider",when(col("average_wallet_balance_of_pluang_plus_user")>limit_balance,lit(limit_balance)).otherwise(col("average_wallet_balance_of_pluang_plus_user")))
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_balance_of_regular_user_for_yield_consider",col("average_balance_of_regular_user_for_yield_consider").cast("double"))
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_balance_of_pluang_plus_user_for_yield_consider",col("average_balance_of_pluang_plus_user_for_yield_consider").cast("double")).fillna(0)
    logging.info("calculate avg limit forex balance for yield ")
    return forex_accounts_kyc_mapping


'''check interest rate of user type and join with eligble user'''
def  yield_calculation(asset_general_items,forex_accounts_kyc_mapping,execution_date):
    year_days = 360
    key_list = asset_general_items["key_list"]
    forex_accounts_kyc_mapping = average_limit_balance(forex_accounts_kyc_mapping)
    general_items = spark.read.format("jdbc").option("user", config_data["postgres_username"]).option("password",config_data["postgres_password"]).option("url", url).option("dbtable", asset_general_items["db_table"]).load()
    interest_rate = general_items.filter(col("key").isin(key_list)).select("key","value","meta")
    interest_rate_regular_user =interest_rate.filter(col("key").isin("REGULAR_USD_YIELD_PERCENTAGE")).select("value").collect()[0]["value"]
    interest_rate_pluang_plus_user = interest_rate.filter(col("key").isin("PRIORITY_USD_YIELD_PERCENTAGE")).select("value").collect()[0]["value"]
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("average_wallet_balance",round(((col("sum_of_account_balance_of_regular_user")+col("sum_of_account_balance_of_pluang_plus_user"))/(col("yield_days_of_pluang_plus_user") + col("yield_days_of_regular_user"))),5))
    user_level_interest_rate = forex_accounts_kyc_mapping.withColumn("yield_quantity",floor((((col("average_balance_of_pluang_plus_user_for_yield_consider")*col("yield_days_of_pluang_plus_user")*interest_rate_pluang_plus_user)/(year_days*100)).cast("double")+((col("average_balance_of_regular_user_for_yield_consider")*col("yield_days_of_regular_user")*interest_rate_regular_user)/(year_days*100)).cast("double"))*100)/100.0)
    user_level_interest_rate = user_level_interest_rate.withColumn("yield_percentage",round((((col("yield_days_of_pluang_plus_user")*interest_rate_pluang_plus_user)/year_days)+((col("yield_days_of_regular_user")*interest_rate_regular_user)/year_days)),5))
    user_level_interest_rate = user_level_interest_rate.withColumn("interest_rate_regular_user",lit(interest_rate_regular_user))
    user_level_interest_rate = user_level_interest_rate.withColumn("interest_rate_pluang_plus_user",lit(interest_rate_pluang_plus_user))
    logging.info("calculatation of yield is completed")
    return user_level_interest_rate

'''fetch avg balance of user and elgible day of yield'''
def calculation_forex_accounts_kyc_aggreagate(forex_accounts_kyc,execution_date,first_of_month_date,offset):
    if execution_date == first_of_month_date:
        forex_accounts_kyc = forex_accounts_kyc.withColumn("sum_of_account_balance_of_pluang_plus_user",when((col("pluang_plus_flag")==True) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True) ,col("balance")).otherwise(0)). \
            withColumn("sum_of_account_balance_of_regular_user",when((col("pluang_plus_flag")==False) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),col("balance")).otherwise(0)). \
            withColumn("yield_days_of_pluang_plus_user",when((col("pluang_plus_flag")==True) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),lit(1)).otherwise(0)). \
            withColumn("yield_days_of_regular_user",when((col("pluang_plus_flag")==False) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),lit(1)).otherwise(0))
    else:
        dt_1 = get_date(offset+1)
        s3_path = "s3a://{}/{}/{}/dt={}".format(bucket_name,forex_yield_properties["usd_yield"],forex_yield_properties["snap_forex_accounts_yield"],str(dt_1))
        forex_accounts_kyc_t_1 = spark.read.csv(s3_path,header=True,inferSchema=True)
        forex_accounts_kyc_t_1 = forex_accounts_kyc_t_1.select("account_id","user_id","forex_id","partner_id","sum_of_account_balance_of_pluang_plus_user","sum_of_account_balance_of_regular_user","yield_days_of_pluang_plus_user","yield_days_of_regular_user")
        forex_accounts_kyc = forex_accounts_kyc.join(forex_accounts_kyc_t_1,["account_id","user_id","forex_id","partner_id"],"full").drop("user_id_dup").fillna(0)
        forex_accounts_kyc = forex_accounts_kyc.withColumn("sum_of_account_balance_of_pluang_plus_user_new",when((col("pluang_plus_flag")==True) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),(col("balance") + col("sum_of_account_balance_of_pluang_plus_user"))).otherwise(col("sum_of_account_balance_of_pluang_plus_user"))). \
            withColumn("sum_of_account_balance_of_regular_user_new",when((col("pluang_plus_flag")==False) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),(col("balance") +col("sum_of_account_balance_of_regular_user"))).otherwise(col("sum_of_account_balance_of_regular_user"))). \
            withColumn("yield_days_of_pluang_plus_user_new",when((col("pluang_plus_flag")==True) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),(col("yield_days_of_pluang_plus_user")+1)).otherwise(col("yield_days_of_pluang_plus_user"))). \
            withColumn("yield_days_of_regular_user_new",when((col("pluang_plus_flag")==False) & (col("gss_kyc_verfied").isin("VERIFIED")) & (col("is_forex_yield_active")==True),(col("yield_days_of_regular_user")+1)).otherwise(col("yield_days_of_regular_user")))
        forex_accounts_kyc = forex_accounts_kyc.drop("sum_of_account_balance_of_pluang_plus_user","sum_of_account_balance_of_regular_user","yield_days_of_pluang_plus_user","yield_days_of_regular_user","sum_of_limit_account_balance_of_regular_user","sum_of_limit_account_balance_of_pluang_plus_user")
        forex_accounts_kyc = forex_accounts_kyc.withColumn("sum_of_account_balance_of_pluang_plus_user",col("sum_of_account_balance_of_pluang_plus_user_new").cast("double"))
        forex_accounts_kyc = forex_accounts_kyc.withColumn("sum_of_account_balance_of_regular_user",col("sum_of_account_balance_of_regular_user_new").cast("double"))
        forex_accounts_kyc = forex_accounts_kyc.withColumn("yield_days_of_pluang_plus_user",col("yield_days_of_pluang_plus_user_new").cast(IntegerType()))
        forex_accounts_kyc = forex_accounts_kyc.withColumn("yield_days_of_regular_user",col("yield_days_of_regular_user_new").cast(IntegerType())).fillna(0)
        forex_accounts_kyc = forex_accounts_kyc.drop("sum_of_account_balance_of_pluang_plus_user_new","sum_of_account_balance_of_regular_user_new","yield_days_of_pluang_plus_user_new","yield_days_of_regular_user_new")
    logging.info("succesfully calcualate number of days yield and sum")
    forex_accounts_kyc.coalesce(1).write.mode('overwrite').save("s3a://{}/{}/{}/dt={}".format(bucket_name,forex_yield_properties["usd_yield"],forex_yield_properties["snap_forex_accounts_yield"],str(execution_date)),format="csv", header=True)
    return forex_accounts_kyc


''' publish the data kafka'''
def publish_kafka_events(df, kafka_topic, t_1):
    #already_published = check_if_already_published(t_1, job_config)
    already_published = False
    logging.info("Checking if data is already published for {}".format(t_1))
    if already_published is False:
        logging.info("Compared data is not published yet for {}".format(t_1))
        logging.info("publishing usd yield in kafka topic")
        cols_to_publish = df.columns
        df = df.withColumn("x-request-id", f.expr("uuid()"))
        df = df.select(col("accountId").cast(StringType()).alias("key"), f.to_json(f.struct(cols_to_publish)).alias("value"), f.array(f.struct(lit("x-request-id").alias("key"), col("x-request-id").cast("binary").alias("value"))).alias("headers"))
        write_data_in_kafka_topic(df, config_data["bootstrap_servers"], kafka_topic)
        logging.info("Compared events are successfully published")
        logging.info("Writing published events in s3")
    else:
        logging.info("data is already published for usd yield for date".format(t_1))


def convert_columns(forex_yield_calculation_user_with_yield):
    original_columns = forex_yield_calculation_user_with_yield.columns
    for cols in original_columns:
        input_str = cols
        renamed_col = convert_snake_to_camel_case(input_str)
        forex_yield_calculation_user_with_yield = forex_yield_calculation_user_with_yield.withColumnRenamed(cols, renamed_col)
    logging.info("columns renames")
    return forex_yield_calculation_user_with_yield


def get_forex_price(usd_yield):
    current_forex_prices = get_current_forex_price(config_data["forex"]["partner_id"],
                                                   config_data["forex"]["forex_id"],
                                                   config_data["forex"]["asset_name"])
    current_forex_prices = current_forex_prices.select(col("forex_id").alias("partner_forex_id"),
                                                       col("mid_price").alias("usdToIdr"))
    usd_yield = usd_yield.join(current_forex_prices, usd_yield.forexId == current_forex_prices.partner_forex_id,"left").drop("partner_forex_id")
    logging.info("get forex mid price")
    return usd_yield


def get_last_month_yield(yield_of_current_month,last_month_date):
    last_month_yield = spark.read.csv("s3a://{}/{}/{}/dt={}/*".format(config_data["bucket"],forex_yield_properties["usd_yield"],forex_yield_properties["snap_usd_yield"],str(last_month_date)),header=True,inferSchema=True)
    last_month_yield = last_month_yield.select("userId","yieldQuantity","taxPercentage","taxAmount").withColumn("lastMonthYieldQuantity", col("yieldQuantity").cast('double')).drop("yieldQuantity")
    last_month_yield = last_month_yield.withColumn("lastMonthTaxPercentage", col("taxPercentage").cast('double')).drop("taxPercentage")
    last_month_yield = last_month_yield.withColumn("lastMonthTaxAmount", col("taxAmount").cast('double')).drop("taxAmount")
    yield_of_current_month = yield_of_current_month.join(last_month_yield,["userId"],"full").fillna(0)
    logging.info("last month yield fetch")
    return yield_of_current_month


def forex_yield_column_filter_add_column(forex_yield_calculation_user_with_yield):
    forex_yield_calculation_user_with_yield = forex_yield_calculation_user_with_yield.select("account_id","user_id","forex_id","partner_id","forex_yield_joining_date","is_forex_yield_active","gss_kyc_verfied","kyc_verified_date","tag_name","pluang_plus_flag","sum_of_account_balance_of_pluang_plus_user","sum_of_account_balance_of_regular_user","yield_days_of_pluang_plus_user","yield_days_of_regular_user","average_balance_of_regular_user_for_yield_consider","average_balance_of_pluang_plus_user_for_yield_consider","average_wallet_balance","yield_quantity","yield_percentage","execution_date")
    forex_yield_calculation_user_with_yield = forex_yield_calculation_user_with_yield.withColumn("tax_percentage",lit(0.0)).withColumn("tax_amount",lit(0.0))
    forex_yield_calculation_user_with_yield = convert_columns(forex_yield_calculation_user_with_yield)
    return forex_yield_calculation_user_with_yield


def start_processing():
    offset = config_data["offset"]
    execution_date = get_date(offset)
    first_of_month_date = execution_date.replace(day=1)
    last_month_date = first_of_month_date - timedelta(days=1)
    snap_forex_accounts = forex_accounts(asset_config_forex_accounts,offset)
    forex_yield_opt_in_path = "s3a://{}/{}/dt={}/".format(bucket_name,forex_yield_properties["snap_user_forex_yield_opt_in"], execution_date)
    snap_forex_yield_opt_in = spark.read.csv(forex_yield_opt_in_path,header=True,inferSchema=True).filter(col("is_forex_yield_active")==True).withColumn("user_id_dup",col("user_id")).select("user_id_dup","forex_yield_joining_date","is_forex_yield_active")
    snap_forex_accounts = snap_forex_accounts.join(snap_forex_yield_opt_in,snap_forex_accounts.user_id== snap_forex_yield_opt_in.user_id_dup,"left").drop("user_id_dup")
    gss_kyc_t_2_path = "s3a://{}/{}/dt={}/".format(bucket_name,forex_yield_properties["snap_gss_kyc_information_path"], execution_date)
    snap_gss_kyc_information = spark.read.csv(gss_kyc_t_2_path,header=True,inferSchema=True)
    snap_gss_kyc_information = snap_gss_kyc_information.filter(col("to_state")=="VERIFIED").withColumn("kyc_verified_date",col("created")).withColumn("user_id_dup",col("user_id"))
    snap_gss_kyc_information = snap_gss_kyc_information.withColumn("gss_kyc_verfied",col("to_state")).select("user_id_dup","gss_kyc_verfied","kyc_verified_date")
    forex_accounts_kyc = snap_forex_accounts.join(snap_gss_kyc_information,snap_forex_accounts.user_id== snap_gss_kyc_information.user_id_dup,"left").drop("user_id_dup")
    user_type_mappings_path = "s3a://{}/{}/dt={}/".format(bucket_name,forex_yield_properties["user_tag_mappings_path"], execution_date)
    user_type_mappings = check_user_type_mappings(user_type_mappings_path)
    forex_accounts_kyc_mapping = forex_accounts_kyc.join(user_type_mappings,forex_accounts_kyc.user_id== user_type_mappings.user_id_dup,"left").drop("user_id_dup")
    forex_accounts_kyc_mapping = forex_accounts_kyc_mapping.withColumn("pluang_plus_flag",when((col("tag_name").isin(tag_name)),True).otherwise(False))
    forex_accounts_kyc_aggreagate  = calculation_forex_accounts_kyc_aggreagate(forex_accounts_kyc_mapping,execution_date,first_of_month_date,offset)
    forex_interest_rate = yield_calculation(asset_general_items,forex_accounts_kyc_aggreagate,execution_date)
    forex_yield_calculation_user_with_yield = forex_interest_rate.withColumn("execution_date",lit(execution_date))
    forex_yield_calculation_user_with_yield = forex_yield_column_filter_add_column(forex_yield_calculation_user_with_yield)
    forex_yield_calculation_user_with_yield = get_last_month_yield(forex_yield_calculation_user_with_yield,last_month_date)
    forex_yield_calculation_user_with_yield_usd = get_forex_price(forex_yield_calculation_user_with_yield)
    forex_yield_calculation_user_with_yield_usd = forex_yield_calculation_user_with_yield_usd.withColumn("disbursementFlag",lit(disbursementFlag))
    if execution_date == first_of_month_date:
        forex_yield_calculation_user_with_yield_usd = forex_yield_calculation_user_with_yield_usd.withColumn("updateLastMonthYieldFlag",lit(True))
    else:
        forex_yield_calculation_user_with_yield_usd = forex_yield_calculation_user_with_yield_usd.withColumn("updateLastMonthYieldFlag",lit(updateLastMonthYieldFlag))
    logging.info("s3 write path started")
    logging.info("s3a://{}/{}/{}/dt={}".format(config_data["bucket"],forex_yield_properties["usd_yield"],forex_yield_properties["snap_usd_yield"],str(execution_date)))
    forex_yield_calculation_user_with_yield_usd.coalesce(1).write.mode("overwrite").save("s3a://{}/{}/{}/dt={}".format(config_data["bucket"],forex_yield_properties["usd_yield"],forex_yield_properties["snap_usd_yield"],str(execution_date)),format="csv", header=True)
    forex_yield_calculation_user_with_yield_usd = forex_yield_calculation_user_with_yield_usd.filter(col("accountId")!=0)
    forex_yield_calculation_user_with_yield_usd = forex_yield_calculation_user_with_yield_usd.filter(((col("updateLastMonthYieldFlag")==False) & (col("yieldQuantity")>=0.01)) | ((col("updateLastMonthYieldFlag")==True) & ((col("lastMonthYieldQuantity")>=0.01) | (col("yieldQuantity")>=0.01))  ))
    forex_yield_calculation_user_with_yield_usd = forex_yield_calculation_user_with_yield_usd.select("accountId","userId","forexId","partnerId","averageWalletBalance","yieldQuantity","yieldPercentage","executionDate","taxPercentage","taxAmount","lastMonthYieldQuantity","lastMonthTaxPercentage","lastMonthTaxAmount","updateLastMonthYieldFlag","disbursementFlag","usdToIdr")
    kafka_topic = forex_yield_properties["kafka_topic"]
    publish_kafka_events(forex_yield_calculation_user_with_yield_usd,kafka_topic, execution_date)

if __name__ == "__main__":
    start_time = datetime.now()
    offset = config_data["offset"]-1
    config_data['offset'] = int(offset)
    parser = argparse.ArgumentParser()
    parser.add_argument("--job_type", help="offset for the job")
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"forex_yield_calculation")
