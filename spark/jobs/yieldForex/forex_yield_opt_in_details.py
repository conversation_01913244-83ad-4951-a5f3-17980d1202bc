'''
snapshot of
user tag mapping
'''

from common import *
from structs import *
from config import *

spark = spark_session_create("forex_yield_opt_in_details")
bucket_name = config_data["bucket"]
forex_account_settings_details_config = job_config_data["forex_account_settings_details"]
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()


def start_processing():
    logging.info("Starting snapshoting for forex yield opt in details")
    offset = config_data["offset"]
    dt_time = get_date_for_query(offset)
    t_1 = get_date(offset)
    t_2 = get_date(offset+1)
    t_2_path = "s3a://{}/{}/{}/dt={}/".format(bucket_name,forex_account_settings_details_config["asset_folder"],forex_account_settings_details_config["t2_files_folder"], t_2)
    logging.info("reading data for t_2_df from {}".format(t_2_path))
    df_t_2 = read_csv_file(t_2_path, None, False, None)
    t_1_path = "s3a://{}/{}/dt={}/".format(bucket_name, forex_account_settings_details_config["t_1_files_folder"], t_1)
    logging.info("reading data for t_1_df from {}".format(t_1_path))
    df_t_1 = read_json_data(t_1_path)
    delete_record_path = "s3a://{}/{}/dt=".format(bucket_name, forex_account_settings_details_config["t_1_files_folder"])
    delete_record = read_deleted_record(delete_record_path,"id",lowerbound_ts,upperbound_ts)
    if df_t_1 is not None:
        df_t_1 = df_t_1.select("value.*")
        if offset == 0:
            hour= forex_account_settings_details_config["filter_time"]["hour"]
            minute= forex_account_settings_details_config["filter_time"]["minute"]
            second= forex_account_settings_details_config["filter_time"]["second"]
            microsecond = forex_account_settings_details_config["filter_time"]["microsecond"]
            dt_utc =dt_time.replace(hour=hour,minute=minute,second=second,microsecond=microsecond).strftime("%Y-%m-%dT%H:%M:%S.000Z")
            df_t_1 = df_t_1.filter(col("created")< dt_utc ).distinct()
        order_key = forex_account_settings_details_config["order_key"]
        primary_key = forex_account_settings_details_config["primary_keys"]
        df_union = get_union_and_de_dupe(df_t_1, df_t_2,primary_key,order_key)
    else:
        df_union = df_t_2
    df_union = df_union.filter(~col("id").isin(delete_record))
    logging.info("writing snapshot into s3")
    df_union.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/{}/dt={}/".format(bucket_name,forex_account_settings_details_config["asset_folder"],forex_account_settings_details_config["t2_files_folder"], t_1), header=True)
    logging.info("successfully written the data into s3")



if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"forex_yield_opt_in_details")

