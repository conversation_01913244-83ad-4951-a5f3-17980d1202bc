import logging

from common import *
from structs import *
from config import *

class CheckKafkaLastMsgTime(object):

    def __init__(self):
        # Fill this variable with your last date
        self.lowerbound = get_date_for_query(config_data["offset"]+1)

        # Current execution
        self.upperbound = datetime.now()
        self.spark = spark_session_create("aum_crypto_currency")
        logging.info("spark session create")
        self.kafka_topic_list = job_config_data["kafka_validation_check_for_topic"]
        self.raw_bucket = config_data["raw_bucket_name"]
        self.topic_list_msg_delay=[]
        self.topic_list_data_not_available =[]
        self.slack_webhook_url = config_data["slack_webhook_url"]


    def read_raw_data(self,s3_path):
        try:
            df = self.spark.read.json(s3_path,modifiedAfter=self.lowerbound.strftime('%Y-%m-%dT%H:%M:%S'),
                                    modifiedBefore=self.upperbound.strftime('%Y-%m-%dT%H:%M:%S')).select("value.*")
            return df
        except Exception as e:
            logging.warning(e)
            return None


    def check_last_message(self,df,topic):
        try:
            zone = pytz.timezone("Asia/Jakarta")
            logging.info("check msg time for {}".format(topic))
            msg_check_time = datetime.now(tz=zone)- timedelta(hours=0, minutes=40)
            msg_check_time = msg_check_time.replace(tzinfo=None)
            last_msg_time = df.withColumn("kafka_msg_time", col("__source_ts_ms")/1000).select("kafka_msg_time")
            last_msg_time = last_msg_time.withColumn("kafka_msg_time", f.from_utc_timestamp(f.to_timestamp("kafka_msg_time"), "Asia/Jakarta"))
            last_msg_time = last_msg_time.groupBy().agg(f.max(col("kafka_msg_time")).alias('kafka_msg_time')).collect()[0]["kafka_msg_time"]
            logging.info("value is {}".format(last_msg_time))
            logging.info("value is {}".format(msg_check_time))
            if last_msg_time < msg_check_time:
                self.topic_list_msg_delay.append(topic)
                logging.warning("Please check msg of this topic {} and last msg time {} and current time {}".format(topic,last_msg_time,msg_check_time))
            else:
                logging.info("last 30 mins msg fetch")
        except Exception as e:
            logging.error(e)

    def start_processing(self):
        dt = get_date(config_data['offset'])
        for topic in self.kafka_topic_list:
            s3_path = "s3a://{}{}/dt={}".format(self.raw_bucket,topic,str(dt))
            df = self.read_raw_data(s3_path)
            if df is not None:
                self.check_last_message(df,topic)
                logging.info(df.count())
            else:
                self.topic_list_data_not_available.append(topic)
                logging.warning("please check raw data and kafka msg  for topic {}".format(topic))
        logging.info(self.topic_list_msg_delay)
        logging.info(self.topic_list_data_not_available)
        slack_msg_alert_for_delay_data = "*@channel*\n*kafka raw data validation date*: {offset_date}\n*message*: {message}\n{topic_list}".format(
            offset_date= dt,
            message="message not consumed before 30 min current time",
            topic_list=self.topic_list_msg_delay
        )
        slack_msg_alert_for_data_not_available  = "*@channel*\n*kafka raw data validation date*: {offset_date}\n*message*: {message}\n{topic_list}".format(
            offset_date= dt,
            message="data not available for date",
            topic_list=self.topic_list_data_not_available
        )
        slack_msg(slack_msg_alert_for_delay_data, self.slack_webhook_url)
        slack_msg(slack_msg_alert_for_data_not_available, self.slack_webhook_url)
        logging.info("send alert of slack")


if __name__ == "__main__":
    start_time =datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    obj = CheckKafkaLastMsgTime()
    obj.start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"check kafka msg update time")