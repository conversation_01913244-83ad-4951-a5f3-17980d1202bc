from common import *
from structs import *


raw_bucket = config_data["forex"]["raw_bucket"]
spark = spark_session_create("snap_forex_returns")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()
cut_off_time=None

def calculate_forex_returns(forex_t_0, current_forex_prices, asset_name):
    try:
        forex_t_0 = forex_t_0.join(current_forex_prices,
                                   forex_t_0["forex_id"] == current_forex_prices["partner_forex_id"], "left").drop(
            "partner_forex_id")

        forex_returns_t_0 = forex_t_0.withColumnRenamed("partner_mid_price", "unit_price")

        forex_returns_t_0 = forex_returns_t_0.withColumn("unit_price", col("unit_price").cast("long"))

        forex_returns_t_0 = forex_returns_t_0.withColumn("total_quantity", col("total_quantity").cast("double"))
        forex_returns_t_0 = forex_returns_t_0.withColumn("total_quantity", round(col("total_quantity"), 2))

        forex_returns_t_0 = forex_returns_t_0.withColumn("total_value", floor(
            col("total_quantity") * col("unit_price")))
        forex_returns_t_0 = forex_returns_t_0.withColumn("unrealised_gain", col("total_value") - ceil(
            col("total_quantity") * col("weighted_cost")))

        logging.info("Successfully calculated {} returns".format(asset_name))

        return forex_returns_t_0

    except Exception as e:
        logging.error("An error has occurred while calculating {} returns: {}".format(asset_name, repr(e)))


def get_forex_returns_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    forex_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["forex"]["t1"]["asset_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_forex_returns)
    logging.info("Count for forex returns : {}".format(forex_t_1.count()))
    logging.info("Completed spark read")
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["forex"]["t1"]["asset_folder"]),"id",lowerbound_ts,upperbound_ts)
    forex_t_1 = forex_t_1.filter(~col("id").isin(delete_record))
    forex_t_1 = forex_t_1.filter(col("updated")<=cut_off_time)
    save_de_duped_asset_t_1_to_s3(forex_t_1,
                                  config_data["forex"]["de_dupe_t_1"]["bucket"],
                                  config_data["forex"]["de_dupe_t_1"]["asset_folder"],
                                  config_data["forex"]["de_dupe_t_1"]["files_folder"],
                                  get_date(config_data["offset"]),
                                  config_data["forex"]["asset_name"],
                                  config_data["forex"]["primary_keys"], config_data["forex"]["column_order"])
    forex_t_2 = get_asset_t_2(config_data["forex"]["t2"]["bucket"],
                              config_data["forex"]["t2"]["asset_folder"],
                              config_data["forex"]["t2"]["files_folder"],
                              get_date(config_data["offset"]+1),
                              schema_for_forex_returns,
                              config_data["forex"]["asset_name"])
    logging.info("Merging T1 and T2 Data")
    forex_t_2 = forex_t_2.filter(~col("id").isin(delete_record))
    forex_t_0 = get_asset_t_0(forex_t_1,
                              forex_t_2,
                              config_data["forex"]["primary_keys"],
                              config_data["forex"]["asset_name"], config_data["forex"]["column_order"])
    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(forex_t_0,
                         config_data["forex"]["t2"]["bucket"],
                         config_data["forex"]["t2"]["asset_folder"],
                         config_data["forex"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["forex"]["asset_name"])
    forex_t_0 = drop_unnecessary_columns(forex_t_0,
                                         config_data["forex"]["list_of_unnecessary_columns"])
    return forex_t_0


def start_processing():
    logging.info("Starting execution for forex Snapshotting")
    forex_returns_t_0 = get_forex_returns_data()

    current_forex_prices = get_current_forex_price(config_data["forex"]["partner_id"],
                                                   config_data["forex"]["forex_id"],
                                                   config_data["forex"]["asset_name"])
    current_forex_prices = current_forex_prices.select(col("forex_id").alias("partner_forex_id"),
                                                       col("mid_price").alias("partner_mid_price"))

    logging.info("Calculation cost and value using current price")
    forex_returns_t_0 = calculate_forex_returns(forex_returns_t_0, current_forex_prices,
                                                config_data["forex"]["asset_name"])

    logging.info("Updating calculated values to s3")
    save_calculated_asset_returns_t_0_to_s3(forex_returns_t_0,
                                            config_data["forex"]["snap"]["bucket"],
                                            config_data["forex"]["snap"]["asset_folder"],
                                            config_data["forex"]["snap"]["files_folder"],
                                            get_date(config_data["offset"]),
                                            config_data["forex"]["asset_name"])


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--cut_off_time", help="value same as config")
    parser.add_argument("--offset", help="offset for price cutoff")
    args = parser.parse_args()
    cut_off_time_config = job_config_data["cut_off_time"][args.cut_off_time] if args.cut_off_time else job_config_data["cut_off_time"]["0000JKT"]
    config_data["offset"] = cut_off_time_config["offset"]
    if args.offset:
        config_data["offset"]= int(args.offset)
    cut_off_time = get_cutoff_time(cut_off_time_config)
    start_time = datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_forex_returns")

