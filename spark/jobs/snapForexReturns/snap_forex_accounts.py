from common import *
from structs import *


raw_bucket = config_data["forex"]["raw_bucket"]
spark = spark_session_create("snap_forex_accounts")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()
cut_off_time=None

def get_forex_accounts_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    forex_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["forex"]["t1"]["asset_accounts_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_forex_accounts)
    logging.info("Count for forex accounts : {}".format(forex_t_1.count()))
    logging.info("Completed spark read")
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["forex"]["t1"]["asset_accounts_folder"]),"id",lowerbound_ts,upperbound_ts)
    forex_t_1 = forex_t_1.filter(~col("id").isin(delete_record))
    forex_t_1 = forex_t_1.filter(col("updated")<=cut_off_time)
    save_de_duped_asset_t_1_to_s3(forex_t_1,
                                  config_data["forex"]["de_dupe_t_1"]["bucket"],
                                  config_data["forex"]["de_dupe_t_1"]["asset_accounts_folder"],
                                  config_data["forex"]["de_dupe_t_1"]["files_folder"],
                                  get_date(config_data["offset"]),
                                  config_data["forex"]["asset_name"],
                                  config_data["forex"]["primary_keys"], config_data["forex"]["accounts_column_order"])
    forex_t_2 = get_asset_t_2(config_data["forex"]["t2"]["bucket"],
                              config_data["forex"]["t2"]["asset_accounts_folder"],
                              config_data["forex"]["t2"]["files_folder"],
                              get_date(config_data["offset"]+1),
                              schema_for_forex_accounts,
                              config_data["forex"]["asset_name"])
    forex_t_2 = forex_t_2.filter(~col("id").isin(delete_record))
    logging.info("Merging T1 and T2 Data")
    forex_t_0 = get_asset_t_0(forex_t_1,
                              forex_t_2,
                              config_data["forex"]["primary_keys"],
                              config_data["forex"]["asset_name"], config_data["forex"]["accounts_column_order"])
    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(forex_t_0,
                         config_data["forex"]["t2"]["bucket"],
                         config_data["forex"]["t2"]["asset_accounts_folder"],
                         config_data["forex"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["forex"]["asset_name"])
    return forex_t_0


def start_processing():
    logging.info("Starting execution for forex Snapshotting")
    get_forex_accounts_data()


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--cut_off_time", help="cutoff time according to job")
    args = parser.parse_args()
    cut_off_time_config = job_config_data["cut_off_time"][args.cut_off_time] if args.cut_off_time else job_config_data["cut_off_time"]["0000JKT"]
    config_data["offset"] = cut_off_time_config["offset"]
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    cut_off_time = get_cutoff_time(cut_off_time_config)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_forex_accounts")
   

