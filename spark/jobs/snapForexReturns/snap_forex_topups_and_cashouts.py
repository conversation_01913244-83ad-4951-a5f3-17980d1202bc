from common import *
from structs import *
from config import *

spark = spark_session_create("snap_forex_topups_and_cashouts")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()
job_config = job_config_data["forex_cashout_and_topups"]
cut_off_time=None
t_1=None
t_2= None


def forex_topups(t_1,t_2):
    forex_topups_t_2 = read_csv_file("s3a://"+config_data["bucket"]+"/"+job_config["forex_top_ups_dedupe"] + "/dt=" + str(t_2) + "/", None, True, schema_for_forex_top_ups)
    forex_topups_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["forex_top_ups"], t_1), is_raw=True)
    delete_record = read_deleted_record("{}{}/dt=".format(config_data["bucket"],job_config["forex_top_ups"]),"id",lowerbound_ts,upperbound_ts)
    if forex_topups_t_1 is not None:
        forex_topups_t_1 = forex_topups_t_1.filter(~col("id").isin(delete_record))
        forex_topups_t_1 = forex_topups_t_1.filter(col("updated")<=cut_off_time)
    forex_topups_t_2 = forex_topups_t_2.filter(~col("id").isin(delete_record))
    forex_topups = get_union_and_de_dupe(forex_topups_t_1, forex_topups_t_2, ["id","account_id"], "updated")
    forex_topups.coalesce(1).write.mode('overwrite').save("s3a://"+config_data["bucket"]+"/"+job_config["forex_top_ups_dedupe"] + "/dt=" +str(t_1)+"/",format="csv", header=True)

def forex_cashouts(t_1,t_2):
    forex_cashouts_t_2 = read_csv_file("s3a://"+config_data["bucket"]+"/"+job_config["forex_cash_outs_dedupe"] + "/dt=" + str(t_2) + "/", None, True, schema_for_forex_cash_outs)
    forex_cashouts_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["forex_cash_outs"], t_1), is_raw=True)
    delete_record = read_deleted_record("{}{}/dt=".format(config_data["bucket"],job_config["forex_cash_outs"]),"id",lowerbound_ts,upperbound_ts)
    if forex_cashouts_t_1 is not None:
        forex_cashouts_t_1 = forex_cashouts_t_1.filter(~col("id").isin(delete_record))
        forex_cashouts_t_1 = forex_cashouts_t_1.filter(col("updated")<=cut_off_time)
    forex_cashouts_t_2 = forex_cashouts_t_2.filter(~col("id").isin(delete_record))
    forex_cashouts = get_union_and_de_dupe(forex_cashouts_t_1, forex_cashouts_t_2, ["id","account_id"], "updated")
    forex_cashouts.coalesce(1).write.mode('overwrite').save("s3a://"+config_data["bucket"]+"/"+job_config["forex_cash_outs_dedupe"] + "/dt=" +str(t_1)+"/",format="csv", header=True)

def start_processing():
    logging.info("Starting execution for cashouts Snapshotting")
    offset = config_data["offset"]
    t_1 = get_date(offset)
    t_2 = get_date(offset+1)
    forex_topups(t_1,t_2)
    forex_cashouts(t_1,t_2)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--cut_off_time", help="cutoff time according to job")
    args = parser.parse_args()
    cut_off_time_config = job_config_data["cut_off_time"][args.cut_off_time] if args.cut_off_time else job_config_data["cut_off_time"]["0000JKT"]
    config_data["offset"] = cut_off_time_config["offset"]
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    cut_off_time = get_cutoff_time(cut_off_time_config)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_cashouts")



