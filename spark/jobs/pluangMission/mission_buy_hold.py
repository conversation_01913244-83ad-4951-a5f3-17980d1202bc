from common import *
from structs import *
from config import *

spark = spark_session_create("mission_buy_hold")

def check_if_already_published(t_1, job_config):
    flag = False
    try:
        published_events_s3_path = "s3a://{}/{}/dt={}/".format(config_data['bucket'], job_config['kafka_published_events_s3_path'], t_1)
        logging.info("reading published data from path {}".format(published_events_s3_path))
        df = spark.read.json(published_events_s3_path)
        cnt = df.count()
        logging.info("already published {} msgs".format(cnt))
        if cnt > 0:
            logging.info("msgs are already published")
            flag = True
    except Exception as e:
        logging.info("msgs are not published yet")
    return flag


def publish_kafka_events(df, job_config, t_1):
    already_published = check_if_already_published(t_1, job_config)
    logging.info("Checking if data is already published for {}".format(t_1))
    if already_published is False:
        logging.info("Compared data is not published yet for {}".format(t_1))
        logging.info("publishing pluang-missions-events in kafka topic")
        zone = pytz.timezone("UTC")
        current_timestamp = datetime.now(tz=zone)
        df = df.filter((col("eventStatus") == "complete") | (col("eventStatus") == "fail") | (col("requiredAUMAmount") > col("current_aum_amount")))
        df = df.withColumn("eventId", f.expr("uuid()"))
        df = df.withColumn("published", lit(current_timestamp))
        df = df.withColumn("type", lit("user"))
        df = df.withColumn("id", col("userId"))
        df = df.withColumn("data", lit(None))
        df = df.withColumn("data", f.struct(["data"]))
        df = df.withColumn("actor", f.struct(["type", "id", "data"]))
        df = df.withColumn("type", lit("mission_holding_period"))
        df = df.withColumn("id", col("missionId"))
        df = df.withColumn("userActionId", col("userActionId"))
        df = df.withColumn("warning", when((col("eventStatus") == "fail"), False).otherwise(True))
        df = df.withColumn("warning", when((col("eventStatus") == "complete"), None).otherwise(col("warning")))
        df = df.withColumn("data", f.struct(["userActionId", "warning"]))
        df = df.withColumn("object", f.struct(["type", "id", "data"]))
        df = df.withColumn("type", lit("account"))
        df = df.withColumn("id", col("accountId"))
        df = df.withColumn("data", lit(None))
        df = df.withColumn("data", f.struct(["data"]))
        df = df.withColumn("target", f.struct(["type", "id", "data"]))
        df = df.withColumn("verb", when((col("eventStatus") == "complete"), "complete").otherwise("fail"))
        df = df.withColumn("x-request-id", f.expr("uuid()"))
        df = df.withColumn("loggerContext", f.struct(["x-request-id"]))
        df = df.withColumn("retryOnFailure", lit(True))
        df = df.withColumn("retryOnTimeout", lit(True))
        df = df.withColumn("retryCountOnFailure", lit(5))
        df = df.withColumn("retryCountOnTimeout", lit(2))
        df = df.withColumn("timeout", lit(30))
        df = df.withColumn("config", f.struct(["retryOnFailure", "retryOnTimeout", "retryCountOnFailure", "retryCountOnTimeout", "timeout"]))
        cols_to_publish = ["userId", "x-request-id", "eventId", "published", "actor", "object", "target", "verb", "loggerContext", "config"]
        df = df.select(cols_to_publish)
        cols_to_publish.remove("userId")
        cols_to_publish.remove("x-request-id")
        df = df.select(col("userId").cast(StringType()).alias("key"), f.to_json(f.struct(cols_to_publish)).alias("value"), f.array(f.struct(lit("x-request-id").alias("key"), col("x-request-id").cast("binary").alias("value"))).alias("headers"))
        write_data_in_kafka_topic(df, config_data["bootstrap_servers"], job_config["kafka_topic"])
        logging.info("Compared events are successfully published")
        logging.info("Writing published events in s3")
        df.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["kafka_published_events_s3_path"], t_1))
        logging.info("published events are successfully written to s3")
    else:
        logging.info("data is already published for pluang-missions-events for date {}".format(t_1))


def getAUM(t_1, asset_class):
    if asset_class.get("offset") is not None:
        t_1 = get_date(asset_class.get("offset"))
    asset_aum_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], asset_class["aum_s3_path"], t_1)
    logging.info("Reading AUM data from path : {}".format(asset_aum_path))
    df_asset_aum = read_csv_file(asset_aum_path, None, False, None)
    logging.info("Filtering AUM for {}".format(asset_class["asset_sub_type"]))
    df_asset_aum = df_asset_aum.filter((col("partner_id") == 1000002) & (col("asset_subtype").isin(asset_class["asset_sub_type"])))
    exclude_ids = asset_class.get("exclude_ids")
    if exclude_ids is not None:
        df_asset_aum = df_asset_aum.filter(~(col("product_id").isin(exclude_ids)))
    df_asset_aum = df_asset_aum.withColumn("current_aum_amount", col("quantity")*col("product_mid_price"))
    df_asset_aum = df_asset_aum.groupBy(["account_id", "user_id"]).agg(sum("current_aum_amount").alias("current_aum_amount"))
    logging.info("reading AUM is successful")
    return df_asset_aum


def compare_aum(df_to_compare, job_config, t_1):
    active_asset_class = job_config["active_asset_class"]
    df_compared = None
    for asset_class in active_asset_class:
        logging.info("Starting AUM comparision for {}".format(asset_class["asset_type"]))
        df_to_compare_asset = df_to_compare.filter(col("aumAssetClass") == asset_class["asset_type"])
        df_aum = getAUM(t_1, asset_class)
        joined_df = df_to_compare_asset.join(df_aum, (df_to_compare_asset["accountId"] == df_aum["account_id"]) & (df_to_compare_asset["userId"] == df_aum["user_id"]), "full")
        joined_df = joined_df.filter(col("missionId").isNotNull())
        joined_df = joined_df.fillna(0)
        joined_df = joined_df.withColumn("requiredAUMAmount", col("aumAmount")-col("aumAmount")*job_config["grace_percentage"]/100)
        joined_df = joined_df.withColumn("violationCount", when((col("requiredAUMAmount") > col("current_aum_amount")), col("violationCount")+1).otherwise(col("violationCount")))
        zone = pytz.timezone("UTC")
        updated_at = datetime.now(tz=zone)
        joined_df = joined_df.withColumn("updatedAt", lit(updated_at))
        joined_df = joined_df.withColumn("eventStatus", when(((t_1 == col("missionEndDt")) & (col("violationCount") <= job_config["grace_period"])), "complete").when((col("violationCount") > job_config["grace_period"]), "fail").otherwise("active"))
        if df_compared is None:
            df_compared = joined_df
        else:
            df_compared = df_compared.union(joined_df)
        logging.info("Completed AUM comparision for {}".format(asset_class["asset_type"]))
    return df_compared


def filter_active_mission_trigger_events(df, job_config, t_1):
    df = df.filter((col("eventStatus") == "active") & (col("violationCount") <= job_config["grace_period"]) & (col("missionEndDt") >= t_1))
    return df


def de_dupe_mission_trigger_events(t_1, t_2, job_config):
    t_2_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["snapshot_folder"], t_2)
    t_1_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["t_1_folder"], t_2)
    t_0_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["t_1_folder"], t_1)
    logging.info("Reading t-2 data from path: {}".format(t_2_path))
    df_t_2 = read_csv_file(t_2_path, None, False, None)
    df_t_2 = df_t_2.withColumn("updatedAt", col("updatedAt").cast(TimestampType()))
    df_delta = None
    logging.info("Reading t-1 data from path: {}".format(t_1_path))
    df_t_1 = read_json_data(t_1_path)
    logging.info("Reading t-0 data from path: {}".format(t_0_path))
    df_t_0 = read_json_data(t_0_path)
    if (df_t_1 is not None) & (df_t_0 is not None):
        df_delta = df_t_1.union(df_t_0)
    elif df_t_1 is not None:
        df_delta = df_t_1
    else:
        df_delta = df_t_0
    if df_delta is None:
        logging.info("No data is received from kafka at {}".format(t_1))
        df_union = df_t_2
    else:
        df_delta = df_delta.select("value.*")
        df_delta = df_delta.drop("loggerContext")
        df_delta = df_delta.withColumn("accountId", col("accountId").cast(LongType())) \
            .withColumn("aumAmount", col("aumAmount").cast(DoubleType())) \
            .withColumn("aumHoldingPeriodDays", col("aumHoldingPeriodDays").cast(IntegerType())) \
            .withColumn("clientId", col("clientId").cast(LongType())) \
            .withColumn("missionId", col("missionId").cast(LongType())) \
            .withColumn("partnerId", col("partnerId").cast(LongType())) \
            .withColumn("userActionId", col("userActionId").cast(LongType())) \
            .withColumn("userId", col("userId").cast(LongType())) \
            .withColumn("eventStatus", lit("active")) \
            .withColumn("updatedAt", col("startDate").cast(TimestampType())) \
            .withColumn("violationCount", lit(0)) \
            .withColumn("tempDate", col("startDate").cast(DateType())) \
            .withColumn("missionEndDt", f.expr("date_add(tempDate, aumHoldingPeriodDays)")) \
            .drop("tempDate")
        t_2_cols = df_t_2.columns
        df_delta = df_delta.select(t_2_cols)
        df_union = df_t_2.union(df_delta)
    logging.info("Starting de dupe operation")
    df = de_dupe_dataframe(df_union, ["accountId", "userId", "missionId", "userActionId"], "updatedAt")
    logging.info("de dupe operation is successful")
    return df


def start_processing():
    logging.info("Starting Execution of Mission Buy Hold Trigger Event Processing")
    job_config = job_config_data["mission_buy_hold_calculation"]
    offset = config_data["offset"]
    t_1 = get_date(offset)
    t_2 = get_date(offset+1)
    logging.info("Starting de dupe operation of T-1 raw data with yesterday's data")
    df = de_dupe_mission_trigger_events(t_1, t_2, job_config)
    logging.info("De dupe is successful")
    logging.info("Filtering active mission buy hold events")
    df_to_compare = filter_active_mission_trigger_events(df, job_config, t_1)
    logging.info("Starting AUM comparison with mission buy hold events")
    df_compared = compare_aum(df_to_compare, job_config, t_1)
    logging.info("AUM Comparison is completed")
    if (df_compared is not None) and (df_compared.count() > 0):
        logging.info("There are events which is either completed or violated the rule")
        logging.info("Starting kafka msg publish")
        publish_kafka_events(df_compared, job_config, t_1)
        logging.info("merging compared data with current snapshot")
        df_compared_for_de_dupe = df_compared.select(["userId", "clientId", "partnerId", "accountId", "missionId", "userActionId", "aumAmount", "aumCurrency", "aumHoldingPeriodDays", "aumAssetClass", "startDate", "eventStatus", "updatedAt", "violationCount", "missionEndDt"])
        df_cols = df.columns
        df_compared_for_de_dupe = df_compared_for_de_dupe.select(df_cols)
        df_union = df.union(df_compared_for_de_dupe)
        df = de_dupe_dataframe(df_union, ["accountId", "userId", "missionId", "userActionId"], "updatedAt")
    logging.info("writing updated events to s3")
    df.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], job_config["snapshot_folder"], t_1), header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"mission_buy_hold")
