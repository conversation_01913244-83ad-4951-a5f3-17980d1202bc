from common import *
from structs import *
from config import *


bucket_name = config_data["bucket"]
accounts_config = job_config_data["accounts"]
spark = spark_session_create("snap_accounts")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()


def start_processing():
    logging.info("Starting snapshoting of accounts")
    t_2 = get_date(config_data["offset"]+1)
    t_1 = get_date(config_data["offset"])
    accounts_t_2_path = "s3a://{}/{}/dt={}/".format(bucket_name, accounts_config["accounts_t2_path"], t_2)
    logging.info("reading T-2 accounts from {}".format(accounts_t_2_path))
    prev_df = read_csv_file(accounts_t_2_path, None, False, None)
    accounts_t_1_path = "s3a://{}/{}/dt={}/".format(bucket_name, accounts_config["accounts_t_1_path"], t_1)
    logging.info("reading T-1 accounts from {}".format(accounts_t_1_path))
    current_df = read_json_data(accounts_t_1_path)
    union_df = prev_df
    if current_df is not None:
        logging.info("accounts which are updated today are {}".format(current_df.count()))
        current_df = current_df.select("value.*")
        cols = union_df.columns
        union_df_fields = union_df.schema.fields
        for field in union_df_fields:
            current_df = current_df.withColumn(field.name, col(field.name).cast(field.dataType))
        current_df = current_df.select(cols)
        union_df = prev_df.union(current_df)
    logging.info("de duping accounts")
    union_df = de_dupe_dataframe(union_df, ["id"], "updated")
    delete_record = read_deleted_record("s3a://{}/{}/dt=".format(bucket_name, accounts_config["accounts_t_1_path"]), "id",lowerbound_ts,upperbound_ts)
    union_df = union_df.filter(~col("id").isin(delete_record))
    accounts_write_path = "s3a://{}/{}/dt={}/".format(bucket_name, accounts_config["accounts_t2_path"], t_1)
    logging.info("writing updated accounts in s3 on path {}".format(accounts_write_path))
    save_asset_t_0_to_s3(union_df, bucket_name, accounts_config["accounts_t2_path"], "", t_1, "snap_accounts")


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_accounts")

