from common import *
from config import *
import boto3
from io import BytesIO
import gzip


spark = spark_session_create("cleavertap_profile")

def move_gz_files(s3_client, s3_upload_client, t_1):
    dest_bucket = config_data["bucket"]
    source_bucket = config_data["clevertap_profile"]["raw_bucket"]
    objects_to_copy = s3_client.list_objects_v2(Bucket=source_bucket)
    if objects_to_copy.get('Contents') is not None:
        for obj in objects_to_copy['Contents']:
            file_name = obj['Key']
            copy_source = {'Bucket': source_bucket, 'Key': file_name}
            s3_upload_client.upload_fileobj(
                Fileobj=gzip.GzipFile(
                    None,
                    'rb',
                    fileobj=BytesIO(
                        s3_client.get_object(Bucket=source_bucket, Key=file_name)[
                            'Body'].read())),
                Bucket=dest_bucket,
                Key="{}/{}/dt={}/{}".format(job_config_data["clevertap_profile"]["datalake_folder"], job_config_data["clevertap_profile"]["t_1_folder"], t_1, file_name.replace(".gz", ""))
            )
            s3_client.delete_object(Bucket=source_bucket, Key=file_name)


def start_processing():
    spark = SparkSession.builder.appName("cleaverTap profile").getOrCreate()
    t_1 = get_date(job_config_data["offset"])
    t_2 = get_date(job_config_data["offset"] + 1)
    t_3 = get_date(job_config_data["offset"] + 2)
    s3_client = boto3.client('s3', use_ssl=False, region_name=config_data["clevertap_profile"]["source_region"])
    s3_upload_client = boto3.client('s3', use_ssl=False, region_name=config_data["clevertap_profile"]["dest_region"])
    move_gz_files(s3_client, s3_upload_client, t_1)
    df = read_parque_file("s3a://{}/{}/{}/dt={}/*.parquet".format(config_data["bucket"],
                                                                  job_config_data["clevertap_profile"]["datalake_folder"],
                                                                  job_config_data['clevertap_profile']["t_1_folder"],
                                                                  str(t_1)))

    prev_df = read_parque_file("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"],
                                                              job_config_data["clevertap_profile"]["datalake_folder"],
                                                              job_config_data['clevertap_profile']["t_2_folder"],
                                                              str(t_2)))
    logging.info("Total No of Records in t_2 is {}".format(prev_df.count()))
    if df is not None:
        df = df.withColumn("filename", f.input_file_name())
        df = df.withColumn("ts", f.split("filename", "/")[7])
        df = df.withColumn("ts", f.split("ts", "\.")[0])
        df = df.drop("filename")
        logging.info("Total No of Records in t_1 is {}".format(df.count()))
        current_df = prev_df.union(df)
    else:
        logging.info("No files found in t_1")
        current_df = prev_df
    current_df = de_dupe_dataframe(current_df, ["identity"], "ts")
    current_df.coalesce(5).write.mode('overwrite').parquet("s3a://{}/{}/{}/dt={}/".format(config_data["bucket"],
                                                                                          job_config_data["clevertap_profile"]["datalake_folder"],
                                                                                          job_config_data['clevertap_profile']["t_2_folder"],
                                                                                          str(t_1)))
    logging.info("Total No of Records after de dupe is {}".format(current_df.count()))
    prev_folder_to_delete = "{}/{}/dt={}".format(job_config_data["clevertap_profile"]["datalake_folder"], job_config_data['clevertap_profile']["t_2_folder"], t_3)
    logging.info("delete {} folder".format(prev_folder_to_delete))
    boto3.resource('s3', region_name=config_data["clevertap_profile"]["dest_region"]).Bucket(config_data["bucket"]).objects.filter(Prefix=prev_folder_to_delete).delete()


if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time =datetime.now()
    job_time_metrics(start_time,end_time,"cleavertap_profile")
    