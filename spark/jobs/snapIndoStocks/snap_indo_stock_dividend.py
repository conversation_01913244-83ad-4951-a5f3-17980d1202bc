from common import *
from structs import *


raw_bucket = config_data["indo_stock"]["raw_bucket"]
spark = spark_session_create("snap_indo_stock_dividend")

lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()

def get_indo_stock_accounts_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    indo_stock_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["indo_stock"]["t1"]["asset_accounts_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_indo_stock_accounts)
    logging.info("Count for indo stock accounts : {}".format(indo_stock_t_1.count()))
    logging.info("Completed spark read")
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["indo_stock"]["t1"]["asset_accounts_folder"]),"id",lowerbound_ts,upperbound_ts)
    indo_stock_t_1 = indo_stock_t_1.filter(~col("id").isin(delete_record))
    logging.info("Reading t2 file")
    indo_stock_t_2 = get_asset_t_2(config_data["indo_stock"]["t2"]["bucket"],
                                   config_data["indo_stock"]["t2"]["asset_accounts_folder"],
                                   config_data["indo_stock"]["t2"]["files_folder"],
                                   get_date(config_data["offset"]+1),
                                   schema_for_indo_stock_accounts,
                                   config_data["indo_stock"]["asset_name"])

    indo_stock_t_2 = indo_stock_t_2.filter(~col("id").isin(delete_record))
    logging.info("Merging to get latest data")
    indo_stock_t_0 = get_asset_t_0(indo_stock_t_1,
                                   indo_stock_t_2,
                                   config_data["indo_stock"]["primary_keys"],
                                   config_data["indo_stock"]["asset_name"],
                                   config_data["indo_stock"]["accounts_column_order"])

    save_asset_t_0_to_s3(indo_stock_t_0,
                         config_data["indo_stock"]["t2"]["bucket"],
                         config_data["indo_stock"]["t2"]["asset_accounts_folder"],
                         config_data["indo_stock"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["indo_stock"]["asset_name"])

    return indo_stock_t_0


def get_indo_stock_returns_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    indo_stock_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["indo_stock"]["t1"]["asset_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_indo_stock_returns)
    logging.info("Count for indo stock returns : {}".format(indo_stock_t_1.count()))
    logging.info("Completed spark read")
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["indo_stock"]["t1"]["asset_folder"]),"id",lowerbound_ts,upperbound_ts)
    indo_stock_t_1 = indo_stock_t_1.filter(~col("id").isin(delete_record))
    logging.info("Reading t2 file")
    indo_stock_t_2 = get_asset_t_2(config_data["indo_stock"]["t2"]["bucket"],
                                   config_data["indo_stock"]["t2"]["asset_folder"],
                                   config_data["indo_stock"]["t2"]["files_folder"],
                                   get_date(config_data["offset"]),
                                   schema_for_indo_stock_returns,
                                   config_data["indo_stock"]["asset_name"])

    indo_stock_t_2 = indo_stock_t_2.filter(~col("id").isin(delete_record))
    logging.info("Merging to get latest data")
    indo_stock_t_0 = get_asset_t_0(indo_stock_t_1,
                                   indo_stock_t_2,
                                   config_data["indo_stock"]["primary_keys"],
                                   config_data["indo_stock"]["asset_name"],
                                   config_data["indo_stock"]["column_order"])

    return indo_stock_t_0


def start_processing():
    logging.info("Starting execution for indo stocks Snapshotting")
    indo_stock_accounts_t_0 = get_indo_stock_accounts_data()
    indo_stock_returns_t_0 = get_indo_stock_returns_data()

    indo_stock = indo_stock_returns_t_0.join(indo_stock_accounts_t_0, (
            (indo_stock_accounts_t_0.account_id == indo_stock_returns_t_0.account_id) & (
            indo_stock_accounts_t_0.stock_id == indo_stock_returns_t_0.stock_id)),
                                             "inner").drop(indo_stock_accounts_t_0.account_id).drop(
        indo_stock_accounts_t_0.created).drop(indo_stock_accounts_t_0.updated).drop(
        indo_stock_accounts_t_0.id).drop(indo_stock_accounts_t_0.stock_id).drop(
        indo_stock_accounts_t_0.user_id)

    logging.info("Filtering data to get accounts with total value > 0")
    indo_stock = indo_stock.withColumn("total_quantity",col("total_quantity").cast("double"))
    indo_stock = indo_stock.where((indo_stock.total_quantity > 0))
    indo_stock = indo_stock.withColumn("_stock_id",col("stock_id"))

    save_calculated_asset_returns_t_0_to_s3_split_by_column(indo_stock,
                                                            config_data["indo_stock"]["snap"]["bucket"],
                                                            config_data["indo_stock"]["snap"]["balance_asset_folder"],
                                                            config_data["indo_stock"]["snap"]["files_folder"],
                                                            get_date(config_data["offset"]),
                                                            config_data["indo_stock"]["asset_name"],"_stock_id")
    logging.info("Completed indo stock snapshotting")


if __name__ == "__main__":
    start_time= datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_indo_stock_dividend")

