from common import *
from trading_competition_config import *
from trading_competition_common import *


class PortfolioSummaryReport:
    def __init__(self, utc_cutoff_ts=None):
        self.spark = spark_session_create("portfolio_summary_report")
        self.bucket = config_data["bucket"]
        self.batch_path = "s3a://{}/{}".format(self.bucket, job_config_data['batches']['batch_file_path'])
        self.tier_snapshot_path = "s3a://{}/{}".format(self.bucket, job_config_data['aum_tier_upgrade']['tier_snapshot_path'])
        self.partner_id = job_config_data["partner_id"]
        self.usdt_coin_id = job_config_data["usdt_coin_id"]
        self.utc_cutoff_ts = utc_cutoff_ts or datetime.now(pytz.timezone("UTC"))
        self.trading_competition_start_time = get_utc_timestamp_from_string(config_data["trading_competition_start_time"])
        self.portfolio_summary_mongo_collection = job_config_data["portfolio_summary_report"]["mongo_collection"]
        self.portfolio_summary_report_s3_path = job_config_data["portfolio_summary_report"]["s3_path"]
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = get_dates_and_timestamps(self.utc_cutoff_ts)
        self.crypto_currencies_topic = config_data["kafka_topics"]["crypto_currencies_topic"]
        self.global_stocks_topic = config_data["kafka_topics"]["global_stock_topic"]
        self.snapshot_path = "s3a://{}/{}/".format(self.bucket, job_config_data['snapshot_path'])
        logging.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))

    def get_forex_and_usdt_price(self):
        forex_partner_price = self.spark.read.json("s3a://{}/{}/dt={}/hour={}/".format(self.bucket, job_config_data['prices']['forex']['price_path'], self.t_1, self.h_1))
        forex_partner_price = forex_partner_price.filter((col("forex_id") == 10000) & (col("partner_id") == self.partner_id))
        forex_price = int(forex_partner_price.collect()[0]["mid_price"])

        crypto_currency_price = read_csv_file("s3a://{}/{}/dt={}/hour={}/".format(self.bucket, job_config_data['prices']['crypto_currency']['price_path'], self.t_1, self.h_1), None, False, None)
        usdt_price_df = crypto_currency_price.filter(col("_id") == self.usdt_coin_id)
        usdt_price = int(usdt_price_df.collect()[0]["mid_price"])

        return forex_price, usdt_price

    def get_crypto_currencies(self):
        logging.info("Reading Crypto codes from kafka")
        crypto_currencies = get_asset_data("crypto_currencies_topic", "kafka").select(col("id").alias("asset_id"), col("symbol").alias("asset_code"))
        logging.info("Reading Crypto codes from kafka is successful")
        return crypto_currencies

    def get_global_stocks(self):
        logging.info("reading global stock codes from kafka topic")
        global_stocks = get_asset_data("global_stock_topic", "kafka").select(col("id").alias("asset_id"), col("pluang_company_code").alias("asset_code"))
        logging.info("successfully read global stock codes from kafka")
        return global_stocks

    def get_crypto_futures(self):
        logging.info("reading crypto future instruments from kafka topic")
        crypto_futures = get_asset_data("crypto_future_instruments_topic", "kafka").select(col("id").alias("asset_id"), col("future_pair_symbol").alias("asset_code"))
        logging.info("successfully read global stock codes from kafka")
        return crypto_futures

    def get_options_contracts(self, global_stocks):
        logging.info("reading global stock options contracts")
        global_stocks = global_stocks.select(col("asset_id").alias("global_stock_id"), col("asset_code").alias("global_stock_code"))
        options_contracts = read_csv_file("{}{}/dt={}/hour={}/".format(self.snapshot_path, "options_contracts", self.t_1, self.h_1), None, False, None)
        options_contracts = options_contracts.join(global_stocks, on=["global_stock_id"], how="left")
        options_contracts = options_contracts.withColumnRenamed("id", "asset_id")
        options_contracts = options_contracts.withColumn("expiration_date", col("expiration_date").cast(IntegerType()))
        options_contracts = options_contracts.withColumn("expiration_date", f.expr("date_add(to_date('1970-01-01'), expiration_date)").cast(DateType()))
        options_contracts = options_contracts.withColumn("expiration_date", (f.date_format(col("expiration_date"), "yyMMdd")))
        options_contracts = options_contracts.withColumn("strike_price_symbol", lit("$"))
        options_contracts = options_contracts.withColumn("strike_price", f.concat("strike_price_symbol", "strike_price")).drop("strike_price_symbol")
        options_contracts = options_contracts.withColumn("asset_code", f.concat(col("global_stock_code"), lit(" "), col("contract_type"), lit(" "), col("expiration_date"), lit(" "), col("strike_price")))
        return options_contracts.select("asset_id", "asset_code", "shares_per_contract")

    def get_user_tiers(self):
        tier = read_csv_file("{}/dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1,self.h_1), None, False, None)
        tier = tier.select("account_id", "user_id", "tier", "opt_in_time") \
            .withColumn("opt_in_time", col("opt_in_time").cast(TimestampType()))
        return tier

    def create_portfolio_summary_for_asset(self, report, keys):
        report = report.groupBy(keys) \
            .agg(
            f.sum("normalized_quantity").alias("quantity"),
            f.sum("unrealized_pnl").alias("unrealized_pnl"),
            f.sum("realized_pnl").alias("realized_pnl"),
            f.first("current_unit_price").alias("closing_price")
        )
        report = report.withColumn("quantity", round(col("quantity"), 12))
        report = report.withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType()))
        report = report.withColumn("pnl", (col("realized_pnl") + col("unrealized_pnl")).cast(LongType()))
        return report

    def group_assets(self, report, key):
        report = report.withColumnRenamed("asset_id", "assetId") \
            .withColumnRenamed("asset_code", "assetCode") \
            .withColumnRenamed("unrealized_pnl", "unrealizedPnl") \
            .withColumnRenamed("realized_pnl", "realizedPnl") \
            .withColumnRenamed("closing_price", "closingPrice") \
            .withColumnRenamed("market_value", "marketValue")
        if key == "crypto_futures":
            report = report.withColumn("side", when(col("quantity") < 0, "Short").otherwise("Long")) \
                .withColumn("quantity", f.abs(col("quantity"))) \
                .withColumn("marketValue", f.abs(col("marketValue"))) \
                .withColumnRenamed("closingPrice", "markPrice")
            report = report.withColumn(key, f.struct("assetId", "assetCode", "side", "quantity", "unrealizedPnl", "realizedPnl", "markPrice", "marketValue", "pnl"))
        elif key == "option_contracts":
            report = report.withColumn("isOption", lit(True))
            report = report.withColumn(key, f.struct("assetId", "assetCode", "isOption", "quantity", "unrealizedPnl", "realizedPnl", "closingPrice", "marketValue", "pnl"))
        else:
            report = report.withColumn(key, f.struct("assetId", "assetCode", "quantity", "unrealizedPnl", "realizedPnl", "closingPrice", "marketValue", "pnl"))
        return report

    def get_global_stock_report(self, txn):
        txn = txn.withColumn("executed_unit_price", (col("executed_unit_price")*col("currency_to_idr")).cast("long"))
        txn = txn.withColumn("current_unit_price", (col("current_unit_price")*col("current_currency_to_idr")).cast("long"))
        report = self.create_portfolio_summary_for_asset(txn, ["account_id", "asset_id", "leverage"])
        global_stocks = self.get_global_stocks()
        report = report.join(global_stocks, on=["asset_id"], how="left")

        report = report.withColumn("asset_code", when(((col("leverage") == 0) | (col("leverage").isNull())), col("asset_code")).otherwise(f.concat(col("asset_code"), lit(" ("), col("leverage"), lit("X)"))))

        pnl = report.groupBy(["account_id"]).agg(f.sum("pnl").alias("total_global_stock_pnl"))

        report = self.group_assets(report, "global_stock")

        report = report.groupBy("account_id").agg(f.collect_list("global_stock").alias("global_stock"))
        report = report.join(pnl, on=["account_id"], how="left").fillna({'total_global_stock_pnl': 0})
        return report, global_stocks

    def get_options_report(self, txn, global_stocks):
        txn = txn.withColumn("executed_unit_price", (col("executed_unit_price")*col("currency_to_idr")).cast("long"))
        txn = txn.withColumn("current_unit_price", (col("current_unit_price")*col("current_currency_to_idr")).cast("long"))
        report = self.create_portfolio_summary_for_asset(txn, ["account_id", "asset_id"])
        options_contracts = self.get_options_contracts(global_stocks)
        report = report.join(options_contracts, on=["asset_id"], how="left").fillna({'shares_per_contract': 100})
        report = report.withColumn("quantity", col("quantity")/col("shares_per_contract"))
        pnl = report.groupBy(["account_id"]).agg(f.sum("pnl").alias("total_option_contracts_pnl"))

        report = self.group_assets(report, "option_contracts")

        report = report.groupBy("account_id").agg(f.collect_list("option_contracts").alias("option_contracts"))
        report = report.join(pnl, on=["account_id"], how="left").fillna({'total_option_contracts_pnl': 0})
        return report

    def get_crypto_currency_report(self, txn):
        report = self.create_portfolio_summary_for_asset(txn, ["account_id", "asset_id"])
        crypto_currencies = self.get_crypto_currencies()
        report = report.join(crypto_currencies, on=["asset_id"], how="left")

        pnl = report.groupBy(["account_id"]).agg(f.sum("pnl").alias("total_crypto_currency_pnl"))
        report = self.group_assets(report, "crypto_currency")

        report = report.groupBy("account_id").agg(f.collect_list("crypto_currency").alias("crypto_currency"))
        report = report.join(pnl, on=["account_id"], how="left").fillna({'total_crypto_currency_pnl': 0})
        return report

    def get_crypto_futures_report(self, txn):
        txn = txn.withColumn("executed_unit_price", (col("executed_unit_price")*col("currency_to_idr")).cast("long"))
        txn = txn.withColumn("current_unit_price", (col("current_unit_price")*col("current_currency_to_idr")).cast("long"))
        report = self.create_portfolio_summary_for_asset(txn, ["account_id", "asset_id"])
        crypto_future_instruments = self.get_crypto_futures()
        report = report.join(crypto_future_instruments, on=["asset_id"], how="left")

        pnl = report.groupBy(["account_id"]).agg(f.sum("pnl").alias("total_crypto_futures_pnl"))
        report = self.group_assets(report, "crypto_futures")

        report = report.groupBy("account_id").agg(f.collect_list("crypto_futures").alias("crypto_futures"))
        report = report.join(pnl, on=["account_id"], how="left").fillna({'total_crypto_futures_pnl': 0})
        return report

    def get_gold_report(self, txn):
        report = self.create_portfolio_summary_for_asset(txn, ["account_id", "asset_id"])
        report = report.withColumn("asset_code", lit("GOLD"))

        pnl = report.groupBy(["account_id"]).agg(f.sum("pnl").alias("total_gold_pnl"))
        report = self.group_assets(report, "gold")

        report = report.groupBy("account_id").agg(f.collect_list("gold").alias("gold"))
        report = report.join(pnl, on=["account_id"], how="left").fillna({'total_gold_pnl': 0})
        return report

    def create_portfolio_summary_report(self, batches, tiers):
        logging.info("trading_competition_start_time: {}".format(self.trading_competition_start_time))
        buy_types = job_config_data["buy_types"]
        sell_types = job_config_data["sell_types"]
        batches = batches.filter((col("transaction_type").isin(buy_types)) | (col("transaction_type").isin(sell_types)))
        batches = batches.withColumn("normalized_quantity", when(col("transaction_type").isin(buy_types), col("updated_executed_quantity")).otherwise(-1*col("updated_executed_quantity")))
        batches = batches.select("account_id", "asset_id", "asset_type", "asset_sub_type", "executed_quantity", "normalized_quantity", "updated_executed_quantity",
                                 "executed_unit_price", "updated_executed_unit_price", "transaction_type", "current_unit_price", "current_currency_to_idr", "currency_to_idr", "realized_pnl", "unrealized_pnl", "leverage", "usdt_price", "forex_price")

        batches = batches.withColumn("realized_pnl", col("realized_pnl").cast(LongType())) \
            .withColumn("unrealized_pnl", col("unrealized_pnl").cast(LongType()))

        global_stock_report, global_stocks = self.get_global_stock_report(batches.filter((col("asset_type") == "global_stocks")))
        options_report = self.get_options_report(batches.filter((col("asset_type") == "global_stock_options")), global_stocks)
        crypto_currency_report = self.get_crypto_currency_report(batches.filter((col("asset_type") == "crypto_currency")))
        crypto_futures_report = self.get_crypto_futures_report(batches.filter((col("asset_type") == "crypto_futures")))
        gold_report = self.get_gold_report(batches.filter((col("asset_type") == "gold")))

        report = global_stock_report.join(crypto_currency_report, on=["account_id"], how="full") \
            .join(options_report, on=["account_id"], how="full") \
            .join(gold_report, on=["account_id"], how="full") \
            .join(crypto_futures_report, on=["account_id"], how="full") \
            .fillna({'total_crypto_currency_pnl': 0, 'total_global_stock_pnl': 0, 'total_option_contracts_pnl': 0, 'total_gold_pnl': 0, 'total_crypto_futures_pnl': 0}) \
            .withColumn("crypto_currency", when(col("crypto_currency").isNull(), f.array([])).otherwise(col("crypto_currency"))) \
            .withColumn("global_stock", when(col("global_stock").isNull(), f.array([])).otherwise(col("global_stock"))) \
            .withColumn("gold", when(col("gold").isNull(), f.array([])).otherwise(col("gold"))) \
            .withColumn("crypto_futures", when(col("crypto_futures").isNull(), f.array([])).otherwise(col("crypto_futures"))) \
            .withColumn("option_contracts", when(col("option_contracts").isNull(), f.array([])).otherwise(col("option_contracts")))

        report = report.withColumn("total_pnl", col("total_crypto_currency_pnl") + col("total_global_stock_pnl") + col("total_option_contracts_pnl") + col("total_gold_pnl") + col("total_crypto_futures_pnl")) \
            .withColumn("total_us_stocks_pnl", col("total_option_contracts_pnl") + col("total_global_stock_pnl"))

        report = report.join(tiers, on=["account_id"], how="left") \
            .withColumnRenamed("opt_in_time", "created_at")

        report = report.withColumn("execution_time", lit(self.utc_cutoff_ts))
        report = report.withColumn("competition_start_time", lit(self.trading_competition_start_time).cast(TimestampType()))
        report = report.withColumn("updated_at", lit(datetime.now(pytz.timezone("UTC"))))
        forex_price, usdt_price = self.get_forex_and_usdt_price()
        report = report.withColumn("forex_price", lit(forex_price)) \
            .withColumn("usdt_price", lit(usdt_price))
        return report

    def write_portfolio_report_in_mongo(self, report):
        mongo_write_config = {
            "uri": "{}.{}?authSource=admin".format(config_data["reporting_uri"], self.portfolio_summary_mongo_collection),
            "collection": self.portfolio_summary_mongo_collection,
            "batch_size": "500",
            "mode": "append"
        }

        write_asset_returns_to_mongo(report, mongo_write_config, "portfolio_summary_report", "update", "{'accountId':1}", add_created_at=False)

    def execute(self):
        batches = read_csv_file("{}/dt={}/hour={}".format(self.batch_path, self.t_1, self.h_1), None, False, None)
        tiers = self.get_user_tiers()
        portfolio_summary_report = self.create_portfolio_summary_report(batches, tiers)
        portfolio_summary_report.coalesce(1).write.mode('overwrite').json("s3a://{}/{}/dt={}/hour={}/".format(self.bucket, self.portfolio_summary_report_s3_path, self.t_1, self.h_1))
        self.write_portfolio_report_in_mongo(portfolio_summary_report)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--utc_cutoff_ts", help="UTC cutoff timestamp")
    args = parser.parse_args()
    utc_cutoff_ts = get_utc_timestamp_from_string(args.utc_cutoff_ts) if args.utc_cutoff_ts else None

    portfolioSummaryReport = PortfolioSummaryReport(utc_cutoff_ts)

    portfolioSummaryReport.execute()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "portfolio_summary_report")
