from pyspark.sql.functions import col, from_json, get_json_object
from pyspark.sql import DataFrame
from enum import Enum
from common import *
from trading_competition_structs import *
import trading_competition_config
from trading_competition_common import *

config = trading_competition_config.job_config_data
class DeDupeOnConstants(Enum):
    crypto_currency_wallet_transfers = "updated_at"
    trading_competition_user_events = "eventTime"

class DeDupeKeysConstants(Enum):
    bappebti_wallets = ["account_id", "user_id"]
    trading_competition_user_events = ["userId", "accountId"]

class TransactionFilterColumnConstants(Enum):
    crypto_future_trades = "traded_at_timestamp"
    crypto_margin_wallets = "updated"
    crypto_future_instruments = "updated"
    crypto_broker_margin_wallet_balances = "updated"
    bappebti_wallets = "updated"
    leverage_wallet_accounts = "updated"
    forex_accounts = "updated"

class CreatedFilterConstants(Enum):
    crypto_currency_wallet_transfers = "created_at"
    trading_competition_user_events = "eventTime"

class FlattenColumnConstants(Enum):
    crypto_future_trades = {"column_name": "fee", "required_column": "$.totalTradeFee", "rename_to": "total_trade_fee"}
    crypto_margin_wallets = {"column_name": "exchange_response", "required_column": "$.walletBalance", "rename_to": "wallet_balance"}


class SnapAssets:

    def __init__(self, utc_cutoff_ts, batch):
        self.utc_cutoff_ts = utc_cutoff_ts
        self.batch = batch
        self.ts_1 = None
        self.bucket = config_data.get("bucket")
        self.snapshot_path = config.get("snapshot_path")
        self.raw_data_path = config.get("raw_data_path")
        self.raw_data_format = "raw_json"
        self.snapshot_format = "csv"
        self.spark = spark_session_create("trading_competition_snap_assets")
        logging.info("trading_competition_snap_assets spark session created")

    @staticmethod
    def get_schema_for_asset(asset):
        try:
            asset_column_name = f"{asset}_columns"
            column_names = config.get(asset_column_name)
            # Define the schema explicitly
            return StructType([StructField(col, StringType(), True) for col in column_names])
        except Exception as e:
            raise e

    @staticmethod
    def process_trading_competition_user_events(df: DataFrame):
        # Extract the "object" field and parse "data" as JSON
        if not isinstance(df, DataFrame):
            return df
        df = df.withColumn("object", from_json(col("object"), StructType([
            StructField("type", StringType(), True),
            StructField("id", LongType(), True),
            StructField("data", schema_for_trading_competition_user_events)
        ])))
        # Select the fields from the nested "data" structure
        return df.select("object.data.*")

    def read_data(self, path, format, **kwargs):
        df = None
        if format is None or format == "csv":
            if kwargs.get("schema") is not None:
                logging.info("Reading data using schema")
                df = read_csv_file(path, None, False, kwargs.get("schema"))
            else:
                df = read_csv_file(path, None, False, None)
        elif format == "raw_json":
            df = read_json_data(path, is_raw=True)
        elif format == "json":
            df = read_json_data(path)
        elif format == "parquet":
            df = read_parque_file(path)
        else:
            logging.info("No Valid file format is present")
        return df

    def custom_transformation(self, df_t_1, df_t_2, de_dupe_on):
        if df_t_1 is not None:
            logging.info("cutoff time is {}".format(self.ts_1))
            df_t_1 = df_t_1.withColumn(de_dupe_on, col(de_dupe_on)).filter(
                col(de_dupe_on) <= lit(self.ts_1))
            t_2_cols = df_t_2.columns
            df_t_1 = df_t_1.select(t_2_cols)
            data_types = df_t_2.dtypes
            for data_type in data_types:
                df_t_1 = df_t_1.withColumn(data_type[0], col(data_type[0]).cast(data_type[1]))
            df_union = df_t_2.union(df_t_1)
        else:
            df_union = df_t_2
        return df_union

    @staticmethod
    def write_data(df, path, format):
        if format is None or format == "csv":
            df.coalesce(5).write.mode('overwrite').csv(path, header=True)
        elif format == "json":
            df.coalesce(1).write.mode('overwrite').json(path)
        elif format == "parquet":
            df.coalesce(1).write.mode('overwrite').parquet(path)
        else:
            logging.info("No Valid file format is present")
        return df

    def start_processing(self):
        t_1, h_1, t_2, h_2, self.ts_1, ts_2 = get_dates_and_timestamps(self.utc_cutoff_ts)
        logging.info(f"Jkt Date t_1: {t_1}, Snapshot write hour h_1: {h_1}, Snapshot and Raw Data Read Date  t_2: {t_2}, Snapshot read hour h_2: {h_2}")
        assets_batch = config.get("asset_snapshot").get(self.batch)
        for assets in assets_batch:
            for asset, dedupe in assets.items():
                #Read Last Snapshot Data
                logging.info(f"Snapshot asset : {asset}")

                try:
                    de_dupe_key = getattr(DeDupeKeysConstants, asset).value
                except AttributeError:
                    de_dupe_key = ["id"]

                try:
                    de_dupe_on = getattr(DeDupeOnConstants, asset).value
                except AttributeError:
                    de_dupe_on = "updated"

                try:
                    transaction_filter_column = getattr(TransactionFilterColumnConstants, asset).value
                except AttributeError:
                    transaction_filter_column = "transaction_time"

                last_snapshot_path = f"s3a://{self.bucket}/{self.snapshot_path}/{asset}/dt={t_2}/hour={h_2}"
                logging.info(f"Reading snapshot from {last_snapshot_path}")
                df_last_snapshot = read_csv_file(last_snapshot_path, None, False, self.get_schema_for_asset(asset))

                #Read Raw data
                raw_data_path = f"s3a://{self.bucket}/{self.raw_data_path}/{asset}/dt={t_1}"
                logging.info(f"Reading raw data from {raw_data_path}")
                df_raw_data = self.read_data(raw_data_path, self.raw_data_format)

                if asset == "trading_competition_user_events":
                    df_raw_data = self.process_trading_competition_user_events(df_raw_data)

                if isinstance(df_raw_data, DataFrame) and asset not in ["trading_competition_user_events", "options_contracts"]:
                    df_raw_data = df_raw_data.filter(col(transaction_filter_column).cast("timestamp") >= lit(
                        config_data.get("trading_competition_start_time")).cast("timestamp"))

                try:
                    flatten_column = getattr(FlattenColumnConstants, asset).value
                except AttributeError:
                    flatten_column = False

                if flatten_column and isinstance(df_raw_data, DataFrame):
                    df_raw_data_flatten = df_raw_data.withColumn(flatten_column.get("rename_to"),
                                                                 get_json_object(col(flatten_column.get("column_name")),
                                                                                 flatten_column.get("required_column")))

                    df_raw_data = df_raw_data_flatten.drop(flatten_column.get("column_name"))

                #Union Last Snapshot Data and Raw data
                df_union = self.custom_transformation(df_raw_data, df_last_snapshot, de_dupe_on)

                # Remove Duplicates based on de_dupe_key and de_dupe_on
                df_union = df_union.dropDuplicates(de_dupe_key + [de_dupe_on])

                logging.info("Started De dupe operation")
                if dedupe:
                    df = de_dupe_dataframe(df_union, de_dupe_key, de_dupe_on)
                else:
                    df = df_union
                de_dupe_write_path = f"s3a://{self.bucket}/{self.snapshot_path}/{asset}/dt={t_1}/hour={h_1}"
                logging.info("writing data in {}".format(de_dupe_write_path))
                self.write_data(df, de_dupe_write_path, self.snapshot_format)

if __name__ == "__main__":
    start_time= datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--utc_cutoff_ts", help="utc timestamp for snap assets")
    parser.add_argument("--batch_name", help="Batch name to snapshot")
    args = parser.parse_args()
    if args.utc_cutoff_ts:
        utc_cutoff_ts = get_utc_timestamp_from_string(args.utc_cutoff_ts)
    if args.batch_name:
        batch = args.batch_name
    obj = SnapAssets(utc_cutoff_ts, batch)
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "trading_competition_snap_asset")