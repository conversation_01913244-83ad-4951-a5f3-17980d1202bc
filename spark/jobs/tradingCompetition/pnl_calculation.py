from common import *
from trading_competition_config import *
from trading_competition_common import *


class PNLCalculation:
    def __init__(self, utc_cutoff_ts=None):
        self.spark = spark_session_create("gtv")
        self.bucket = config_data["bucket"]
        self.batch_path = "s3a://{}/{}".format(self.bucket, job_config_data['batches']['batch_file_path'])
        self.tier_snapshot_path = "s3a://{}/{}".format(self.bucket, job_config_data['aum_tier_upgrade']['tier_snapshot_path'])
        self.utc_cutoff_ts = utc_cutoff_ts or datetime.now(pytz.timezone("UTC"))
        self.partner_id = job_config_data["partner_id"]
        self.buy_types = job_config_data["buy_types"]
        self.trading_competition_start_time = get_utc_timestamp_from_string(config_data["trading_competition_start_time"])
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = get_dates_and_timestamps(self.utc_cutoff_ts)
        logging.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))

    def get_user_tiers(self):
        tier = read_csv_file("{}/dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1,self.h_1), None, False, None)
        tier = tier.select("account_id", "tier", "opt_in_time") \
            .withColumn("opt_in_time", col("opt_in_time").cast(TimestampType()))
        return tier

    def get_user_niv(self):
        niv_date = self.t_1 - timedelta(days=1)
        niv = read_csv_file("s3a://{}/{}/dt={}/".format(self.bucket, job_config_data['niv_path'], niv_date), None, False, None)
        niv = niv.select("account_id", "invested_value")
        return niv

    def get_user_gtv(self, batches):
        gtv = batches.filter((col("ignore_for_gtv") == False) & (col("transaction_type") != "WITHDRAWAL") & (col("transaction_type") != "GOLD_WITHDRAWAL"))
        gtv = gtv.withColumn("total_gtv", f.round(col("executed_quantity") * col("executed_unit_price") * col("currency_to_idr")).cast(LongType()))
        gtv = gtv.withColumn("total_gtv", when(col("asset_sub_type") == "options_contract_transactions", f.round(col("executed_quantity") * col("currency_to_idr") * col("gtv_multiplier")).cast(LongType())).otherwise(col("total_gtv")))
        gtv = gtv.groupby(["account_id"]).agg(sum("total_gtv").alias("total_gtv"))
        return gtv

    def calculate_pnl_and_percentage(self, batches, tiers):
        logging.info("trading_competition_start_time: {}".format(self.trading_competition_start_time))

        batches = batches.withColumn("realized_pnl", col("realized_pnl").cast(LongType()))
        batches = batches.withColumn("unrealized_pnl", col("unrealized_pnl").cast(LongType()))
        batches = batches.withColumn("position_x_time", col("position_x_time").cast(DoubleType()))

        pnl = batches.groupby(["account_id"]).agg(sum("position_x_time").alias("position_x_time"),
                                                  sum("position_open_time").alias("position_open_time"),
                                                  sum("realized_pnl").alias("realized_pnl"),
                                                  sum("unrealized_pnl").alias("unrealized_pnl"))

        pnl = tiers.join(pnl, on=["account_id"], how="left").fillna(0)

        pnl = pnl.withColumn("trading_competition_start_time", lit(self.trading_competition_start_time))
        pnl = pnl.withColumn("trading_competition_entry_time", f.greatest(col("opt_in_time"), col("trading_competition_start_time")))
        pnl = pnl.withColumn("execution_time", lit(self.utc_cutoff_ts))

        pnl = pnl.withColumn("pnl", (col("realized_pnl") + col("unrealized_pnl")).cast("long"))

        pnl = pnl.withColumn("time_weighted_avg_cost", when(col("position_open_time") == 0, 0).otherwise(col("position_x_time") / col("position_open_time")).cast(DoubleType()))
        pnl = pnl.withColumn("pnl_percentage", when(col("time_weighted_avg_cost") == 0, 0)
                             .otherwise(f.round(col("pnl") * 100 / col("time_weighted_avg_cost"), 2)))

        niv = self.get_user_niv()
        pnl = pnl.join(niv, on=["account_id"], how="left").fillna({'invested_value': 0})

        gtv = self.get_user_gtv(batches)
        pnl = pnl.join(gtv, on=["account_id"], how="left").fillna({'total_gtv': 0})

        pnl_window = Window.partitionBy(["tier"]).orderBy(col("pnl").desc(), col("total_gtv").desc(), col("invested_value").desc(), col("opt_in_time").asc())
        pnl_percentage_window = Window.partitionBy(["tier"]).orderBy(col("pnl_percentage").desc(), col("total_gtv").desc(), col("invested_value").desc(), col("opt_in_time").asc())

        pnl = pnl.withColumn("pnl_rank", f.rank().over(pnl_window))
        pnl = pnl.withColumn("pnl_percentage_rank", f.rank().over(pnl_percentage_window))

        return pnl

    def execute(self):
        batches = read_csv_file("{}/dt={}/hour={}".format(self.batch_path, self.t_1, self.h_1), None, False, None)
        tiers = self.get_user_tiers()
        pnl = self.calculate_pnl_and_percentage(batches, tiers)
        pnl.coalesce(1).write.mode('overwrite').csv("s3a://{}/{}/dt={}/hour={}/".format(self.bucket, job_config_data['pnl_path'], self.t_1, self.h_1), header=True)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--utc_cutoff_ts", help="UTC cutoff timestamp")
    args = parser.parse_args()
    utc_cutoff_ts = get_utc_timestamp_from_string(args.utc_cutoff_ts) if args.utc_cutoff_ts else None

    pnlCalculation = PNLCalculation(utc_cutoff_ts)

    pnlCalculation.execute()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "pnl calculation")
