from common import *
from trading_competition_config import *
from trading_competition_common import *


class AUMTierUpgradeProcessor:
    def __init__(self, utc_cutoff_ts=None):
        self.spark = spark_session_create("aum_tier_upgrade")
        self.bucket = config_data["bucket"]
        self.aum_file_path = "s3a://{}/{}".format(self.bucket, job_config_data["aum_tier_upgrade"]["aum_file_path"])
        self.tier_event_snapshot_path = "s3a://{}/{}".format(self.bucket, job_config_data["aum_tier_upgrade"]["tier_event_snapshot_path"])
        self.utc_cutoff_ts = utc_cutoff_ts or datetime.now(pytz.timezone("UTC"))
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = get_dates_and_timestamps(self.utc_cutoff_ts)
        logging.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))
        self.tier_upgrade_aum_range = job_config_data["aum_tier_upgrade"]["tier_upgrade_range"]
        self.clevertap_events_topic = config_data["kafka_topics"]["clevertap_events_topic"]

    def publish_clevertap_event(self, tier):
        prev_tier_path = "s3a://{}/{}/dt={}/hour={}/".format(
            self.bucket, job_config_data["aum_tier_upgrade"]["tier_snapshot_path"], self.t_2, self.h_2
        )
        prev_tier = None
        try:
            prev_tier = read_csv_file(prev_tier_path, None, False, None)
        except Exception as e:
            logging.error("Previous Tier File is not found")
        if prev_tier is not None:
            prev_tier = prev_tier.select("account_id", "user_id", col("tier").alias("prev_tier"))
            prev_tier = prev_tier.select("account_id", "user_id", "prev_tier")
            tier = tier.join(prev_tier, on=["account_id", "user_id"], how="left")
            tier = tier.filter(col("prev_tier").isNotNull() & (col("prev_tier") != col("tier")))
            current_utc_ts = get_current_utc_timestamp()
            tier = tier.withColumn("x-request-id", f.expr("uuid()").cast("binary"))
            tier = tier.withColumn("data", f.struct(
                col("user_id").alias("userId"),
                col("account_id").alias("accountId"),
                f.struct(
                    lit(current_utc_ts).alias("upgrade_tier_time"),
                    col("prev_tier").alias("previous_tier"),
                    col("tier").alias("upgrade_tier")
                ).alias("eventData"),
                lit("tc_tier_upgrade_success").alias("eventName")
            )).withColumn("type", lit("event")) \
                .withColumn("loggerContext", f.struct(col("x-request-id").cast(StringType()).alias("x-request-id")))
            tier = tier.select(
                col("account_id").cast(StringType()).alias("key"),
                f.to_json(f.struct(["data", "type", "loggerContext"])).alias("value"),
                f.array(f.struct(lit("x-request-id").alias("key"), col("x-request-id").alias("value"))).alias("headers")
            )
            write_data_in_kafka_topic(tier, config_data["bootstrap_servers"], self.clevertap_events_topic)

    def get_user_tier(self):
        tier_events = read_csv_file("{}/dt={}/hour={}/".format(self.tier_event_snapshot_path, self.t_1, self.h_1), None, False, None)
        tier_events = tier_events.withColumn("eventTime", col("eventTime").cast(TimestampType()))

        opt_in_time = tier_events.filter(col("userAction").isin(["OPT_IN"]))
        opt_in_time = de_dupe_dataframe(opt_in_time, ["accountId"], "eventTime", type="asc")
        opt_in_time = opt_in_time.select(col("accountId").alias("account_id"), col("eventTime").alias("opt_in_time"))

        tier = tier_events.filter(col("userAction").isin(["OPT_IN", "ACCEPTED"]))
        tier = de_dupe_dataframe(tier, ["accountId"], "eventTime")
        tier = tier.withColumn("tier", when(col("userAction") == "OPT_IN", col("currentTier")).otherwise(col("eligibleUpgradeTier")))
        tier = tier.select(
            col("accountId").alias("account_id"),
            col("userId").alias("user_id"),
            col("name"),
            col("email"),
            col("tradingCompetitionId").alias("trading_competition_id"),
            col("tier")
        )
        tier = tier.join(opt_in_time, on=["account_id"], how="left")
        return tier, tier_events

    def execute(self):
        t_2_aum = self.t_1 - timedelta(1)
        tier_upgrade_aum = self.spark.createDataFrame(self.tier_upgrade_aum_range)
        tier_upgrade_aum = tier_upgrade_aum.withColumnRenamed("tier", "eligible_upgrade_tier")

        aum = read_csv_file("{}/dt={}/".format(self.aum_file_path, t_2_aum), None, False, None)
        aum = aum.select("account_id", col("portfolioValue").alias("aum")) \
                .withColumn("aum_to_compare", when(col("aum") < 0, 0).otherwise(col("aum")))
        aum = aum.join(
            tier_upgrade_aum,
            (col("aum_to_compare") >= col("min_aum")) & ((col("aum_to_compare") < col("max_aum")) | col("max_aum").isNull()),
            "left"
        ).drop("min_aum", "max_aum", "aum_to_compare")

        tier, tier_events = self.get_user_tier()
        tier = tier.select("account_id", "user_id", "tier", "opt_in_time", "name", "email", "trading_competition_id")
        tier = tier.join(aum, on=["account_id"], how="left").fillna({'aum': 0})

        tier = tier.withColumn(
            "eligible_upgrade_tier",
            when((col("tier") == "CHALLENGER") & ((col("eligible_upgrade_tier") == "LEGEND") | (col("eligible_upgrade_tier") == "ELITE")), col("eligible_upgrade_tier"))
            .when((col("tier") == "ELITE") & (col("eligible_upgrade_tier") == "LEGEND"), col("eligible_upgrade_tier"))
            .otherwise(None)
        )
        tier = tier.withColumn("is_upgradable", when(((col("eligible_upgrade_tier") == col("tier")) | col("eligible_upgrade_tier").isNull()), False).otherwise(True))
        tier = tier.withColumn("user_action_for_tier_upgrade", lit(None))

        declined_tier = tier_events.filter(col("userAction").isin(["DECLINED"]))
        declined_tier = de_dupe_dataframe(declined_tier, ["accountId", "currentTier", "eligibleUpgradeTier"], "eventTime")
        declined_tier = declined_tier.select(
            col("accountId").alias("account_id"),
            col("currentTier").alias("tier"),
            col("eligibleUpgradeTier").alias("eligible_upgrade_tier"),
            col("userAction").alias("user_action")
        )

        tier = tier.join(declined_tier, on=["account_id", "tier", "eligible_upgrade_tier"], how="left")
        tier = tier.withColumn("is_upgradable", when(col("user_action").isNull(), col("is_upgradable")).otherwise(False))
        tier = tier.withColumn("user_action_for_tier_upgrade", when(col("user_action").isNotNull(), "DECLINED").otherwise(col("user_action_for_tier_upgrade"))).drop("user_action")

        ignored_tier = tier_events.filter(col("userAction").isin(["IGNORED"]))
        ignored_tier = de_dupe_dataframe(ignored_tier, ["accountId", "currentTier", "eligibleUpgradeTier"], "eventTime")
        ignored_tier = ignored_tier.select(
            col("accountId").alias("account_id"),
            col("currentTier").alias("tier"),
            col("eligibleUpgradeTier").alias("eligible_upgrade_tier"),
            col("userAction").alias("user_action")
        )

        tier = tier.join(ignored_tier, on=["account_id", "tier", "eligible_upgrade_tier"], how="left")
        tier = tier.withColumn("user_action_for_tier_upgrade", when(col("user_action").isNotNull(), "IGNORED").otherwise(col("user_action_for_tier_upgrade")))

        current_ts = datetime.now(tz=pytz.timezone("UTC"))
        tier = tier.withColumn("updated_at", lit(current_ts))
        tier = tier.withColumn("execution_time", lit(self.utc_cutoff_ts))

        output_path = "s3a://{}/{}/dt={}/hour={}/".format(
            self.bucket, job_config_data["aum_tier_upgrade"]["tier_snapshot_path"], self.t_1, self.h_1
        )
        tier = tier.withColumn("user_action_for_tier_upgrade", when(col("user_action_for_tier_upgrade").isNull(), None).otherwise(col("user_action_for_tier_upgrade")))
        tier.coalesce(1).write.mode('overwrite').csv(output_path, header=True)

        tier = tier.select("account_id", "user_id", "tier", "is_upgradable", "user_action_for_tier_upgrade", "eligible_upgrade_tier", "updated_at", "aum")
        tier = tier.withColumn("user_action_for_tier_upgrade", when(col("user_action_for_tier_upgrade").isNull(), "").otherwise(col("user_action_for_tier_upgrade")))
        tier = tier.withColumn("eligible_upgrade_tier", when(col("eligible_upgrade_tier").isNull(), "").otherwise(col("eligible_upgrade_tier")))

        mongo_write_config = {
            "uri": "{}.{}?authSource=admin".format(config_data["reporting_uri"], job_config_data["aum_tier_upgrade"]["mongo_collection"]),
            "collection": job_config_data["trading_competition_mongo_collection"],
            "batch_size": "500",
            "mode": "append"
        }

        write_asset_returns_to_mongo(tier, mongo_write_config, "aum_tier_upgrade", "update", "{'accountId':1}", add_created_at=False)
        self.publish_clevertap_event(tier)

if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--utc_cutoff_ts", help="UTC cutoff timestamp")
    args = parser.parse_args()
    utc_cutoff_ts = get_utc_timestamp_from_string(args.utc_cutoff_ts) if args.utc_cutoff_ts else None

    processor = AUMTierUpgradeProcessor(utc_cutoff_ts)
    processor.execute()

    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "aum_tier_upgrade")
