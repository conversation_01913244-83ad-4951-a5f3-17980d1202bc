from common import *
from structs import *
from config import *
from pyspark.sql.functions import split, explode

class GlobalStockPriceWatchlist(object):
    
    def __init__(self):
        self.job_config = job_config_data["predefined_watch_list"]["global_stock_price_watchlist"]
        self.global_stock_pre_define_watch_list = config_data["pre_define_watch_list"]["global_stock"]
        self.source_bucket_name =config_data["pre_define_watch_list"]["52_week_price_bucket"]
        self.source_folder_name = self.global_stock_pre_define_watch_list["52_week_price_folder"]
        self.kafka_topic = config_data["pre_define_watch_list"]["kafka_topic"]
        self.bootstrap_servers = config_data["bootstrap_servers"]
        self.spark = spark_session_create("global_stock_daily_52_week_price")
        self.threshold_value = job_config_data["predefined_watch_list"]["threshold_value"]

    def get_global_stock_price(self,offset):
        logging.info("price fetch from mongo")
        zone = pytz.timezone("Asia/Jakarta")
        dt_1 = (datetime.now(tz=zone) - timedelta(offset + 1))
        dt_0 = (datetime.now(tz=zone) - timedelta(offset))
        dt_1 = dt_1.replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        dt_0 = dt_0.replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        pipeline = "[{'$match':{startTime: {'$gte':ISODate('%s'),'$lte':ISODate('%s')}}},{'$sort':{startTime:-1}},{'$group':{_id:'$globalStockId',current_high_price:{'$max':'$midHighPrice'},current_low_price:{'$min':'$midLowPrice'}}}]" % (dt_1, dt_0)
        current_day_price = self.spark.read.format("com.mongodb.spark.sql.DefaultSource").option("spark.mongodb.input.uri", config_data["global_stock"]["mongo_ip"]).option("pipeline", pipeline).load()
        current_day_price = current_day_price.withColumnRenamed("_id","partner_global_stock_id")
        return current_day_price


    ''' get the market cap'''
    def get_global_stock_market_cap(self):
        logging.info("read market cap of global stock from postgres")
        global_stock_market_cap = self.spark.read.format("jdbc").option("user", config_data["postgres"]["global_stock"]["username"]).option("password", config_data["postgres"]["global_stock"]["password"]).option(
            "url", config_data["postgres"]["global_stock"]["url"]).option("dbtable", self.global_stock_pre_define_watch_list["db_table"]).load()
        global_stock_market_cap = global_stock_market_cap.orderBy(col("updated").desc()).select("global_stock_id","market_cap","updated")
        w2 = Window.partitionBy("global_stock_id").orderBy(col("updated").desc())
        logging.info("dedup the market cap data")
        global_stock_market_cap = global_stock_market_cap.withColumn("row", row_number().over(w2)).filter(col("row") == 1).drop("row").drop("updated")
        global_stock_market_cap = global_stock_market_cap.filter(col("market_cap").isNotNull())
        global_stock_market_cap = global_stock_market_cap.withColumn("market_cap",col("market_cap").cast(LongType()))
        return global_stock_market_cap


    ''' create low price list publish to kafka
    input: low price df
    output : return stock list df'''
    def get_current_day_low_price_list(self,current_day_low_price,t_1):
        logging.info("get 52 week low price")
        current_day_low_price = current_day_low_price.select("id","market_cap","low_price")
        if current_day_low_price.count()==0:
            logging.info("today is zero low stock")
            column = ["id","market_cap","low_price"]
            arr = [(0,0,0)]
            current_day_low_price = self.spark.createDataFrame(arr,column)
            current_day_low_price = current_day_low_price.withColumn('usStocks', lit(None).cast(StructType()))
            current_day_low_price = current_day_low_price.withColumn("asset_type", lit("usStocks"))
        else:
            logging.info("some stock")
            low_prices_columns = current_day_low_price.columns
            current_day_low_price = current_day_low_price.withColumn("usStocks", f.struct(low_prices_columns))
            current_day_low_price = current_day_low_price.withColumn("asset_type", lit("usStocks"))
        logging.info("collect list of gss stock of low price")
        current_day_low_price = current_day_low_price.groupBy("asset_type").agg(f.collect_list("usStocks").alias("usStocks"))
        current_day_low_price = current_day_low_price.withColumn("content", f.struct("usStocks")).drop("asset_type","usStocks").withColumn("category",lit("low_52_week")).withColumn("execution_date",lit(t_1))
        return current_day_low_price


    ''' create high price list publish to kafka
    input: high price df
    output : return stock list df'''
    def get_current_day_high_price_list(self,current_day_high_price,t_1):
        logging.info("get 52 week hgh price")
        current_day_high_price = current_day_high_price.select("id","market_cap","high_price")
        if current_day_high_price.count()==0:
            logging.info("today is zero high stock")
            column = ["id","market_cap","high_price"]
            arr = [(0,0,0)]
            current_day_high_price = self.spark.createDataFrame(arr,column)
            current_day_high_price = current_day_high_price.withColumn('usStocks', lit(None).cast(StructType()))
            current_day_high_price = current_day_high_price.withColumn("asset_type", lit("usStocks"))
        else:
            high_prices_columns = current_day_high_price.columns
            current_day_high_price = current_day_high_price.withColumn("usStocks", f.struct(high_prices_columns))
            current_day_high_price = current_day_high_price.withColumn("asset_type", lit("usStocks"))
        logging.info("collect list of gss stock of high price")
        current_day_high_price = current_day_high_price.groupBy("asset_type").agg(f.collect_list("usStocks").alias("usStocks"))
        current_day_high_price = current_day_high_price.withColumn("content", f.struct("usStocks")).drop("asset_type","usStocks").withColumn("category",lit("high_52_week")).withColumn("execution_date",lit(t_1))
        return current_day_high_price

    ''' 
    input - offset
    get last one year price from s3 price bucket, price is syn in mongo to s3 dag'''
    def read_52_week_price(self,offset):
        s3_path  = "s3a://{}/{}/*/*".format(self.source_bucket_name,self.source_folder_name)
        logging.info("recursive read global stock price from s3:{}".format(s3_path))
        global_stock_daily_price = self.spark.read.option("recursiveFileLookup", "true").json(s3_path)
        logging.info("convert starttime column to date")
        global_stock_daily_price = global_stock_daily_price.withColumn('t_date_parsed',f.from_unixtime(f.col('startTime')/1000))
        pick_price_offset = offset + 365
        starting_date = get_date(pick_price_offset)
        end_date =  get_date(offset)
        gss_current_price = global_stock_daily_price.filter(col("t_date_parsed")==end_date)
        count = gss_current_price.count()
        logging.info(count)
        if count != 0:
            gss_current_price = gss_current_price.groupBy("globalStockId"). \
                agg(f.min(f.col('midLowPrice')).alias('current_low_price'),f.max(f.col('midHighPrice')).alias('current_high_price'))
            gss_current_price = gss_current_price.withColumnRenamed("globalStockId","partner_global_stock_id")

        logging.info("pick 365 days low and high price between:{} to {}".format(starting_date,end_date))
        global_stock_daily_price = global_stock_daily_price.filter(col("t_date_parsed")>=starting_date)
        global_stock_daily_price = global_stock_daily_price.filter(col("t_date_parsed")<end_date)
        last_52_week_price = global_stock_daily_price.groupBy("globalStockId").\
            agg(f.min(f.col('midLowPrice')).alias('low_price'),f.max(f.col('midHighPrice')).alias('high_price'))
        stock_list = global_stock_daily_price.filter(col("t_date_parsed")== starting_date).select("globalStockId")
        last_52_week_price = last_52_week_price.join(stock_list,on=["globalStockId"],how="inner")
        return last_52_week_price,gss_current_price

    def get_global_stock_status(self):
        logging.info("reading global stock codes from kafka topic")
        df_gss_status = read_from_kafka_in_memory(config_data["bootstrap_servers"], config_data["global_stock"]["global_stock_list_kafka_topic"])
        logging.info("topic name {}".format(config_data["global_stock"]["global_stock_list_kafka_topic"]))
        df_gss_status = df_gss_status.select(col("value.id").alias("global_stock_id"), col("value.status").alias("status"),col("value.updated").alias("updated"),col("value.stock_type").alias("stock_type")).distinct()
        df_gss_status = de_dupe_dataframe(df_gss_status,["global_stock_id"], "updated").select("global_stock_id","status","stock_type")
        df_gss_status = df_gss_status.filter(col("status").isin("ACTIVE","active"))
        df_gss_status = df_gss_status.filter(col("stock_type").isin("PALN"))
        logging.info("active stock {}".format(df_gss_status.count()))
        df_gss_status = df_gss_status.select("global_stock_id")
        logging.info("successfully read global stock codes from kafka")
        return df_gss_status

    def get_eligible_stock_for_high_price(self,current_day_high_price,s3_high_price_path,t_2,t_1,gss_current_price):
        try:
            yesterday_high_price_gss_stock = self.spark.read.json("{}{}/*".format(s3_high_price_path,t_2)).select("content.*","execution_date").withColumn("usStocks", explode(col("usStocks")))
            yesterday_high_price_gss_stock = yesterday_high_price_gss_stock.select("usStocks.*","execution_date").select("id","market_cap","high_price","execution_date")
            current_day_high_price = current_day_high_price.withColumn("execution_date", lit(t_1))
            current_day_high_price =current_day_high_price.withColumnRenamed("global_stock_id","id").drop("high_price").withColumnRenamed("current_high_price","high_price").select("id","market_cap","high_price","execution_date")
            high_price_stock = current_day_high_price.union(yesterday_high_price_gss_stock)
            high_price_stock = de_dupe_dataframe(high_price_stock,["id"] , "execution_date")
        except Exception as e:
            current_day_high_price = current_day_high_price.withColumn("execution_date", lit(t_1))
            high_price_stock =current_day_high_price.withColumnRenamed("global_stock_id","id").drop("high_price").withColumnRenamed("current_high_price","high_price").select("id","market_cap","high_price","execution_date")
        high_price_stock = high_price_stock.withColumn("threshold_high_price",(col("high_price")*(100-self.threshold_value)/100))
        check_high_price_stock = high_price_stock.join(gss_current_price,high_price_stock.id==gss_current_price.partner_global_stock_id,"left").drop("partner_global_stock_id")
        check_high_price_stock = check_high_price_stock.filter((col("threshold_high_price") <= col("current_high_price")) & (col("high_price") >= col("current_high_price"))).drop("execution_date")
        check_high_price_stock = check_high_price_stock.withColumn("id",col("id").cast('integer'))
        return check_high_price_stock

    def get_eligible_stock_for_low_price(self,current_day_low_price,s3_low_price_path,t_2,t_1,gss_current_price):
        try:
            yesterday_low_price_gss_stock = self.spark.read.json("{}{}/*".format(s3_low_price_path,t_2)).select("content.*","execution_date").withColumn("usStocks", explode(col("usStocks")))
            yesterday_low_price_gss_stock = yesterday_low_price_gss_stock.select("usStocks.*","execution_date").select("id","market_cap","low_price","execution_date")
            current_day_low_price = current_day_low_price.withColumn("execution_date", lit(t_1))
            current_day_low_price =current_day_low_price.withColumnRenamed("global_stock_id","id").drop("low_price").withColumnRenamed("current_low_price","low_price").select("id","market_cap","low_price","execution_date")
            low_price_stock = current_day_low_price.union(yesterday_low_price_gss_stock)
            low_price_stock = de_dupe_dataframe(low_price_stock,["id"] , "execution_date")
        except Exception as e:
            current_day_low_price = current_day_low_price.withColumn("execution_date", lit(t_1))
            low_price_stock =current_day_low_price.withColumnRenamed("global_stock_id","id").drop("low_price").withColumnRenamed("current_low_price","low_price").select("id","market_cap","low_price","execution_date")
        low_price_stock = low_price_stock.withColumn("threshold_low_price",(col("low_price")*(100+self.threshold_value)/100))
        check_low_price_stock = low_price_stock.join(gss_current_price,low_price_stock.id==gss_current_price.partner_global_stock_id,"left").drop("partner_global_stock_id")
        check_low_price_stock = check_low_price_stock.filter((col("threshold_low_price")>= col("current_low_price")) & (col("low_price")<= col("current_low_price"))).drop("execution_date")
        check_low_price_stock = check_low_price_stock.withColumn("id",col("id").cast('integer'))
        return check_low_price_stock


    def start_processing(self):
        offset = config_data["offset"]
        t_1 = get_date(offset)
        t_2 = get_date(offset+1)
        logging.info("call read_52_week_price")
        last_52_week_price,gss_current_price = self.read_52_week_price(offset)
        logging.info("join current day price with last 365 days price")
        count = gss_current_price.count()
        s3_low_price_path = "s3a://{}/{}/52_week_low_price/dt=".format(config_data["bucket"], self.job_config["write_path"])
        s3_high_price_path = "s3a://{}/{}/52_week_high_price/dt=".format(config_data["bucket"], self.job_config["write_path"])
        if count ==0 :
            logging.info("call get_global_stock_price_s3")
            gss_current_price = self.get_global_stock_price(offset)

        if gss_current_price.count() !=0:
            last_one_year_price = gss_current_price.join(last_52_week_price,gss_current_price.partner_global_stock_id==last_52_week_price.globalStockId,"inner").drop("globalStockId")
            current_day_low_price = last_one_year_price.select("partner_global_stock_id","current_low_price","low_price")
            current_day_high_price = last_one_year_price.select("partner_global_stock_id","current_high_price","high_price")

            current_day_low_price = current_day_low_price.filter(col("current_low_price")<col("low_price"))
            current_day_high_price = current_day_high_price.filter(col("current_high_price")>col("high_price"))
            logging.info("call market cap function")
            global_stock_market_cap=self.get_global_stock_market_cap()
            df_gss_status = self.get_global_stock_status()
            logging.info("join low price stock with market cap")
            current_day_low_price = current_day_low_price.join(global_stock_market_cap,current_day_low_price.partner_global_stock_id==global_stock_market_cap.global_stock_id,"inner").drop("partner_global_stock_id")
            current_day_high_price = current_day_high_price.join(global_stock_market_cap,current_day_high_price.partner_global_stock_id==global_stock_market_cap.global_stock_id,"inner").drop("partner_global_stock_id")
            current_day_low_price = current_day_low_price.join(df_gss_status,on=["global_stock_id"],how="inner")
            current_day_high_price = current_day_high_price.join(df_gss_status,on=["global_stock_id"],how="inner")
            logging.info("join high price stock with market cap")
            current_day_low_price = self.get_eligible_stock_for_low_price(current_day_low_price,s3_low_price_path,t_2,t_1,gss_current_price)
            current_day_high_price = self.get_eligible_stock_for_high_price(current_day_high_price,s3_high_price_path,t_2,t_1,gss_current_price)
            current_day_low_price = self.get_current_day_low_price_list(current_day_low_price,t_1)
            current_day_high_price = self.get_current_day_high_price_list(current_day_high_price,t_1)
            current_day_high_price.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/52_week_high_price/dt={}/".format(config_data["bucket"], self.job_config["write_path"], str(t_1)))
            current_day_low_price.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/52_week_low_price/dt={}/".format(config_data["bucket"], self.job_config["write_path"], str(t_1)))
            logging.info("publish kafka msg for low and high price")
            logging.info(self.kafka_topic)
            publish_kafka_events(current_day_low_price,self.kafka_topic, t_1,"category",self.bootstrap_servers)
            publish_kafka_events(current_day_high_price,self.kafka_topic, t_1,"category",self.bootstrap_servers)
        else:
            yesterday_high_price_gss_stock = self.spark.read.json("{}{}/*".format(s3_high_price_path,t_2))
            yesterday_low_price_gss_stock = self.spark.read.json("{}{}/*".format(s3_low_price_path,t_2))
            yesterday_high_price_gss_stock.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/52_week_high_price/dt={}/".format(config_data["bucket"], self.job_config["write_path"], str(t_1)))
            yesterday_low_price_gss_stock.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/52_week_low_price/dt={}/".format(config_data["bucket"], self.job_config["write_path"], str(t_1)))


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    obj = GlobalStockPriceWatchlist()
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"gss_daily_52_week_price")
