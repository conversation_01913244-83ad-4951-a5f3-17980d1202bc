from common import *
from structs import *
from config import *
from pyspark.sql.functions import split, explode

class CryptoCurrencyPriceWatchlist(object):

    def __init__(self):
        self.job_config = job_config_data["predefined_watch_list"]["crypto_currency_price_watchlist"]
        self.crypto_currency_pre_define_watch_list = config_data["pre_define_watch_list"]["crypto_currency"]
        self.source_bucket_name =config_data["pre_define_watch_list"]["52_week_price_bucket"]
        self.source_folder_name = self.crypto_currency_pre_define_watch_list["52_week_price_folder"]
        self.kafka_topic = config_data["pre_define_watch_list"]["kafka_topic"]
        self.bootstrap_servers = config_data["bootstrap_servers"]
        self.spark = spark_session_create("crypto_daily_52_week_price")
        self.threshold_value = job_config_data["predefined_watch_list"]["threshold_value"]

    def get_crypto_currency_price(self,offset):
        logging.info("price fetch from mongo")
        zone = pytz.timezone("Asia/Jakarta")
        dt_1 = (datetime.now(tz=zone) - timedelta(offset + 1))
        dt_0 = (datetime.now(tz=zone) - timedelta(offset))
        dt_1 = dt_1.replace(hour=17, minute=0, second=0, microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        dt_0 = dt_0.replace(hour=17, minute=0, second=0, microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        pipeline = "[{'$match':{startTime: {'$gte':ISODate('%s'),'$lte':ISODate('%s')}}},{'$sort':{startTime:-1}},{'$group':{_id:'$cryptoCurrencyId',current_high_price:{'$max':'highMidPrice'},current_low_price:{'$min':'$lowMidPrice'}}}]" % (dt_1, dt_0)
        current_day_price = self.spark.read.format("com.mongodb.spark.sql.DefaultSource").option("spark.mongodb.input.uri", config_data["crypto_currency"]["mongo_ip"]).option("pipeline", pipeline).load()
        current_day_price = current_day_price.withColumnRenamed("_id","partner_crypto_currency_id")
        return current_day_price

    ''' get marekt cap of crypto coin'''
    def get_crypto_market_cap(self):
        logging.info("read market cap of crypto coin from postgres")
        crypto_market_cap = self.spark.read.format("jdbc").option("user", config_data["postgres"]["crypto_currency"]["username"]).option("password", config_data["postgres"]["crypto_currency"]["password"]).option(
            "url", config_data["postgres"]["crypto_currency"]["url"]).option("dbtable", self.crypto_currency_pre_define_watch_list["db_table"]).load()
        crypto_market_cap = crypto_market_cap.orderBy(col("updated").desc()).select("crypto_currency_id","market_cap","updated")
        w2 = Window.partitionBy("crypto_currency_id").orderBy(col("updated").desc())
        logging.info("dedup the market cap data")
        crypto_market_cap = crypto_market_cap.withColumn("row", row_number().over(w2)).filter(col("row") == 1).drop("row").drop("updated")
        crypto_market_cap = crypto_market_cap.filter(col("market_cap").isNotNull())
        crypto_market_cap = crypto_market_cap.withColumn("market_cap",col("market_cap").cast(LongType()))
        return crypto_market_cap

    ''' get last 365 days of of price low'''
    def get_current_day_low_price_list(self,current_day_low_price,t_1):
        logging.info("get 52 week low price")
        current_day_low_price =current_day_low_price.select("id","market_cap","low_price")
        if current_day_low_price.count()==0:
            logging.info("today is zero low coin")
            column = ["id","market_cap","low_price"]
            arr = [(0,0,0)]
            current_day_low_price = self.spark.createDataFrame(arr,column)
            current_day_low_price = current_day_low_price.withColumn('cryptocurrency', lit(None).cast(StructType()))
            current_day_low_price = current_day_low_price.withColumn("asset_type", lit("cryptocurrency"))
        else:
            low_prices_columns = current_day_low_price.columns
            current_day_low_price = current_day_low_price.withColumn("cryptocurrency", f.struct(low_prices_columns))
            current_day_low_price = current_day_low_price.withColumn("asset_type", lit("cryptocurrency"))
        logging.info("collect list of crypto coin of low price")
        current_day_low_price = current_day_low_price.groupBy("asset_type").agg(f.collect_list("cryptocurrency").alias("cryptocurrency"))
        current_day_low_price = current_day_low_price.withColumn("content", f.struct("cryptocurrency")).drop("asset_type","cryptocurrency").withColumn("category",lit("low_52_week")).withColumn("execution_date",lit(t_1))
        return current_day_low_price

    ''' get last 365 days of of price low'''
    def get_current_day_high_price_list(self,current_day_high_price,t_1):
        logging.info("get 52 week hgh price")
        current_day_high_price = current_day_high_price.select("id","market_cap","high_price")
        if current_day_high_price.count()==0:
            logging.info("today is zero high stock")
            column = ["id","market_cap","high_price"]
            arr = [(0,0,0)]
            current_day_high_price = self.spark.createDataFrame(arr,column)
            current_day_high_price = current_day_high_price.withColumn('cryptocurrency', lit(None).cast(StructType()))
            current_day_high_price = current_day_high_price.withColumn("asset_type", lit("cryptocurrency"))

        else:
            high_prices_columns = current_day_high_price.columns
            current_day_high_price = current_day_high_price.withColumn("cryptocurrency", f.struct(high_prices_columns))
            current_day_high_price = current_day_high_price.withColumn("asset_type", lit("cryptocurrency"))
        logging.info("collect list of crypto coin of low price")
        current_day_high_price = current_day_high_price.groupBy("asset_type").agg(f.collect_list("cryptocurrency").alias("cryptocurrency"))
        current_day_high_price = current_day_high_price.withColumn("content", f.struct("cryptocurrency")).drop("asset_type","cryptocurrency").withColumn("category",lit("high_52_week")).withColumn("execution_date",lit(t_1))
        return current_day_high_price


    '''read last 52 week price  from s3,ohlc price, sync in mongo to s3 job'''
    def read_52_week_price(self,offset):
        s3_path  = "s3a://{}/{}/*/*".format(self.source_bucket_name,self.source_folder_name)
        logging.info("recursive read crypto coin price from s3:{}".format(s3_path))
        crypto_daily_price = self.spark.read.option("recursiveFileLookup", "true").json(s3_path)
        logging.info("convert starttime column to date")
        crypto_daily_price = crypto_daily_price.withColumn('t_date_parsed',f.from_unixtime(f.col('startTime')/1000))
        pick_price_offset = offset + 365
        starting_date = get_date(pick_price_offset)
        logging.info("price start from")
        logging.info(starting_date)
        end_date =  get_date(offset)
        logging.info("price end date")
        logging.info(end_date)
        crypto_current_price = crypto_daily_price.filter(col("t_date_parsed")==end_date)
        count = crypto_current_price.count()
        logging.info(count)
        if count != 0:
            crypto_current_price = crypto_current_price.groupBy("cryptoCurrencyId").\
                agg(f.min(f.col('lowMidPrice')).alias('current_low_price'),f.max(f.col('highMidPrice')).alias('current_high_price'))
            crypto_current_price = crypto_current_price.withColumnRenamed("cryptoCurrencyId","partner_crypto_currency_id")
        logging.info("pick 365 days low and high price between:{} to {}".format(starting_date,end_date))
        crypto_daily_price = crypto_daily_price.filter(col("t_date_parsed")>= starting_date)
        crypto_daily_price = crypto_daily_price.filter(col("t_date_parsed")<end_date)
        coin_list = crypto_daily_price.filter(col("t_date_parsed")== starting_date).select("cryptoCurrencyId")
        crypto_daily_price = crypto_daily_price.join(coin_list,on=["cryptoCurrencyId"],how="inner")
        last_52_week_price = crypto_daily_price.groupBy("cryptoCurrencyId").\
            agg(f.min(f.col('lowMidPrice')).alias('low_price'),f.max(f.col('highMidPrice')).alias('high_price'))
        return last_52_week_price,crypto_current_price

    def get_crypto_coin_status(self):
        logging.info("reading crypto active coin from kafka topic")
        df_crypto_coin_status = read_from_kafka_in_memory(config_data["bootstrap_servers"], config_data["tax_report"]["crypto"]["crypto_coin_code_kafka_topic"])
        df_crypto_coin_status = df_crypto_coin_status.select(col("value.id").alias("crypto_currency_id"), col("value.safety_label").alias("coin_status"),col("value.updated").alias("updated")).distinct()
        df_crypto_coin_status = de_dupe_dataframe(df_crypto_coin_status,["crypto_currency_id"], "updated").select("crypto_currency_id","coin_status").fillna({"coin_status":"active"})
        df_crypto_coin_status = df_crypto_coin_status.filter(~col("coin_status").isin("DELISTING","DELISTED"))
        logging.info("active coin {}".format(df_crypto_coin_status.count()))
        df_crypto_coin_status = df_crypto_coin_status.select("crypto_currency_id")
        logging.info("successfully read crypto coin status from kafka")
        return df_crypto_coin_status
    
    def get_eligible_coin_for_high_price(self,current_day_high_price,s3_high_price_path,t_2,t_1,crypto_current_price):
        try:
            yesterday_high_price_coin = self.spark.read.json("{}{}/*".format(s3_high_price_path,t_2)).select("content.*","execution_date").withColumn("cryptocurrency", explode(col("cryptocurrency")))
            yesterday_high_price_coin = yesterday_high_price_coin.select("cryptocurrency.*","execution_date").select("id","market_cap","high_price","execution_date")
            current_day_high_price = current_day_high_price.withColumn("execution_date", lit(t_1))
            current_day_high_price =current_day_high_price.withColumnRenamed("crypto_currency_id","id").drop("high_price").withColumnRenamed("current_high_price","high_price").select("id","market_cap","high_price","execution_date")
            high_price_coin = current_day_high_price.union(yesterday_high_price_coin)
            high_price_coin = de_dupe_dataframe(high_price_coin,["id"] , "execution_date")
        except Exception as e:
            current_day_high_price = current_day_high_price.withColumn("execution_date", lit(t_1))
            high_price_coin =current_day_high_price.withColumnRenamed("crypto_currency_id","id").drop("high_price").withColumnRenamed("current_high_price","high_price").select("id","market_cap","high_price","execution_date")
        high_price_coin = high_price_coin.withColumn("threshold_high_price",(col("high_price")*(100-self.threshold_value)/100))
        check_high_price_coin = high_price_coin.join(crypto_current_price,high_price_coin.id==crypto_current_price.partner_crypto_currency_id,"left").drop("partner_crypto_currency_id")
        check_high_price_coin = check_high_price_coin.filter((col("threshold_high_price") <= col("current_high_price")) & (col("high_price") >= col("current_high_price"))).drop("execution_date")
        check_high_price_coin = check_high_price_coin.withColumn("id",col("id").cast('integer'))
        return check_high_price_coin

    def get_eligible_coin_for_low_price(self,current_day_low_price,s3_low_price_path,t_2,t_1,crypto_current_price):
        try:
            yesterday_low_price_coin = self.spark.read.json("{}{}/*".format(s3_low_price_path,t_2)).select("content.*","execution_date").withColumn("cryptocurrency", explode(col("cryptocurrency")))
            yesterday_low_price_coin = yesterday_low_price_coin.select("cryptocurrency.*","execution_date").select("id","market_cap","low_price","execution_date")
            current_day_low_price = current_day_low_price.withColumn("execution_date", lit(t_1))
            current_day_low_price =current_day_low_price.withColumnRenamed("crypto_currency_id","id").drop("low_price").withColumnRenamed("current_low_price","low_price").select("id","market_cap","low_price","execution_date")
            low_price_coin = current_day_low_price.union(yesterday_low_price_coin)
            low_price_coin = de_dupe_dataframe(low_price_coin,["id"] , "execution_date")
        except Exception as e:
            current_day_low_price = current_day_low_price.withColumn("execution_date", lit(t_1))
            low_price_coin =current_day_low_price.withColumnRenamed("crypto_currency_id","id").drop("low_price").withColumnRenamed("current_low_price","low_price").select("id","market_cap","low_price","execution_date")
        low_price_coin = low_price_coin.withColumn("threshold_low_price",(col("low_price")*(100+self.threshold_value)/100))
        check_low_price_coin = low_price_coin.join(crypto_current_price,low_price_coin.id==crypto_current_price.partner_crypto_currency_id,"left").drop("partner_crypto_currency_id")
        check_low_price_coin = check_low_price_coin.filter((col("threshold_low_price")>= col("current_low_price")) & (col("low_price")<= col("current_low_price"))).drop("execution_date")
        check_low_price_coin = check_low_price_coin.withColumn("id",col("id").cast('integer'))
        return check_low_price_coin


    def start_processing(self):
        offset = config_data["offset"]
        t_1 = get_date(offset)
        logging.info(t_1)
        t_2 = get_date(offset+1)
        logging.info(t_2)
        logging.info("call read_52_week_price")
        last_52_week_price,crypto_current_price = self.read_52_week_price(offset)
        logging.info("call get_crypto_currency_price_s3")
        count = crypto_current_price.count()
        if count ==0 :
            crypto_current_price = self.get_crypto_currency_price(offset)
        logging.info("join current day price with last 365 days price")
        last_one_year_price = crypto_current_price.join(last_52_week_price,crypto_current_price.partner_crypto_currency_id==last_52_week_price.cryptoCurrencyId,"inner").drop("cryptoCurrencyId")
        current_day_low_price = last_one_year_price.select("partner_crypto_currency_id","current_low_price","low_price")
        current_day_low_price = current_day_low_price.filter(col("current_low_price")<col("low_price"))
        current_day_high_price = last_one_year_price.select("partner_crypto_currency_id","current_high_price","high_price")
        current_day_high_price = current_day_high_price.filter(col("current_high_price")>col("high_price"))
        logging.info("call market cap function")
        crypto_market_cap = self.get_crypto_market_cap()
        df_crypto_coin_status = self.get_crypto_coin_status()
        logging.info("join low price coin with market cap")
        s3_low_price_path = "s3a://{}/{}/52_week_low_price/dt=".format(config_data["bucket"], self.job_config["write_path"])
        current_day_low_price = current_day_low_price.join(crypto_market_cap,current_day_low_price.partner_crypto_currency_id==crypto_market_cap.crypto_currency_id,"inner").drop("partner_crypto_currency_id")
        current_day_low_price = current_day_low_price.join(df_crypto_coin_status,on=["crypto_currency_id"],how="inner")
        low_price_coin = self.get_eligible_coin_for_low_price(current_day_low_price,s3_low_price_path,t_2,t_1,crypto_current_price)
        current_day_low_price = self.get_current_day_low_price_list(low_price_coin,t_1)

        logging.info("join high price coin with market cap")
        s3_high_price_path = "s3a://{}/{}/52_week_high_price/dt=".format(config_data["bucket"], self.job_config["write_path"])

        current_day_high_price = current_day_high_price.join(crypto_market_cap,current_day_high_price.partner_crypto_currency_id==crypto_market_cap.crypto_currency_id,"inner").drop("partner_crypto_currency_id")
        current_day_high_price = current_day_high_price.join(df_crypto_coin_status,on=["crypto_currency_id"],how="inner")
        current_day_high_price = self.get_eligible_coin_for_high_price(current_day_high_price,s3_high_price_path,t_2,t_1,crypto_current_price)
        current_day_high_price = self.get_current_day_high_price_list(current_day_high_price,t_1)
        current_day_high_price.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/52_week_high_price/dt={}/".format(config_data["bucket"], self.job_config["write_path"], str(t_1)))
        current_day_low_price.coalesce(1).write.mode("overwrite").json("s3a://{}/{}/52_week_low_price/dt={}/".format(config_data["bucket"], self.job_config["write_path"], str(t_1)))
        logging.info("publish kafka msg for low and high price")

        publish_kafka_events(current_day_low_price,self.kafka_topic, t_1,"category",self.bootstrap_servers)
        publish_kafka_events(current_day_high_price,self.kafka_topic, t_1,"category",self.bootstrap_servers)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    obj = CryptoCurrencyPriceWatchlist()
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"crypto_daily_52_week_price")

