from common import *
from structs import *
from config import *
from pyspark.sql import functions as f


class GSSFreeFee(object):
    def __init__(self):
        self.job_config = job_config_data["gss_free_fees"]
        self.offset = config_data['offset']
        self.execution_date = get_date(self.offset)
        self.mission_period = self.job_config["mission_period"]
        logging.info("free fees gss transaction for:{} days".format(self.mission_period))
        self.partner_ids = self.job_config["us_stock"]["partner_ids"]
        logging.info("filter for partner id is :{}".format(self.partner_ids))
        self.gss_kyc_filter_date = self.job_config["gss_kyc_start_date"]
        logging.info("gss kyc verified consider from {}".format(self.gss_kyc_filter_date))
        self.consider_transaction_date = self.job_config["capping_free_fees_start"]
        logging.info("gss transctions consider from {}".format(self.consider_transaction_date))
        self.free_fee_capping = config_data["gss_fee_waiver"]["stock"]["gss_free_fee_capping_for_new_user"]
        logging.info("free fees for capping per user is {}".format(self.free_fee_capping))
        self.spark = spark_session_create("mission_fees_reversal")

    def find_eligible_user(self, gss_kyc, gss_transaction):
        eligible_user_today = gss_kyc.join(gss_transaction, gss_kyc.user_id == gss_transaction.user_id_dup,
                                           "left").fillna(0.0)
        eligible_user_today = eligible_user_today.select("user_id", "first_verfied_date", "account_id",
                                                         "waived_off_fee", "counter", "free_fee_end_date",
                                                         "created", "transaction_time")

        user_with_fees = eligible_user_today.groupBy("user_id", "first_verfied_date", "account_id", "counter").agg(
            (round(sum("waived_off_fee"), 2).alias("total_gss_fee_waived")))
        user_with_fees = user_with_fees.withColumnRenamed("first_verfied_date", "kyc_verified_date")
        return user_with_fees

    def find_gss_transaction(self):
        dt_time = get_date_for_query(self.offset)
        dt_utc = dt_time.replace(hour=8, minute=0, second=0, microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        gss_transaction_path = "s3a://{}/{}/dt={}/*".format(config_data['bucket'],
                                                            self.job_config["us_stock"]["gss_transaction_path"],
                                                            self.execution_date)
        gss_transaction = self.spark.read.csv(gss_transaction_path, header=True, inferSchema=True)
        gss_transaction = gss_transaction.filter(
            (col("status").isin("SUCCESS", "PARTIALLY_FILLED", "PENDING")) & (col("partner_id").isin(self.partner_ids)))
        gss_transaction = gss_transaction.select("account_id", "user_id", "waived_off_fee", "created",
                                                 "transaction_time")
        gss_transaction = gss_transaction.withColumnRenamed("user_id", "user_id_dup")
        gss_transaction = gss_transaction.filter((col("created") >= self.consider_transaction_date) & (col("created") <= dt_utc)).drop("created")
        return gss_transaction

    def get_kyc_verified_user_last_30_days(self):
        gss_kyc_path = "s3a://{}/{}/dt={}/*".format(config_data['bucket'], self.job_config["gss_kyc_path"],
                                                    self.execution_date)
        gss_kyc = self.spark.read.csv(gss_kyc_path, header=True, inferSchema=True)
        gss_kyc = gss_kyc.filter(col("rejection_state").isNull())
        gss_kyc = gss_kyc.filter(col("first_verfied_date") >= self.gss_kyc_filter_date)
        gss_kyc = gss_kyc.withColumn("execution_date", lit(self.execution_date))
        gss_kyc = gss_kyc.withColumn("free_fee_end_date", f.date_add(col("first_verfied_date"), self.mission_period))
        gss_kyc = gss_kyc.filter(col("free_fee_end_date") >= col("execution_date"))
        gss_kyc = gss_kyc.withColumn("counter", datediff(col("execution_date"), col("first_verfied_date")) + 1)
        gss_kyc_verfied_user = gss_kyc.filter((col("counter")) <= self.mission_period)
        return gss_kyc_verfied_user

    def build_mongo_config(self) -> dict:

        mongo_config = {}
        collection_name = self.job_config["collection_name"]
        mongo_config["uri"] = "%s.%s?authSource=admin" % (
            config_data.get("reporting_uri"),
            collection_name,
        )
        mongo_config["collection"] = "%s.%s" % (
            config_data.get("user_fee_tiering").get("database"),
            collection_name,
        )
        mongo_config["batch_size"] = 500
        mongo_config["mode"] = "append"
        return mongo_config

    def start_processing(self):
        logging.info("Starting snapshoting for gss kyc information")
        gss_kyc_verfied_user = self.get_kyc_verified_user_last_30_days()
        logging.info("fetch gss transaction")
        gss_transaction = self.find_gss_transaction()
        logging.info("gss kyc")
        user_with_fees = self.find_eligible_user(gss_kyc_verfied_user, gss_transaction)
        user_with_fees = user_with_fees.filter(col("account_id") != 0)
        logging.info("processing is completed and start s3 write")
        user_with_fees.write.mode("overwrite").csv(
            "s3a://{}/{}/dt={}/".format(config_data['bucket'], self.job_config["write_free_fee"], self.execution_date),
            header=True)
        user_with_fees = user_with_fees.withColumn("gss_fee_max_cap_reached", when((col("total_gss_fee_waived")>self.free_fee_capping),lit(True)).otherwise(False))
        update_time = datetime.now()

        user_with_fees = user_with_fees.select("user_id","account_id","total_gss_fee_waived","gss_fee_max_cap_reached")
        user_with_fees = user_with_fees.withColumn("updated_at",lit(update_time))
    
        write_asset_returns_to_mongo(
            asset_returns_t_0=user_with_fees,
            mongo_config=self.build_mongo_config(),
            asset_name="gss_free_fees",
            write_format="update",
            shardkey=self.job_config["shardkey"],
            add_created_at=False
        )



if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    obj = GSSFreeFee()
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "mission_fees_reversal")
