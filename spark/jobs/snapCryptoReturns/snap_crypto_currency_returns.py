from common import *
from structs import *


raw_bucket = config_data["crypto_currency"]["raw_bucket"]
spark = spark_session_create("snap_crypto_currency_returns")
lowerbound_ts = get_date_for_query(config_data["offset"]+1)
upperbound_ts = datetime.now()
cut_off_time=None

def get_current_crypto_currency_price(url, username, password, db_table, partner_id, asset_name):
    try:
        current_timestamp = get_date_for_query(config_data["offset"] - 1).replace(hour=0,
                                                                                                           minute=0,
                                                                                                           second=0,
                                                                                                           microsecond=0)

        crypto_currency_partner_prices = spark.read.format("jdbc").option("user", username).option("password",
                                                                                                   password).option(
            "url", url).option("dbtable", db_table).load()

        crypto_currency_partner_prices = crypto_currency_partner_prices.where((col("partner_id") == partner_id) & (
                date_format(from_utc_timestamp(col("updated"), "Asia/Jakarta"), "yyyy-MM-dd") <= date_format(
            lit(current_timestamp), "yyyy-MM-dd"))).orderBy(col("updated").desc())

        w2 = Window.partitionBy("crypto_currency_id").orderBy(col("updated").desc())
        crypto_currency_partner_prices = crypto_currency_partner_prices.withColumn("row", row_number().over(w2)).filter(
            col("row") == 1).drop("row")
        crypto_currency_partner_prices = crypto_currency_partner_prices.withColumn("mid_price",
                                                                                   floor((col("buy_back_price") + col(
                                                                                       "sell_price")) / 2))
        crypto_currency_partner_prices = crypto_currency_partner_prices.select(
            col("crypto_currency_id").alias("partner_crypto_currency_id"), "mid_price")

        logging.info("Successfully loaded {} partner prices".format(asset_name))
        return crypto_currency_partner_prices

    except Exception as e:
        logging.error("An error has occurred while loading {} partner_prices: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def get_crypto_currency_price_from_mongo(partner_id, asset_name):
    try:

        offset = config_data["offset"]
        t_1 = get_date(offset)
        s3_path  = "s3a://{}/{}/dt=".format(config_data["bucket"],config_data["crypto_currency"]["price_path"])
        data = spark.read.csv( s3_path + str(t_1) +"/",header=True,inferSchema=True)
        crypto_currency_partner_prices = data.select(col("_id").alias("partner_crypto_currency_id"), "mid_price")
        logging.info("Successfully loaded {} partner prices".format(asset_name))
        return crypto_currency_partner_prices
    except Exception as e:
        logging.error("An error has occurred while loading {} partner_prices: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def calculate_crypto_currency_returns(crypto_currency_t_0, current_crypto_currency_prices, asset_name):
    try:
        crypto_currency_t_0 = crypto_currency_t_0.join(current_crypto_currency_prices,
                                                       crypto_currency_t_0["crypto_currency_id"] ==
                                                       current_crypto_currency_prices["partner_crypto_currency_id"],
                                                       "left").drop("partner_crypto_currency_id")

        crypto_currency_returns_t_0 = crypto_currency_t_0.withColumnRenamed("mid_price", "unit_price")

        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_quantity",
                                                                             col("total_quantity").cast("double"))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("reward_balance",
                                                                             col("reward_balance").cast("double"))

        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("weighted_cost",
                                                                             col("weighted_cost").cast("double"))

        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("realised_gain",
                                                                             col("realised_gain").cast("double"))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_quantity",
                                                                             round(col("total_quantity"), 8))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("reward_balance",
                                                                             round(col("reward_balance"), 8))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_value", floor(
            col("total_quantity") * col("unit_price")))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("total_reward_value", floor(
            col("reward_balance") * col("unit_price")))
        crypto_currency_returns_t_0 = crypto_currency_returns_t_0.withColumn("unrealised_gain",
                                                                             col("total_value") - ceil(
                                                                                 col("total_quantity") * col(
                                                                                     "weighted_cost")))

        logging.info("Successfully calculated {} returns".format(asset_name))

        return crypto_currency_returns_t_0

    except Exception as e:
        logging.error("An error has occurred while calculating {} returns: {}".format(asset_name, repr(e)))
        logging.exception(e)
        raise e


def get_crypto_currency_returns_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    crypto_currency_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["crypto_currency"]["t1"]["asset_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_returns)
    logging.info("Count for crypto returns : {}".format(crypto_currency_t_1.count()))
    logging.info("Completed spark read")
    crypto_currency_t_1 = crypto_currency_t_1.filter(col("updated")<=cut_off_time)
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["crypto_currency"]["t1"]["asset_folder"]),"id",lowerbound_ts,upperbound_ts)
    crypto_currency_t_1 = crypto_currency_t_1.filter(~col("id").isin(delete_record))
    logging.info("Saving deduped T1 files back to s3")
    save_de_duped_asset_t_1_to_s3(crypto_currency_t_1,
                                  config_data["crypto_currency"]["de_dupe_t_1"]["bucket"],
                                  config_data["crypto_currency"]["de_dupe_t_1"]["asset_folder"],
                                  config_data["crypto_currency"]["de_dupe_t_1"]["files_folder"],
                                  get_date(config_data["offset"]),
                                  config_data["crypto_currency"]["asset_name"],
                                  config_data["crypto_currency"]["primary_keys"],
                                  config_data["crypto_currency"]["column_order"])
    crypto_currency_t_2 = get_asset_t_2(config_data["crypto_currency"]["t2"]["bucket"],
                                        config_data["crypto_currency"]["t2"]["asset_folder"],
                                        config_data["crypto_currency"]["t2"]["files_folder"],
                                        get_date(config_data["offset"]+1),
                                        schema_for_crypto_currency_returns,
                                        config_data["crypto_currency"]["asset_name"])
    crypto_currency_t_2 = crypto_currency_t_2.filter(~col("id").isin(delete_record))
    crypto_currency_t_0 = get_asset_t_0(crypto_currency_t_1,
                                        crypto_currency_t_2,
                                        config_data["crypto_currency"]["primary_keys"],
                                        config_data["crypto_currency"]["asset_name"],
                                        config_data["crypto_currency"]["column_order"])
    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(crypto_currency_t_0,
                         config_data["crypto_currency"]["t2"]["bucket"],
                         config_data["crypto_currency"]["t2"]["asset_folder"],
                         config_data["crypto_currency"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["crypto_currency"]["asset_name"],write_partition=5)
    crypto_currency_t_0 = drop_unnecessary_columns(crypto_currency_t_0,
                                                   config_data["crypto_currency"]["list_of_unnecessary_columns"])
    return crypto_currency_t_0


def get_crypto_currency_accounts_data():
    logging.info("Reading t1 files from HDFS")
    dt = get_date(config_data["offset"])
    crypto_currency_t_1 = read_json_data("{}{}/dt={}/".format(raw_bucket, config_data["crypto_currency"]["t1"]["asset_account_folder"], dt), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_accounts)
    logging.info("Count for crypto accounts : {}".format(crypto_currency_t_1.count()))
    logging.info("Completed spark read")
    crypto_currency_t_1 = crypto_currency_t_1.filter(col("updated")<=cut_off_time)
    delete_record = read_deleted_record("{}{}/dt=".format(raw_bucket,config_data["crypto_currency"]["t1"]["asset_account_folder"]),"id",lowerbound_ts,upperbound_ts)
    crypto_currency_t_1 = crypto_currency_t_1.filter(~col("id").isin(delete_record))
    logging.info("Saving deduped T1 files back to s3")
    save_de_duped_asset_t_1_to_s3(crypto_currency_t_1,
                                  config_data["crypto_currency"]["de_dupe_t_1"]["bucket"],
                                  config_data["crypto_currency"]["de_dupe_t_1"]["asset_account_folder"],
                                  config_data["crypto_currency"]["de_dupe_t_1"]["files_folder"],
                                  get_date(config_data["offset"]),
                                  config_data["crypto_currency"]["asset_name"],
                                  config_data["crypto_currency"]["primary_keys"],
                                  config_data["crypto_currency"]["accounts_column_order"])
    crypto_currency_t_2 = get_asset_t_2(config_data["crypto_currency"]["t2"]["bucket"],
                                        config_data["crypto_currency"]["t2"]["asset_account_folder"],
                                        config_data["crypto_currency"]["t2"]["files_folder"],
                                        get_date(config_data["offset"]+1),
                                        schema_for_crypto_currency_accounts,
                                        config_data["crypto_currency"]["asset_name"])
    crypto_currency_t_2 = crypto_currency_t_2.filter(~col("id").isin(delete_record))
    crypto_currency_t_0 = get_asset_t_0(crypto_currency_t_1,
                                        crypto_currency_t_2,
                                        config_data["crypto_currency"]["primary_keys"],
                                        config_data["crypto_currency"]["asset_name"],
                                        config_data["crypto_currency"]["accounts_column_order"])
    logging.info("Saving merged data to s3")
    save_asset_t_0_to_s3(crypto_currency_t_0,
                         config_data["crypto_currency"]["t2"]["bucket"],
                         config_data["crypto_currency"]["t2"]["asset_account_folder"],
                         config_data["crypto_currency"]["t2"]["files_folder"],
                         get_date(config_data["offset"]),
                         config_data["crypto_currency"]["asset_name"],write_partition=5)
    crypto_currency_t_0 = drop_unnecessary_columns(crypto_currency_t_0,
                                                   config_data["crypto_currency"]["list_of_unnecessary_columns"])
    return crypto_currency_t_0


def start_processing():
    logging.info("Starting execution for crypto currency Snapshotting")
    crypto_currency_returns_t_0 = get_crypto_currency_returns_data()
    crypto_currency_accounts_t_0 = get_crypto_currency_accounts_data()

    crypto_currency_t_0 = crypto_currency_returns_t_0.join(crypto_currency_accounts_t_0, (
            (crypto_currency_returns_t_0.account_id == crypto_currency_accounts_t_0.account_id) & (
            crypto_currency_returns_t_0.crypto_currency_id == crypto_currency_accounts_t_0.crypto_currency_id)),
                                                           "inner").drop(
        crypto_currency_returns_t_0["total_quantity"]).drop(crypto_currency_accounts_t_0["account_id"]).drop(
        crypto_currency_accounts_t_0["crypto_currency_id"]).drop(crypto_currency_accounts_t_0["created"])


    current_crypto_currency_prices = get_crypto_currency_price_from_mongo(config_data["crypto_currency"]["partner_id"],config_data["crypto_currency"]["asset_name"])

    crypto_currency_t_0 = crypto_currency_t_0.withColumnRenamed("balance", "total_quantity")
    crypto_currency_t_0 = crypto_currency_t_0.fillna({'locked_balance': 0.0})
    crypto_currency_t_0 = crypto_currency_t_0.withColumn("total_quantity", col("total_quantity") + col("locked_balance"))

    logging.info("Calculation cost and value using current price")
    crypto_currency_t_0 = calculate_crypto_currency_returns(crypto_currency_t_0,
                                                            current_crypto_currency_prices,
                                                            config_data["crypto_currency"]["asset_name"])

    crypto_currency_t_0 = drop_unnecessary_columns(crypto_currency_t_0,
                                                   config_data["crypto_currency"]["removal_list_after_merge"])

    logging.info("Updating calculated values to s3")
    save_calculated_asset_returns_t_0_to_s3(crypto_currency_t_0,
                                            config_data["crypto_currency"]["snap"]["bucket"],
                                            config_data["crypto_currency"]["snap"]["asset_folder"],
                                            config_data["crypto_currency"]["snap"]["files_folder"],
                                            get_date(config_data["offset"]),
                                            config_data["crypto_currency"]["asset_name"],write_partition=5)


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--cut_off_time", help="cutoff time according to job")
    args = parser.parse_args()
    cut_off_time_config = job_config_data["cut_off_time"][args.cut_off_time] if args.cut_off_time else job_config_data["cut_off_time"]["0000JKT"]
    config_data["offset"] = cut_off_time_config["offset"]
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    cut_off_time = get_cutoff_time(cut_off_time_config)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"snap_crypto_currency_returns")
    