from common import *
from config import *
from structs import *

spark = spark_session_create("crypto_currency_promotion")
config = job_config_data["crypto_currency_promotion"]
date_format = "%Y-%m-%d"
promotion_start_date = datetime.strptime(config["promotion_start_date"], date_format).date()
base_date = (promotion_start_date - timedelta(1))
promotion_end_date = datetime.strptime(config["promotion_end_date"], date_format).date()
avg_period_end_date = datetime.strptime(config["avg_period_end_date"], date_format).date()
current_timestamp = get_date_for_query(0)

mongo_write_config = {
    "uri": "{}.{}?authSource=admin".format(config_data["reporting_uri"], config["mongo_collection"]),
    "collection": config["mongo_collection"],
    "batch_size": "500",
    "mode": "append"
}


def get_crypto_internal_deposit(current_day):
    crypto_currency_wallet_transfers_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["pluang_plus_member_validity"]["crypto_currency_wallet_transfers_de_dupe"], current_day)
    crypto_currency_transfer = read_csv_file(crypto_currency_wallet_transfers_path, None, False, schema_for_crypto_currency_wallet_transfers)
    crypto_currency_transfer = crypto_currency_transfer.withColumn("updated_at", col("updated_at").cast("timestamp"))
    cutoff_start_ts = datetime.strptime(str(base_date), '%Y-%m-%d').replace(hour=17, minute=00, second=00, microsecond=000000)
    cutoff_end_ts = datetime.strptime(str(current_day), '%Y-%m-%d').replace(hour=17, minute=00, second=00, microsecond=000000)
    crypto_currency_transfer = crypto_currency_transfer.filter((col("network") == 'INTERNAL') & (col("status").isin(['SUCCESS'])) & (col("transaction_type") == "DEPOSIT") & (col("updated_at") >= cutoff_start_ts) & (col("updated_at") < cutoff_end_ts))
    crypto_currency_transfer = crypto_currency_transfer.withColumn("crypto_internal_deposit", (col("unit_price") * col("total_quantity")).cast("long"))
    crypto_currency_transfer = crypto_currency_transfer.groupBy(["account_id"]).agg(sum("crypto_internal_deposit").alias("crypto_internal_deposit"))
    crypto_currency_transfer = crypto_currency_transfer.withColumn("crypto_internal_deposit", col("crypto_internal_deposit").cast("long"))
    return crypto_currency_transfer


def get_invested_value(current_day):
    s3_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config_data["pluang_plus_member_validity"]["pluang_plus_member_validity_snapshot"], current_day)
    invested_value = read_csv_file(s3_path, None, False, None).select("account_id", col("invested_value").cast("long"))
    return invested_value


def get_crypto_niv(current_day):
    crypto_currency_returns_path = "s3a://{}/{}/{}/dt={}/".format(config_data["bucket"],
                                                       config_data["crypto_currency"]["t2"]["asset_folder"],
                                                       config_data["crypto_currency"]["t2"]["files_folder"], current_day)

    crypto_currency_returns = read_csv_file(crypto_currency_returns_path, None, False, None)
    crypto_currency_returns = crypto_currency_returns.select("account_id", col("total_quantity"), col("weighted_cost")).withColumn("crypto_niv", (col("total_quantity") * col("weighted_cost")).cast("long"))

    crypto_currency_pocket_returns_path = "s3a://{}/{}/{}/dt={}/".format(config_data["bucket"],
                                                       job_config_data["crypto_currency_pocket"]["crypto_currency_pocket_returns"]["asset_folder"],
                                                       job_config_data["crypto_currency_pocket"]["crypto_currency_pocket_returns"]["t2_files_folder"], current_day)
    crypto_currency_pocket_returns = read_csv_file(crypto_currency_pocket_returns_path, None, False, None)
    crypto_currency_pocket_returns = crypto_currency_pocket_returns.select("account_id", col("total_quantity"), col("weighted_cost")).withColumn("crypto_niv", (col("total_quantity") * col("weighted_cost")).cast("long"))

    crypto_currency_returns = crypto_currency_returns.select("account_id", "crypto_niv")
    crypto_currency_pocket_returns = crypto_currency_pocket_returns.select("account_id", "crypto_niv")
    crypto_currency_combined_returns = crypto_currency_returns.union(crypto_currency_pocket_returns)

    crypto_currency_combined_returns = crypto_currency_combined_returns.groupBy(["account_id"]).agg(sum("crypto_niv").alias("crypto_niv"))
    crypto_currency_combined_returns = crypto_currency_combined_returns.withColumn("crypto_niv", col("crypto_niv").cast("long"))
    return crypto_currency_combined_returns


def get_niv_in_promotion_period(current_day):
    base_crypto_niv = get_crypto_niv(base_date)
    base_crypto_niv = base_crypto_niv.withColumnRenamed("crypto_niv", "base_crypto_niv")

    current_crypto_niv = get_crypto_niv(current_day)
    current_crypto_niv = current_crypto_niv.withColumnRenamed("crypto_niv", "current_crypto_niv")

    base_invested_value = get_invested_value(base_date)
    base_invested_value = base_invested_value.withColumnRenamed("invested_value", "base_invested_value")

    current_invested_value = get_invested_value(current_day)
    current_invested_value = current_invested_value.withColumnRenamed("invested_value", "current_invested_value")

    crypto_internal_deposit = get_crypto_internal_deposit(current_day)

    niv_diff = current_invested_value.join(base_invested_value, on=["account_id"], how="full") \
        .join(current_crypto_niv, on=["account_id"], how="full") \
        .join(base_crypto_niv, on=["account_id"], how="full") \
        .join(crypto_internal_deposit, on=["account_id"], how="full")
    niv_diff = niv_diff.fillna(0)

    niv_diff = niv_diff.withColumn("net_crypto_niv", col("current_crypto_niv") - col("base_crypto_niv") - col("crypto_internal_deposit"))
    niv_diff = niv_diff.withColumn("net_invested_value", col("current_invested_value") - col("base_invested_value"))

    niv_diff = niv_diff.withColumn("eligible_niv", f.least(col("net_crypto_niv"), col("net_invested_value")))
    niv_diff = niv_diff.withColumn("max_eligible_niv", lit(config["max_eligible_niv"]))
    niv_diff = niv_diff.withColumn("eligible_niv", f.least(col("eligible_niv"), col("max_eligible_niv"))).drop("max_eligible_niv")
    niv_diff = niv_diff.withColumn("eligible_niv_temp", when(col("eligible_niv") < config["min_eligible_niv"], 0).otherwise(col("eligible_niv")))
    niv_diff = niv_diff.withColumn("cashback", (col("eligible_niv_temp")*config["cashback_rate"]/100).cast("long")).drop("eligible_niv_temp")

    niv_diff = niv_diff.withColumn("updated_at", lit(current_timestamp))

    niv_snapshot_path_prefix = "s3a://{}/{}/".format(config_data["bucket"], config["niv_snapshot_path"])
    current_niv_snapshot_path = "{}/dt={}/".format(niv_snapshot_path_prefix, current_day)
    if current_day > promotion_start_date:
        prev_niv_snapshot_path = "{}/dt={}/".format(niv_snapshot_path_prefix, (current_day - timedelta(1)))
        prev_niv_diff = read_csv_file(prev_niv_snapshot_path, None, False, None).select("account_id", col("created").cast("timestamp"), col("state"))
        niv_diff = niv_diff.join(prev_niv_diff, on=["account_id"], how="left")
        niv_diff = niv_diff.withColumn("created", when(col("created").isNull(), lit(current_timestamp)).otherwise(col("created")))
    else:
        niv_diff = niv_diff.withColumn("created", lit(current_timestamp))
        niv_diff = niv_diff.withColumn("state", lit("AVAILABLE"))

    niv_diff = niv_diff.withColumn("state", when(((col("state") == 'ELIGIBLE') | (col("eligible_niv") >= config["min_eligible_niv"])), "ELIGIBLE").otherwise("AVAILABLE"))
    if current_day == promotion_end_date:
        niv_diff = niv_diff.withColumn("state", when((col("eligible_niv") < config["min_eligible_niv"]), "INELIGIBLE").otherwise("ELIGIBLE"))

    niv_diff.coalesce(1).write.mode("overwrite").csv(current_niv_snapshot_path, header=True)

    niv_diff = niv_diff.withColumnRenamed("base_crypto_niv", "baseCryptoNIV")
    niv_diff = niv_diff.withColumnRenamed("current_crypto_niv", "currentCryptoNIV")
    niv_diff = niv_diff.withColumnRenamed("eligible_niv", "eligibleNIV")
    niv_diff = niv_diff.withColumnRenamed("net_crypto_niv", "netCryptoNIV")
    niv_diff = niv_diff.withColumn("eligibleNIV", when(col("eligibleNIV") < 0, 0).otherwise(col("eligibleNIV")))

    num_of_partition = config_data["num_of_partition_pnl"]
    sleep_time_mongo = config_data["sleep_time_mongo_pnl"]
    for i in range(0, num_of_partition):
        logging.info("writing in mongo for partition number: {}".format(i))
        df = niv_diff.filter(col("account_id") % num_of_partition == i)
        write_asset_returns_to_mongo(df, mongo_write_config,
                                     "crypto_currency_promotion_niv", "update", "{'accountId':1}", add_created_at=False)
        time.sleep(sleep_time_mongo)


def calculate_avg_niv(current_day, previous_day):
    return None


def start_processing():
    logging.info("Starting Crypto Currency promotions job")
    t_1 = get_date(config_data["offset"])
    t_2 = get_date(config_data["offset"]+1)
    if (t_1 >= promotion_start_date) and (t_1 <= promotion_end_date):
        get_niv_in_promotion_period(t_1)
    elif (t_1 > promotion_end_date) and (t_1 <= avg_period_end_date):
        calculate_avg_niv(t_1, t_2)
    else:
        logging.info("current date is less than base date. skipping the processing!")
    logging.info("Crypto Currency Promotions job completed")


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "snap_crypto_currency_returns")