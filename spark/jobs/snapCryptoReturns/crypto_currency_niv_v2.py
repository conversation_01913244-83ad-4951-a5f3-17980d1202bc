from common import *
from config import *
from structs import *

spark = spark_session_create("crypto_currency_promotion")
config = {
    "crypto_currency_wallet_transfer_path": "crypto_currency_wallet_transfers",
    "crypto_currency_returns_path": "crypto_currency_returns/t_2_files",
    "crypto_currency_returns_raw_path": "raw_data/crypto_currency_returns",
    "crypto_currency_pocket_returns_path": "crypto_currency_pocket_returns/t_2_files",
    "crypto_currency_pocket_returns_raw_path": "raw_data/crypto_currency_pocket_returns",
    "crypto_gtv_path": "gtv_calculation/crypto_currency",
    "invested_value_snapshot_path": "pluang_plus_member_validity/snapshot",
    "promotion_start_date": "2024-09-19",
    "promotion_end_date": "2024-10-20",
    "avg_period_end_date": "2025-10-20",
    "niv_snapshot_path": "crypto_currency_niv_new",
    "mongo_collection": "crypto_currency_niv",
    "avg_niv_path": "crypto_currency_niv_new/avg_niv",
    "transaction_path": "crypto_currency_niv_new/transactions_niv",
    "max_eligible_niv": 500000000,
    "min_eligible_niv": 5000000,
    "cashback_rate": 1.5
}
date_format = "%Y-%m-%d"
promotion_start_date = datetime.strptime(config["promotion_start_date"], date_format).date()
base_date = (promotion_start_date - timedelta(1))
promotion_end_date = datetime.strptime(config["promotion_end_date"], date_format).date()
avg_period_end_date = datetime.strptime(config["avg_period_end_date"], date_format).date()
current_timestamp = get_date_for_query(0)
bucket = config_data["bucket"]
mongo_write_config = {
    "uri": "{}.{}?authSource=admin".format(config_data["reporting_uri"], config["mongo_collection"]),
    "collection": config["mongo_collection"],
    "batch_size": "500",
    "mode": "append"
}

transactions_used = None


def union_transactions(df, operation_type="union"):
    global transactions_used
    if operation_type == "union":
        if transactions_used is None:
            transactions_used = df
        else:
            transactions_used = transactions_used.union(df)
    else:
        if transactions_used is not None:
            df = df.select(col("id").alias("internal_transfer_id"), col("asset_type").alias("internal_asset_type"))
            transactions_used = transactions_used.join(df, (transactions_used.id == df.internal_transfer_id) & (transactions_used.asset_type == df.internal_asset_type), "left")
            transactions_used = transactions_used.withColumn("transfer_network", when(col("internal_transfer_id").isNotNull(), lit("INTERNAL")).otherwise(lit(None)))
            transactions_used = transactions_used.drop("internal_transfer_id", "internal_asset_type")


def get_crypto_internal_deposit(current_day):
    crypto_currency_wallet_transfers_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config["crypto_currency_wallet_transfer_path"], current_day)
    crypto_currency_transfer = read_csv_file(crypto_currency_wallet_transfers_path, None, False, schema_for_crypto_currency_wallet_transfers)
    crypto_currency_transfer = crypto_currency_transfer.withColumn("updated_at", col("updated_at").cast("timestamp"))
    cutoff_start_ts = datetime.strptime(str(base_date), '%Y-%m-%d').replace(hour=17, minute=00, second=00, microsecond=000000)
    cutoff_end_ts = datetime.strptime(str(current_day), '%Y-%m-%d').replace(hour=17, minute=00, second=00, microsecond=000000)
    crypto_currency_transfer = crypto_currency_transfer.filter((col("network") == 'INTERNAL') & (col("status").isin(['SUCCESS'])) & (col("transaction_type") == "DEPOSIT") & (col("updated_at") >= cutoff_start_ts) & (col("updated_at") < cutoff_end_ts))
    crypto_currency_transfer = crypto_currency_transfer.withColumn("total_crypto_internal_deposit", (col("unit_price") * col("total_quantity")).cast("long"))
    union_transactions(crypto_currency_transfer.select("id", "account_id", "crypto_currency_id", col("total_quantity").alias("quantity"), "unit_price", lit(0).alias("weighted_cost"), col("created_at").alias("created"), col("updated_at").alias("updated"), lit("crypto_wallet_transfer").alias("asset_type"), "transaction_type", lit("INTERNAL").alias("transfer_network"), col("total_crypto_internal_deposit").alias("total_value")), operation_type="internal_deposit")
    crypto_currency_transfer = crypto_currency_transfer.groupBy(["account_id"]).agg(sum("total_crypto_internal_deposit").alias("total_crypto_internal_deposit"))
    crypto_currency_transfer = crypto_currency_transfer.withColumn("total_crypto_internal_deposit", col("total_crypto_internal_deposit").cast("long"))
    return crypto_currency_transfer


def exclude_margin_wallet_transfers(crypto_gtv, current_day):
    crypto_currency_wallet_transfers_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config["crypto_currency_wallet_transfer_path"], current_day)
    crypto_currency_transfer = read_csv_file(crypto_currency_wallet_transfers_path, None, False, schema_for_crypto_currency_wallet_transfers)
    crypto_currency_transfer = crypto_currency_transfer.select("id", lit("crypto_wallet_transfer").alias("asset_subtype"), "network").filter(col("network") == "MARGIN_INTERNAL")
    crypto_gtv = crypto_gtv.join(crypto_currency_transfer, on=["id", "asset_subtype"], how="left").filter(col("network").isNull()).drop("network")
    return crypto_gtv


def get_crypto_returns(current_day, previous_day):
    crypto_returns_t_2_path = "s3a://{}/{}/dt={}/".format(bucket, config["crypto_currency_returns_path"], previous_day)
    crypto_returns_t_2 = read_csv_file(crypto_returns_t_2_path, None, False, None).select(col("account_id").cast("long"), col("crypto_currency_id").cast("long"), col("weighted_cost").cast("double"), col("updated").cast("timestamp").alias("returns_updated"))

    crypto_returns_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(bucket, config["crypto_currency_returns_raw_path"], current_day), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_returns)
    crypto_returns_t_1 = crypto_returns_t_1.select(col("account_id").cast("long"), col("crypto_currency_id").cast("long"), col("weighted_cost").cast("double"), col("updated").cast("timestamp").alias("returns_updated"))
    crypto_returns = crypto_returns_t_2.union(crypto_returns_t_1)

    crypto_pocket_returns_t_2_path = "s3a://{}/{}/dt={}/".format(bucket, config["crypto_currency_pocket_returns_path"], previous_day)
    crypto_pocket_returns_t_2 = read_csv_file(crypto_pocket_returns_t_2_path, None, False, None).select(col("account_id").cast("long"), col("user_pocket_id").cast("long"), col("crypto_currency_id").cast("long"), col("weighted_cost").cast("double"), col("updated").cast("timestamp").alias("returns_updated"))

    crypto_pocket_returns_t_1 = read_json_data("s3a://{}/{}/dt={}/".format(bucket, config["crypto_currency_pocket_returns_raw_path"], current_day), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_pocket_returns)
    crypto_pocket_returns_t_1 = crypto_pocket_returns_t_1.select(col("account_id").cast("long"), col("user_pocket_id").cast("long"), col("crypto_currency_id").cast("long"), col("weighted_cost").cast("double"), col("updated").cast("timestamp").alias("returns_updated"))
    crypto_pocket_returns = crypto_pocket_returns_t_2.union(crypto_pocket_returns_t_1)
    return crypto_returns, crypto_pocket_returns


def add_weighted_cost(transaction, returns, join_key, metric_name):
    transaction = transaction.join(returns, on=join_key, how="left")
    transaction = transaction.filter(col("created") > col("returns_updated"))
    transaction = de_dupe_dataframe(transaction, ["id"], "returns_updated")
    transaction = transaction.withColumn(metric_name, (col("weighted_cost")*col("quantity")).cast("long"))
    union_transactions(transaction.select("id", "account_id", "crypto_currency_id", "quantity", round(col("total_value")/col("quantity"), 12).alias("unit_price"), "weighted_cost", "created", "updated", col("asset_subtype").alias("asset_type"), "transaction_type", lit(None).alias("transfer_network"), col(metric_name).alias("total_value")))
    transaction = transaction.groupBy(["account_id"]).agg(sum(metric_name).alias(metric_name))
    return transaction


def get_crypto_values_at_t_1(current_day, previous_day):
    crypto_returns, crypto_pocket_returns = get_crypto_returns(current_day, previous_day)

    crypto_gtv_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config["crypto_gtv_path"], current_day)
    crypto_gtv = read_csv_file(crypto_gtv_path, None, False, None)

    crypto_gtv = exclude_margin_wallet_transfers(crypto_gtv, current_day)

    crypto_buy_t_1 = crypto_gtv.filter((col("transaction_type") == 'BUY') & ((col("asset_subtype") == 'crypto_transactions') | (col("asset_subtype") == 'crypto_pocket_transactions')))

    union_transactions(crypto_buy_t_1.select("id", "account_id", col("product_id").alias("crypto_currency_id"), "quantity", round(col("total_value")/col("quantity"), 12).alias("unit_price"), lit(0).alias("weighted_cost"), "created", "updated", col("asset_subtype").alias("asset_type"), "transaction_type", lit(None).alias("transfer_network"), "total_value"))

    crypto_buy_t_1 = crypto_buy_t_1.groupBy(["account_id"]).agg(sum("total_value").cast("long").alias("total_crypto_buy"))

    crypto_deposit_t_1 = crypto_gtv.filter((col("transaction_type") == 'DEPOSIT') & (col("asset_subtype") == 'crypto_wallet_transfer'))

    union_transactions(crypto_deposit_t_1.select("id", "account_id", col("product_id").alias("crypto_currency_id"), "quantity", round(col("total_value")/col("quantity"), 12).alias("unit_price"), lit(0).alias("weighted_cost"), "created", "updated", col("asset_subtype").alias("asset_type"), "transaction_type", lit(None).alias("transfer_network"), "total_value"))

    crypto_deposit_t_1 = crypto_deposit_t_1.groupBy(["account_id"]).agg(sum("total_value").cast("long").alias("total_crypto_deposit"))

    crypto_sell_t_1 = crypto_gtv.filter((col("transaction_type") == 'SELL') & (col("asset_subtype") == 'crypto_transactions'))
    crypto_sell_t_1 = crypto_sell_t_1.withColumnRenamed("product_id", "crypto_currency_id")
    crypto_sell_t_1 = add_weighted_cost(crypto_sell_t_1, crypto_returns, ["account_id", "crypto_currency_id"], "total_crypto_sell")

    crypto_pocket_sell_t_1 = crypto_gtv.filter((col("transaction_type") == 'SELL') & (col("asset_subtype") == 'crypto_pocket_transactions'))
    crypto_pocket_sell_t_1 = crypto_pocket_sell_t_1.withColumnRenamed("product_id", "crypto_currency_id")
    crypto_pocket_sell_t_1 = add_weighted_cost(crypto_pocket_sell_t_1, crypto_pocket_returns, ["account_id", "user_pocket_id", "crypto_currency_id"], "total_crypto_sell")

    crypto_withdrawal_t_1 = crypto_gtv.filter((col("transaction_type") == 'WITHDRAWAL') & (col("asset_subtype") == 'crypto_wallet_transfer'))
    crypto_withdrawal_t_1 = crypto_withdrawal_t_1.withColumnRenamed("product_id", "crypto_currency_id")
    crypto_withdrawal_t_1 = add_weighted_cost(crypto_withdrawal_t_1, crypto_returns, ["account_id", "crypto_currency_id"], "total_crypto_withdrawal")

    crypto_sell_t_1 = crypto_sell_t_1.union(crypto_pocket_sell_t_1)
    crypto_sell_t_1 = crypto_sell_t_1.groupBy(["account_id"]).agg(sum("total_crypto_sell").alias("total_crypto_sell"))

    crypto_values = crypto_buy_t_1.join(crypto_sell_t_1, on=["account_id"], how="full") \
        .join(crypto_deposit_t_1, on=["account_id"], how="full") \
        .join(crypto_withdrawal_t_1, on=["account_id"], how="full").fillna(0)
    return crypto_values


def get_cash_values(current_day, base_day):
    s3_path = "s3a://{}/{}".format(config_data["bucket"], config["invested_value_snapshot_path"])
    base_invested_value = read_csv_file("{}/dt={}/".format(s3_path, base_day), None, False, None).select("account_id", col("total_topup").cast("long").alias("base_topup"), col("total_cashout").cast("long").alias("base_cashout"))
    current_invested_value = read_csv_file("{}/dt={}/".format(s3_path, current_day), None, False, None).select("account_id", col("total_topup").cast("long").alias("current_topup"), col("total_cashout").cast("long").alias("current_cashout"))
    cash_values = base_invested_value.join(current_invested_value, on=["account_id"], how="full").fillna(0)
    cash_values = cash_values.withColumn("total_topup", col("current_topup") - col("base_topup"))
    cash_values = cash_values.withColumn("total_cashout", col("current_cashout") - col("base_cashout"))
    cash_values = cash_values.select("account_id", "total_topup", "total_cashout")
    return cash_values


def get_crypto_niv(prev_crypto_values, current_day, prev_day):
    crypto_values = get_crypto_values_at_t_1(current_day, prev_day)
    crypto_values = crypto_values.select("account_id", "total_crypto_buy", "total_crypto_sell", "total_crypto_deposit", "total_crypto_withdrawal")
    if prev_crypto_values is not None:
        prev_crypto_values = prev_crypto_values.select("account_id", "total_crypto_buy", "total_crypto_sell", "total_crypto_deposit", "total_crypto_withdrawal")
        crypto_values = prev_crypto_values.union(crypto_values)
    crypto_values = crypto_values.groupBy(["account_id"]).agg(sum("total_crypto_buy").alias("total_crypto_buy"), sum("total_crypto_sell").alias("total_crypto_sell"), sum("total_crypto_deposit").alias("total_crypto_deposit"), sum("total_crypto_withdrawal").alias("total_crypto_withdrawal"))
    crypto_internal_deposits = get_crypto_internal_deposit(current_day)
    crypto_values = crypto_values.join(crypto_internal_deposits, on=["account_id"], how="full").fillna(0)
    return crypto_values


def write_crypto_niv_in_mongo(current_niv):
    current_niv = current_niv.withColumnRenamed("eligible_niv", "eligibleNIV")
    current_niv = current_niv.withColumnRenamed("net_cash_niv", "netCashNIV")
    current_niv = current_niv.withColumnRenamed("net_crypto_niv", "netCryptoNIV")
    current_niv = current_niv.withColumn("eligibleNIV", when(col("eligibleNIV") < 0, 0).otherwise(col("eligibleNIV")))

    # remove later start
    current_niv = current_niv.withColumn("baseCryptoNIV", lit(0))
    current_niv = current_niv.withColumn("baseInvestedValue", lit(0))
    current_niv = current_niv.withColumn("cryptoInternalDeposit", col("total_crypto_internal_deposit"))
    current_niv = current_niv.withColumn("currentCryptoNIV", col("netCryptoNIV"))
    current_niv = current_niv.withColumn("currentInvestedValue", col("netCashNIV"))
    current_niv = current_niv.withColumn("netInvestedValue", col("netCashNIV"))
    current_niv = current_niv.select("account_id", "baseCryptoNIV", "baseInvestedValue", "currentCryptoNIV", "currentInvestedValue", "cryptoInternalDeposit", "netCryptoNIV", "netInvestedValue", "eligibleNIV", "cashback", "state", "created_at", "updated_at")
    # remove later end
    write_asset_returns_to_mongo(current_niv, mongo_write_config,
                                 "crypto_currency_promotion_niv", "update", "{'accountId':1}", add_created_at=False)


def get_niv_in_promotion_period(current_day):
    niv_snapshot_path_prefix = "s3a://{}/{}/".format(config_data["bucket"], config["niv_snapshot_path"])
    current_niv_path = "{}/dt={}/".format(niv_snapshot_path_prefix, current_day)
    prev_day = current_day - timedelta(1)
    prev_niv_path = "{}/dt={}/".format(niv_snapshot_path_prefix, prev_day)
    prev_niv = None
    if current_day > promotion_start_date:
        prev_niv = read_csv_file(prev_niv_path, None, False, None)
    crypto_values = get_crypto_niv(prev_niv, current_day, prev_day)
    cash_values = get_cash_values(current_day, base_date)
    current_niv = crypto_values.join(cash_values, on=["account_id"], how="full").fillna(0)
    current_niv = current_niv.withColumn("net_crypto_niv", col("total_crypto_buy") - col("total_crypto_sell"))
    current_niv = current_niv.withColumn("net_cash_niv", col("total_topup") - col("total_cashout"))

    current_niv = current_niv.withColumn("eligible_niv", f.least(col("net_crypto_niv"), col("net_cash_niv")))
    current_niv = current_niv.withColumn("eligible_niv", col("eligible_niv") + col("total_crypto_deposit") - col("total_crypto_withdrawal") - col("total_crypto_internal_deposit"))

    current_niv = current_niv.withColumn("max_eligible_niv", lit(config["max_eligible_niv"]))
    current_niv = current_niv.withColumn("eligible_niv", f.least(col("eligible_niv"), col("max_eligible_niv"))).drop("max_eligible_niv")
    current_niv = current_niv.withColumn("eligible_niv_temp", when(col("eligible_niv") < config["min_eligible_niv"], 0).otherwise(col("eligible_niv")))
    current_niv = current_niv.withColumn("cashback", (col("eligible_niv_temp")*config["cashback_rate"]/100).cast("long")).drop("eligible_niv_temp")

    current_niv = current_niv.withColumn("updated_at", lit(current_timestamp))

    if current_day > promotion_start_date:
        prev_niv = prev_niv.select("account_id", col("created_at").cast("timestamp"), col("state"))
        current_niv = current_niv.join(prev_niv, on=["account_id"], how="left")
        current_niv = current_niv.withColumn("created_at", when(col("created_at").isNull(), lit(current_timestamp)).otherwise(col("created_at")))
    else:
        current_niv = current_niv.withColumn("created_at", lit(current_timestamp))
        current_niv = current_niv.withColumn("state", lit("AVAILABLE"))

    current_niv = current_niv.withColumn("state", when(((col("state") == 'ELIGIBLE') | (col("eligible_niv") >= config["min_eligible_niv"])), "ELIGIBLE").otherwise("AVAILABLE"))
    if current_day == promotion_end_date:
        current_niv = current_niv.withColumn("state", when((col("eligible_niv") < config["min_eligible_niv"]), "INELIGIBLE").otherwise("ELIGIBLE"))

    current_niv.coalesce(1).write.mode("overwrite").csv(current_niv_path, header=True)
    write_crypto_niv_in_mongo(current_niv)


def calculate_avg_niv(current_day, prev_day):
    logging.info("Starting calculation of avg crypto niv")
    avg_niv_path_prefix = "s3a://{}/{}/".format(config_data["bucket"], config["avg_niv_path"])
    if (current_day - timedelta(1)) == promotion_end_date:
        niv_snapshot_path = "s3a://{}/{}/dt={}/".format(config_data["bucket"], config["niv_snapshot_path"], promotion_end_date)
        avg_niv_snapshot = read_csv_file(niv_snapshot_path, None, False, None)
        avg_niv_snapshot = avg_niv_snapshot.filter(col("eligible_niv") >= config["min_eligible_niv"])
        avg_niv_snapshot = avg_niv_snapshot.withColumn("base_eligible_niv", col("eligible_niv"))
        avg_niv_snapshot = avg_niv_snapshot.withColumn("total_eligible_niv_after_promotion_end", lit(0))
    else:
        avg_niv_snapshot = read_csv_file("{}dt={}/".format(avg_niv_path_prefix, prev_day), None, False, None)
    crypto_values = get_crypto_niv(avg_niv_snapshot, current_day, prev_day)
    cash_values = get_cash_values(current_day, base_date)
    eligible_users = avg_niv_snapshot.select("account_id", "state")
    avg_niv_snapshot = avg_niv_snapshot.select("account_id", "base_eligible_niv", "total_eligible_niv_after_promotion_end", "created_at")
    cash_values = cash_values.join(eligible_users, on=["account_id"], how="full").filter(col("state").isNotNull()).drop("state")
    crypto_values = crypto_values.join(eligible_users, on=["account_id"], how="full").filter(col("state").isNotNull()).drop("state")
    current_niv = crypto_values.join(cash_values, on=["account_id"], how="full").fillna(0)
    current_niv = current_niv.withColumn("net_crypto_niv", col("total_crypto_buy") - col("total_crypto_sell"))
    current_niv = current_niv.withColumn("net_cash_niv", col("total_topup") - col("total_cashout"))

    current_niv = current_niv.withColumn("todays_eligible_niv", f.least(col("net_crypto_niv"), col("net_cash_niv")))
    current_niv = current_niv.withColumn("todays_eligible_niv", col("todays_eligible_niv") + col("total_crypto_deposit") - col("total_crypto_withdrawal") - col("total_crypto_internal_deposit"))

    current_niv = current_niv.withColumn("max_eligible_niv", lit(config["max_eligible_niv"]))
    current_niv = current_niv.withColumn("todays_eligible_niv", f.least(col("todays_eligible_niv"), col("max_eligible_niv"))).drop("max_eligible_niv")
    num_of_days = (current_day - promotion_end_date).days
    current_niv = current_niv.withColumn("num_of_days", lit(num_of_days))
    current_niv = current_niv.join(avg_niv_snapshot, on=["account_id"], how="full").fillna(0)
    current_niv = current_niv.withColumn("total_eligible_niv_after_promotion_end", col("total_eligible_niv_after_promotion_end") + col("todays_eligible_niv"))
    current_niv = current_niv.withColumn("avg_eligible_niv", (col("total_eligible_niv_after_promotion_end")/col("num_of_days")).cast("long"))
    current_niv = current_niv.withColumn("eligible_niv", f.least(col("avg_eligible_niv"), col("base_eligible_niv")))
    current_niv = current_niv.withColumn("cashback", (col("eligible_niv")*config["cashback_rate"]/100).cast("long"))
    current_niv = current_niv.withColumn("cashback", when((col("cashback") < 0), 0).otherwise(col("cashback")))
    current_niv = current_niv.withColumn("updated_at", lit(current_timestamp))
    if current_day == avg_period_end_date:
        current_niv = current_niv.withColumn("state", when(col("cashback") > 0, "SUCCESS").otherwise("INELIGIBLE"))
    else:
        current_niv = current_niv.withColumn("state", lit("ELIGIBLE"))
    current_niv = current_niv.select("account_id", "total_crypto_buy", "total_crypto_sell", "total_crypto_deposit", "total_crypto_withdrawal", "total_crypto_internal_deposit", "total_topup", "total_cashout", "net_crypto_niv", "net_cash_niv", "todays_eligible_niv", "base_eligible_niv", "total_eligible_niv_after_promotion_end", "num_of_days", "avg_eligible_niv", "eligible_niv", "cashback", "state", "created_at", "updated_at")
    current_niv.coalesce(1).write.mode("overwrite").csv("{}dt={}/".format(avg_niv_path_prefix, current_day), header=True)
    logging.info("Successfully calculated avg niv. Starting mongo write.")
    write_crypto_niv_in_mongo(current_niv)
    logging.info("Mongo write of crypto avg niv is complete.")
    return current_niv


def start_processing():
    global transactions_used
    logging.info("Starting Crypto Currency promotions job")
    t_1 = get_date(config_data["offset"])
    t_2 = get_date(config_data["offset"]+1)
    if (t_1 >= promotion_start_date) and (t_1 <= promotion_end_date):
        get_niv_in_promotion_period(t_1)
    elif (t_1 > promotion_end_date) and (t_1 <= avg_period_end_date):
        current_niv = calculate_avg_niv(t_1, t_2)
        if transactions_used is not None:
            current_niv = current_niv.select("account_id")
            transactions_used = transactions_used.join(current_niv, on=["account_id"], how="inner")
    else:
        logging.info("current date is less than base date. skipping the processing!")
    if transactions_used is not None:
        transactions_used = transactions_used.withColumn("processing_date", lit(t_1))
        transactions_used.coalesce(1).write.mode("overwrite").csv("s3a://{}/{}/dt={}/".format(config_data["bucket"], config["transaction_path"], t_1), header=True)
    else:
        logging.info("No Crypto Transaction found for crypto currency niv calculation.")
    logging.info("Crypto Currency Promotions job completed")


if __name__ == "__main__":
    start_time = datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    args = parser.parse_args()
    if args.offset:
        offset = args.offset
        config_data['offset'] = int(offset)
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "snap_crypto_currency_returns")