from common import *
from structs import *

spark = spark_session_create("crypto_avg_holding_time")
def get_date(offset):
    zone = pytz.timezone("Asia/Jakarta")
    return (datetime.now(tz=zone) - timedelta(offset)).date()


def start_processing():
    zone = pytz.timezone("Asia/Jakarta")
    t_1 = get_date(config_data["offset"])
    t_2 = get_date(config_data["offset"]+1)
    df_1 = read_json_data("{}{}/dt={}/".format(config_data["crypto_metrics"]["raw_bucket"], config_data["crypto_metrics"]["raw_bucket_folder"], t_1), is_raw=True, schema_for_empty_df=schema_for_crypto_currency_transactions)
    df_1 = df_1.filter(col("status") == "SUCCESS")
    primary_keys = ["account_id", "crypto_currency_id"]
    window = Window.partitionBy([col(x) for x in primary_keys]).orderBy(col("updated").asc())

    df_1_buy = df_1.filter(col("transaction_type") == "BUY")
    df_1_buy = df_1_buy.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
    df_1_buy = df_1_buy.withColumn("buy_ts", col("updated"))

    df_1_sell = df_1.filter(col("transaction_type") == "SELL")
    df_1_sell = df_1_sell.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
    df_1_sell = df_1_sell.withColumn("sell_ts", col("updated"))

    df_2 = spark.read.csv(config_data["crypto_metrics"]["metric_bucket"]+config_data["crypto_metrics"]["metric_folder"]+"/"+config_data["crypto_metrics"]["avg_hold_time_folder"]+"/dt="+str(t_2)+"/", header=True,inferSchema=True, quote='"', escape='"', multiLine=True)

    df = df_2.join(df_1_buy, on=["account_id", "crypto_currency_id"], how="full")
    df = df.withColumn("first_buy_ts", when((col("first_buy_ts").isNull()) | (col("first_buy_ts") > col("buy_ts")), col("buy_ts")).otherwise(col("first_buy_ts")).cast(TimestampType()))
    df = df.select("account_id", "crypto_currency_id", "first_buy_ts", "first_sell_ts", "buy_ts")

    df = df.join(df_1_sell, on=["account_id", "crypto_currency_id"], how="full")
    df = df.withColumn("first_sell_ts", when(((col("first_buy_ts").isNotNull()) & (col("first_buy_ts") <= col("sell_ts")) & ((col("first_sell_ts").isNull()) | (col("first_sell_ts") > col("sell_ts")))), col("sell_ts")).otherwise(col("first_sell_ts")).cast(TimestampType()))
    df = df.select("account_id", "crypto_currency_id", "first_buy_ts", "first_sell_ts")

    df = df.withColumn("first_buy", from_utc_timestamp(to_timestamp("first_buy_ts"), "Asia/Jakarta").cast("date"))
    df = df.withColumn("first_sell", from_utc_timestamp(to_timestamp("first_sell_ts"), "Asia/Jakarta").cast("date"))

    first_sell_ts_temp = datetime.strptime(str(t_1), '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)
    df = df.withColumn("first_sell_temp", when((col("first_buy").isNotNull()) & (col("first_sell").isNull()), t_1).otherwise(col("first_sell")))
    df = df.withColumn("first_sell_ts_temp", when((col("first_buy_ts").isNotNull()) & (col("first_sell_ts").isNull()), first_sell_ts_temp).otherwise(col("first_sell_ts")))

    df = df.withColumn("day_diff", when((col("first_buy").isNotNull()) & (col("first_sell_temp").isNotNull()), datediff(col("first_sell_temp"), col("first_buy"))).otherwise(0))
    df = df.withColumn("hour_diff", when(((col("first_buy_ts").isNull()) | (col("first_sell_ts_temp").isNull())), 0).otherwise(round(((col("first_sell_ts_temp").cast(LongType())) - (col("first_buy_ts").cast(LongType())))/3600,0)))
    created = datetime.strptime(str(t_1), '%Y-%m-%d').replace(hour=00, minute=00, second=00, microsecond=000000)
    df = df.withColumn("created", lit(created))
    df = df.drop("first_sell_temp","first_sell_ts_temp")
    df.coalesce(3).write.mode('overwrite').save(config_data["crypto_metrics"]["metric_bucket"]+config_data["crypto_metrics"]["metric_folder"]+"/"+config_data["crypto_metrics"]["avg_hold_time_folder"]+"/dt="+str(t_1)+"/",format="csv", header=True)

    df_mongo = df.groupBy(["crypto_currency_id","created"]).agg(avg("day_diff").cast(LongType()).alias("avg_hold_days"), avg("hour_diff").cast(LongType()).alias("avg_hold_hours"))
    df_mongo = df_mongo.withColumn("crypto_currency_id", col("crypto_currency_id").cast(LongType()))
    write_asset_returns_to_mongo(df_mongo, config_data["crypto_metrics"]["mongo_avg_hold_time"], "Crypto Average Hold Time", config_data["crypto_metrics"]["mongo_avg_hold_time"]["write_format"], config_data["crypto_metrics"]["mongo_avg_hold_time"]["shardkey"])
    df_mongo = df_mongo.withColumnRenamed("crypto_currency_id","asset_id")
    df_mongo = df_mongo.withColumn("asset_category",lit("CRYPTO_CURRENCY"))
    txt = config_data["crypto_metrics"]["mongo_avg_hold_time"]["uri"]
    config_data["crypto_metrics"]["mongo_avg_hold_time"]["uri"] = txt.replace("crypto_avg_hold_time", "asset_avg_hold_time")
    write_asset_returns_to_mongo(df_mongo, config_data["crypto_metrics"]["mongo_avg_hold_time"], "Crypto Average Hold Time", config_data["crypto_metrics"]["mongo_avg_hold_time"]["write_format"], config_data["crypto_metrics"]["mongo_avg_hold_time"]["shardkey"])
    config_data["crypto_metrics"]["mongo_avg_hold_time"]["uri"] = txt.replace("crypto_avg_hold_time", "asset_metrics")
    write_asset_returns_to_mongo(df_mongo, config_data["crypto_metrics"]["mongo_avg_hold_time"], "Crypto Average Hold Time", "update", config_data["crypto_metrics"]["mongo_avg_hold_time"]["shardkey"])


if __name__ == "__main__":
    start_time = datetime.now()
    start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time,end_time,"crypto_avg_holding_time")

