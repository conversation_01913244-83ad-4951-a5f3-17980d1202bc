from common import *
from config import *


class CryptoFuturesPriceConsumer(object):

    def __init__(self):
        self.config = job_config_data["crypto_futures_price_consumer"]
        self.spark = spark_session_create("crypto_futures_price_consumser")
        self.bootstrap_servers = config_data["bootstrap_servers"]
        self.price_topic = self.config["topic"]
        self.raw_price_path = "s3a://{}/{}/".format(config_data["bucket"], self.config["raw_price_s3_path"])
        self.raw_price_checkpoint_path = "s3a://{}/{}/".format(config_data["bucket"],
                                                               self.config["raw_price_s3_checkpoint_location"])

    def read_stream_from_kafka_topic(self):
        try:
            logging.info("reading crypto futures price data from {} kafka topic/topics".format(self.price_topic))
            df = get_kafka_consumer_object(self.bootstrap_servers, self.price_topic) \
                .option("spark.streaming.kafka.maxRatePerPartition", 200) \
                .option("spark.streaming.receiver.maxRate", 200) \
                .load()
            logging.info("Successfully read crypto future price data")
            return df
        except Exception as e:
            logging.exception(e)
            raise e

    def transform_raw_stream(self, df):
        try:
            logging.info("transforming crypto futures price raw stream data")
            df = df.selectExpr("CAST(value AS STRING)", "topic", "partition", "offset", "timestamp")
            df = df.withColumn("value", f.from_json(df.value, MapType(StringType(), StringType())))
            cols_to_select = []
            keys = ["cryptoFuturePairSymbol", "cryptoFuturesId", "lastUpdatedAt", "lastTradedPrice", "prevPrice24h", "volume24h",
                    "fundingRate", "predictedFundingRate", "low24h", "high24h", "openInterest", "markPrice",
                    "pricePrecision"]
            for key in keys:
                key_in_snake_case = convert_camel_to_snake_case(key)
                cols_to_select.append(key_in_snake_case)
                df = df.withColumn(key_in_snake_case, df["value"].getItem(key))
            t_1 = get_date(config_data["offset"])
            df = df.select(cols_to_select)
            df = df.withColumn("last_updated_at", f.from_unixtime(col("last_updated_at") / 1000).cast("timestamp"))
            df = df.withColumn("date", lit(t_1))
            logging.info("successfully transformed crypto futures price raw stream data")
            return df
        except Exception as e:
            logging.exception(e)
            raise e

    def write_raw_stream_to_s3(self, df):
        try:
            logging.info("writing crypto future price raw stream data to s3 {} path".format(self.raw_price_path))

            raw_query = df.writeStream.outputMode("append") \
                .queryName("raw_crypto_future_price") \
                .format("csv") \
                .trigger(once=True) \
                .partitionBy("date").option("header", "true") \
                .option("startingOffsets", "earliest").option("truncate", "false") \
                .option("path", self.raw_price_path) \
                .option("checkpointLocation", self.raw_price_checkpoint_path) \
                .option("timestampFormat", "yyyy-MM-dd HH:mm:ss").option("inferSchema", "true").start()

            raw_query.awaitTermination()

            logging.info("Successfully written crypto future price raw stream data to s3")

        except Exception as e:
            logging.exception(e)
            raise e

    def start_consumer(self):
        df = self.read_stream_from_kafka_topic()
        df = self.transform_raw_stream(df)
        self.write_raw_stream_to_s3(df)


if __name__ == "__main__":
    start_time = datetime.now()

    consumer_obj = CryptoFuturesPriceConsumer()
    consumer_obj.start_consumer()

    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "crypto_futures_price_consumser")
