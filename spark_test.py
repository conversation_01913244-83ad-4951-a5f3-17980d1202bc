from pyspark.sql import functions as F
from pyspark.sql.window import Window
from pyspark.sql import SparkSession

# Initialize Spark
spark = SparkSession.builder.appName("FilterAccountData").getOrCreate()

# Account ID to filter
# [6825316, ********, ********, ********, 450068]
target_account_id = 6825316  # replace with your actual account_id

# Dummy S3 paths
start_asset_path = "s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/start_asset_position/"
all_txn_path = "s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/all_transactions/"
txn_batches_path = "s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/transaction_batches/dt=2025-06-29/hour=24/"
pnl_path = "s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/pnl/dt=2025-06-29/hour=24/"

# Read parquet files
start_asset = spark.read.option("recursiveFileLookup", "true").parquet(start_asset_path).orderBy("transaction_time")
all_txn = spark.read.option("recursiveFileLookup", "true").parquet(all_txn_path).orderBy("transaction_time")
txn_batches = spark.read.option("recursiveFileLookup", "true").parquet(txn_batches_path).orderBy("transaction_time")

# Read CSV file (add header and infer schema as needed)
pnl = spark.read.option("recursiveFileLookup", "true").csv(pnl_path, header=True, inferSchema=True,
                                                           quote='"', escape='"', multiLine=True)

# Filter by account_id
start_asset = start_asset.filter(F.col("account_id") == target_account_id)
all_txn = all_txn.filter(F.col("account_id") == target_account_id)
txn_batches = txn_batches.filter(F.col("account_id") == target_account_id)
pnl = pnl.filter(F.col("account_id") == target_account_id)

# Write Paths
start_asset_write_path = f"s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/validation/start_asset_position/{target_account_id}"
all_txn_write_path = f"s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/validation/all_transactions/{target_account_id}"
txn_batches_write_path = f"s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/validation/transaction_batches/{target_account_id}"
pnl_write_path = f"s3a://pluang-datalake-calculated-staging/trading_competition_q3_2025_prod/validation/pnl/{target_account_id}"

start_asset.coalesce(1).write.mode("append").csv(start_asset_write_path, header=True)
all_txn.coalesce(1).write.mode("append").csv(all_txn_write_path, header=True)
txn_batches.coalesce(1).write.mode("append").csv(txn_batches_write_path, header=True)
pnl.coalesce(1).write.mode("append").csv(pnl_write_path, header=True)
