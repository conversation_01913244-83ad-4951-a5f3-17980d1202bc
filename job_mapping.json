{"snapGoldReturns": {"srcFolder": "spark/jobs/snapGoldReturns", "files": ["snap_gold_returns.py", "gold_avg_holding_time.py", "gold_buy_sell_ratio.py"], "destFolder": "gold"}, "kafkaBatchConsumer": {"srcFolder": "spark/jobs/kafkaBatchConsumer", "files": ["batch_consumer.py", "check_kafka_last_msg_time.py"], "destFolder": "kafka-batch-consumer"}, "snapBappebtiWallet": {"srcFolder": "spark/jobs/snapBappebtiWallet", "files": ["snap_bappebti_wallet.py"], "destFolder": "bappebti_wallets"}, "snapCashIn": {"srcFolder": "spark/jobs/snapCashIn", "files": ["snap_cashin.py"], "destFolder": "cashin"}, "snapCashouts": {"srcFolder": "spark/jobs/snapCashouts", "files": ["snap_cashouts.py"], "destFolder": "cashout"}, "cryptoCurrencyPriceConsumer": {"srcFolder": "spark/jobs/cryptoCurrencyPriceConsumer", "files": ["crypto_currency_price_consumer.py"], "destFolder": "crypto_currency_price_consumer"}, "snapCryptoReturns": {"srcFolder": "spark/jobs/snapCryptoReturns", "files": ["snap_crypto_currency_returns.py", "crypto_avg_holding_time.py", "crypto_buy_sell_ratio.py", "crypto_currency_niv.py", "crypto_currency_niv_v2.py"], "destFolder": "crypto_currency"}, "snapForexReturns": {"srcFolder": "spark/jobs/snapForexReturns", "files": ["snap_forex_returns.py", "snap_forex_accounts.py", "snap_forex_topups_and_cashouts.py"], "destFolder": "forex"}, "snapFundReturns": {"srcFolder": "spark/jobs/snapFundReturns", "files": ["snap_fund_returns_usd.py"], "destFolder": "fund"}, "snapGlobalStocks": {"srcFolder": "spark/jobs/snapGlobalStocks", "files": ["snap_global_stock_dividend.py", "snap_global_stock_returns.py", "global_stock_buy_sell_ratio.py", "global_stock_avg_holding_time.py", "snap_global_stock_intra_day_accounts.py", "snap_global_stock_options_accounts.py"], "destFolder": "global_stock"}, "globalStockPriceConsumer": {"srcFolder": "spark/jobs/globalStockPriceConsumer", "files": ["global_stock_price_consumer.py"], "destFolder": "global_stocks_price_consumer"}, "snapAUMAndCashDiff": {"srcFolder": "spark/jobs/snapAUMAndCashDiff", "files": ["snap_gold_gift_and_withdrawal.py"], "destFolder": "gold_gift_and_withdrawal"}, "snapIndoStocks": {"srcFolder": "spark/jobs/snapIndoStocks", "files": ["snap_indo_stock_returns.py", "snap_indo_stock_dividend.py"], "destFolder": "indo_stocks"}, "profitAndLoss": {"srcFolder": "spark/jobs/profitAndLoss", "files": ["daily_aggregate_profit_loss_value.py", "daily_pnl.py", "monthly_pnl.py", "quarterly_pnl.py", "weekly_pnl.py", "yearly_pnl.py", "pnl_mongo_write.py"], "destFolder": "pnl_calculation"}, "snapPortfolio": {"srcFolder": "spark/jobs/snapPortfolio", "files": ["snap_portfolio.py", "snap_portfolio_mongo_write.py"], "destFolder": "portfolio"}, "structs": {"srcFolder": "spark/jobs/models", "files": ["structs.py"], "destFolder": ""}, "common": {"srcFolder": "spark/jobs/models", "files": ["common.py", "json_logger.py"], "destFolder": ""}, "asset_mongo": {"srcFolder": "spark/jobs/models", "files": ["asset_mongo_write.py", "pocket_mongo_write.py", "asset_mongo_write_in_pocket.py"], "destFolder": ""}, "masterTransaction": {"srcFolder": "spark/jobs/masterTransaction", "files": ["common.py", "crypto.py", "fx.py", "gss.py", "idss.py", "gold.py", "mutual_fund.py", "structs.py"], "destFolder": "master_transaction"}, "pluangPlusMemberValidity": {"srcFolder": "spark/jobs/pluangPlusMemberValidity", "files": ["snap_pluang_plus_member_validity.py"], "destFolder": "pluang_plus_member_validity"}, "eventsConsumer": {"srcFolder": "spark/jobs/eventsConsumer", "files": ["events_consumer.py", "event_consumer_flatten_data.py", "pluang_crypto_order_book_processor.py"], "destFolder": "pluang_events_consumer"}, "yieldCrypto": {"srcFolder": "spark/jobs/yieldCrypto", "files": ["crypto_yield_calculation.py"], "destFolder": "yield_crypto"}, "recurringOrder": {"srcFolder": "spark/jobs/recurringOrder", "files": ["frequency_based_recurring_orders_notification.py", "frequency_based_recurring_orders_snapshot.py"], "destFolder": "recurring_order"}, "snapPocketReturns": {"srcFolder": "spark/jobs/snapPocketReturns", "files": ["snap_global_stock_pocket_returns.py", "snap_crypto_currency_pocket_returns.py"], "destFolder": "pocket_returns"}, "aumCalculation": {"srcFolder": "spark/jobs/aumAndGtvCalculation", "files": ["cryptoTransaction/aum_crypto_transaction.py", "forexTransaction/aum_forex_transaction.py", "forexTransaction/aum_forex_leverage.py", "fundTransaction/aum_fund_transaction.py", "globalStockTransaction/aum_global_stock_transaction.py", "indoStock/aum_indo_stock_transaction.py", "goldTransaction/aum_gold_transaction.py", "models/aum_common.py", "models/aum_structs.py", "models/aum_data_path.py", "snapAum/snap_aum_calculation.py"], "destFolder": "aum_calculation"}, "gtvCalculation": {"srcFolder": "spark/jobs/aumAndGtvCalculation", "files": ["cryptoTransaction/gtv_crypto_transaction.py", "forexTransaction/gtv_forex_transaction.py", "fundTransaction/gtv_fund_transaction.py", "globalStockTransaction/gtv_global_stock_transaction.py", "indoStock/gtv_indo_stock_transaction.py", "goldTransaction/gtv_gold_transaction.py", "models/aum_common.py", "models/aum_structs.py", "models/aum_data_path.py", "snapGtv/snap_gtv_calculation.py", "snapGtv/snap_cashout_gtv.py"], "destFolder": "gtv_calculation"}, "dataValidation": {"srcFolder": "spark/jobs/dataValidation", "files": ["current_user_portfolio_validation.py", "user_pocket_portfolio_validation.py"], "destFolder": "data_validation"}, "config": {"srcFolder": "spark/jobs/models", "files": ["config.py"], "destFolder": ""}, "cleverTapProfile": {"srcFolder": "spark/jobs/cleverTapProfile", "files": ["clevertap_profile.py", "clevertap_user_properties.py"], "destFolder": "clevertap_profile"}, "snapAsset": {"srcFolder": "spark/jobs/snapAsset", "files": ["snap_asset.py"], "destFolder": "asset_snapshot"}, "pluangMission": {"srcFolder": "spark/jobs/pluangMission", "files": ["mission_buy_hold.py"], "destFolder": "pluang_mission"}, "snapLeverage": {"srcFolder": "spark/jobs/snapLeverage", "files": ["leverage_wallet_accounts.py"], "destFolder": "snap_leverage"}, "userProfile": {"srcFolder": "spark/jobs/userProfile", "files": ["snap_user_tag_mappings.py", "snap_gss_kyc_information.py"], "destFolder": "user_profile"}, "yieldForex": {"srcFolder": "spark/jobs/yieldForex", "files": ["forex_yield_calculation.py", "forex_yield_opt_in_details.py"], "destFolder": "yield_forex"}, "snapAccounts": {"srcFolder": "spark/jobs/snapAccounts", "files": ["snap_accounts.py"], "destFolder": "snap_accounts"}, "snapMissionFeesReversal": {"srcFolder": "spark/jobs/snapMissionFeesReversal", "files": ["gss_free_fee.py", "snap_gss_kyc_state_transaction.py"], "destFolder": "snap_mission_fees_reversal"}, "userDailyStatement": {"srcFolder": "spark/jobs/userDailyStatement", "files": ["daily_statement.py", "gss_daily_statement.py", "monthly_statement.py"], "destFolder": "user_daily_statement"}, "snapPrice": {"srcFolder": "spark/jobs/snapPrice", "files": ["snap_price.py"], "destFolder": "snap_price"}, "preDefineWatchLists": {"srcFolder": "spark/jobs/preDefineWatchList", "files": ["crypto_currency_daily_volume.py", "crypto_currency_price_watchlist.py", "global_stock_price_watchlist.py"], "destFolder": "pre_define_watch_list"}, "newrelic": {"srcFolder": "spark/jobs/models", "files": ["newrelic.py"], "destFolder": ""}, "utils": {"srcFolder": "spark/jobs/utils", "files": ["query.py"], "destFolder": "utils"}, "taxReports": {"srcFolder": "spark/jobs/taxReports", "files": ["crypto_tax_report.py", "fund_tax_report.py", "gold_tax_report.py", "us_stock_tax_report.py"], "destFolder": "tax_reports"}, "userPriceTiering": {"srcFolder": "spark/jobs/pluangUserPriceTiering", "files": ["user_price_tiering.py", "user_price_tiering_cache_clean.py"], "destFolder": "pluangUserPriceTiering"}, "copyToHdfs": {"srcFolder": "spark/jobs/copyToHdfs", "files": ["s3_to_hdfs.py"], "destFolder": "copy_to_hdfs"}, "tokopediaUsersGoldMigration": {"srcFolder": "spark/jobs/tokopediaUsersGoldMigration", "files": ["check_tokopedia_users_gold_migration.py"], "destFolder": "tokopedia_users_gold_migration"}, "cryptoFutures": {"srcFolder": "spark/jobs/cryptoFutures", "files": ["crypto_futures_price_consumer.py", "snap_crypto_preps_accounts.py"], "destFolder": "crypto_futures"}, "tradingCompetition": {"srcFolder": "spark/jobs/tradingCompetition", "files": ["aum_tier_upgrade.py", "batch_creation.py", "gtv_calculation.py", "pnl_calculation.py", "snap_assets.py", "snap_price.py", "mongo_write.py", "trading_competition_common.py", "trading_competition_config.py", "trading_competition_structs.py", "transaction_transformer.py", "redis_write.py", "portfolio_summary_report.py", "clevertap_user_properties_calculation.py", "initial_wallet_asset_balance_feed.py"], "destFolder": "trading_competition"}, "option_fee_waiver": {"srcFolder": "spark/jobs/OptionsFeeWaiver", "files": ["options_fee_waiver.py"], "destFolder": "OptionsFeeWaiver"}, "customer_risk_rating": {"srcFolder": "spark/jobs/CustomerRiskRating", "files": ["customer_risk_rating.py"], "destFolder": "CustomerRiskRating"}}