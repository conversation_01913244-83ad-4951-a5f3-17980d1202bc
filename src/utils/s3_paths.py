class S3Paths:
    # raw_data
    topups_raw_data = "raw_data/topups"
    cashouts_raw_data = "raw_data/cashouts"
    crypto_currency_transactions_raw_data = "raw_data/crypto_currency_transactions"
    crypto_currency_pocket_transactions_raw_data = "raw_data/crypto_currency_pocket_transactions"
    crypto_currency_wallet_transfers_raw_data = "raw_data/crypto_currency_wallet_transfers"
    crypto_future_transactions_raw_data = "raw_data/crypto_futures_transactions"
    global_stock_transactions_raw_data = "raw_data/global_stock_transactions"
    options_contract_transactions_raw_data = "raw_data/options_contract_transactions"
    gold_transactions_raw_data = "raw_data/gold_transactions"
    gold_gift_transactions_raw_data = "raw_data/gold_gift_transactions"
    gold_withdrawals_raw_data = "raw_data/gold_withdrawals"
    gold_loans_raw_data = "raw_data/gold_loans"
    installment_payment_raw_data = "raw_data/installment_payments"
    fund_transactions_raw_data = "raw_data/fund_transactions"
    forex_top_ups_raw_data = "raw_data/forex_top_ups"
    forex_cash_outs_raw_data = "raw_data/forex_cash_outs"
    forex_transactions_raw_data = "raw_data/forex_transactions"
    ph_global_stock_accounts_raw_data = "raw_data/ph_global_stock_accounts"
    ph_global_stock_returns_raw_data = "raw_data/ph_global_stock_returns"

    # snapshots
    gold_accounts = "gold_accounts/t_2_files"
    ph_global_stock_accounts = "ph_global_stock_accounts"
    ph_global_stock_returns = "ph_global_stock_returns"
    user_tag_mappings = "user_tag_mappings/t_2_files"
    accounts = "accounts/snapshots"

    topups = "snapshots/topups"
    cashouts = "snapshots/cashouts"
    crypto_currency_transactions = "snapshots/crypto_currency_transactions"
    crypto_currency_pocket_transactions = "snapshots/crypto_currency_pocket_transactions"
    crypto_currency_wallet_transfers = "snapshots/crypto_currency_wallet_transfers"
    crypto_future_transactions = "snapshots/crypto_future_transactions"
    global_stock_transactions = "snapshots/global_stock_transactions"
    options_contract_transactions = "snapshots/options_contract_transactions"
    gold_transactions = "snapshots/gold_transactions"
    gold_gift_transactions = "snapshots/gold_gift_transactions"
    gold_withdrawals = "snapshots/gold_withdrawals"
    gold_loans = "snapshots/gold_loans"
    installment_payment = "snapshots/installment_payments"
    fund_transactions = "snapshots/fund_transactions"
    forex_top_ups = "snapshots/forex_top_ups"
    forex_cash_outs = "snapshots/forex_cash_outs"
    forex_transactions = "snapshots/forex_transactions"

    # calculations
    gold_maintenance_fees_s3_folder = "gold_maintenance/gold_maintenance_fees"
    gold_maintenance_fees_intermediate_folder = "gold_maintenance/gold_maintenance_fees_intermediate"
    ph_global_stock_dividend_folder = "ph_data/global_stocks_dividend/snapshots"
