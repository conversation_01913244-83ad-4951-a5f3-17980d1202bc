import json
import os
from src.utils.custom_logger import init_logger
from src.utils.date_utils import DateUtils


class ConfigLoader:
    def __init__(self, env):
        self.env = env

    def load_secrets(self):
        if self.env == "local":
            path = os.path.join(os.path.dirname(__file__), "../../config/secret.json")
        else:
            path = "secret.json"
        with open(path, "r") as f:
            config = json.load(f)
        env = config.get("env", "dev")
        init_logger(env)
        return config

    def load_job_config(self):
        if self.env == "local":
            path = os.path.join(os.path.dirname(__file__), "../../config/config.json")
        else:
            path = "config.json"
        with open(path, "r") as f:
            config = json.load(f)
        return config

    def load_trading_competition_config(self):
        if self.env == "local":
            path = os.path.join(os.path.dirname(__file__), "../../config/trading_competition_config.json")
        else:
            path = "trading_competition_config.json"
        config = {}
        if os.path.isfile(path):
            with open(path, "r") as f:
                config = json.load(f)
        return config

    def load_config(self):
        secret_config = self.load_secrets()
        job_config = self.load_job_config()
        tc_config = self.load_trading_competition_config()
        config = {**job_config, **secret_config, **tc_config}
        if (config.get("bucket") is not None) and (self.env != "local"):
            config["bucket_path"] = "s3a://{}".format(secret_config.get("bucket"))
        else:
            config["bucket_path"] = secret_config.get("bucket")
        return config
