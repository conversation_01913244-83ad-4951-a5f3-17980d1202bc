from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException
from py4j.java_gateway import java_import

from src.utils.operations import Operations
from src.utils.constants import Constants
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.utils.io_utils import IOUtils
from src.utils.date_utils import DateUtils
from src.reference_data.user_properties import UserProperties
from src.schema.schema_dict import schema_dict


class SparkUtils:
    def __init__(self, app_nane):
        self.app_name = app_nane
        self.logger = get_logger()

    def create_spark_session(self):
        spark = SparkSession.builder \
            .appName(self.app_name) \
            .getOrCreate()
        self.logger.info("Spark session created")
        return spark

    def stop_spark(self, spark: SparkSession):
        spark.stop()

    def job_time_metrics(self, start_time, end_time, job_name):
        job_running_time = end_time - start_time
        job_running_time = job_running_time.total_seconds()
        self.logger.info("Total Time Taken for the job {} is: {}".format(job_name, job_running_time))


