from src.utils.custom_logger import get_logger


class JobMetrics:
    def __init__(self):
        self.logger = get_logger()

    def job_time_metrics(self, start_time, end_time, job_name):
        if any(x is None for x in (start_time, end_time, job_name)):
            raise ValueError("start_time, end_time, and job_name must not be None")
        job_running_time = end_time - start_time
        job_running_time = job_running_time.total_seconds()
        self.logger.info("Total Time Taken for the job {} is: {}".format(job_name, job_running_time))
