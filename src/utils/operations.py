from src.utils.spark_utils import *
from src.utils.python_utils import PythonUtils


class Operations:
    def __init__(self, spark: SparkSession):
        self.spark = spark

    def convert_columns_snake_to_camel_case(self, df):
        original_columns = df.columns
        for cols in original_columns:
            input_str = cols
            renamed_col = PythonUtils.convert_snake_to_camel_case(input_str)
            df = df.withColumnRenamed(cols, renamed_col)
        return df

    def de_dupe_dataframe(self, df, keys, over, **kwargs):
        if (kwargs.get("type") is not None) and (kwargs.get("type") == "asc"):
            window = Window.partitionBy([col(x) for x in keys]).orderBy(col(over).asc())
        else:
            window = Window.partitionBy([col(x) for x in keys]).orderBy(col(over).desc())
        df = df.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
        return df

    def apply_schema_from(self, target: DataFrame, source: DataFrame):
        schema = source.schema
        target = target.select(source.columns)
        for field in schema.fields:
            if field.name in target.columns:
                target = target.withColumn(field.name, col(field.name).cast(field.dataType))
        return target

    def get_union(self, df1, df2):
        if df1 is None:
            return df2
        elif df2 is None:
            return df1
        else:
            column = df1.columns
            df2 = df2.select(column)
            return df1.union(df2)

    def explode_col_df(self, df):
        keys_df = df.select(F.explode(F.map_keys(col("value")))).distinct()
        keys = list(map(lambda row: row[0], keys_df.collect()))
        key_cols = list(map(lambda x: col("value").getItem(x).alias(str(x)), keys))
        df = df.select(key_cols)
        return df

    def check_null_values(self, df, primary_key, filter_col=None):
        null_val_df = df.filter(col(filter_col).isNull()).select(primary_key).distinct().rdd.flatMap(lambda x: x).collect()
        return null_val_df
