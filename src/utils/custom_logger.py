# custom_logger.py
import json
import json_logging
import logging as root_logging
import sys
import os
from datetime import datetime
from typing import Optional
from logging import Logger

class CustomJSONLog(json_logging.JSONLogFormatter):
    def __init__(self, *args, **kwargs):
        self.env = kwargs.pop('env', 'dev')
        super(CustomJSONLog, self).__init__(*args, **kwargs)

    def format(self, record):
        utcnow = datetime.utcnow()
        return json.dumps({
            "timestamp": json_logging.util.iso_time_format(utcnow),
            "msg": record.getMessage(),
            "type": "log",
            "logger_name": record.name,
            "thread_name": record.threadName,
            "level": record.levelname,
            "module": record.module,
            "line_no": record.lineno,
            "env": self.env,
            "project": "PluangSparkBatchProcessingJobs"
        })


_logger: Optional[Logger] = None


def init_logger(env: str):
    global _logger
    if _logger is None:
        json_logging.init_non_web()
        formatter = CustomJSONLog(env=env)
        _logger = root_logging.getLogger("AppLogger")
        _logger.setLevel(root_logging.DEBUG)
        handler = root_logging.StreamHandler(sys.stdout)
        handler.setFormatter(formatter)
        _logger.addHandler(handler)


def get_logger() -> Logger:
    if _logger is None:
        raise RuntimeError("Logger not initialized. Call init_logger(env) first.")
    return _logger
