from src.utils.spark_utils import *


class GoldMaintenanceFees:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("gold_maintenance_fees")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.six_month_back_date = DateUtils.deduct_months(self.t_1, self.config["gold_maintenance_fees"]["months"])
        self.is_last_day_of_month = DateUtils.is_last_day_of_month(self.t_1)
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("Gold Maintenance initialised successfully with t_1: {}, t_2: {}, six_month_back_date: {}, "
                         "is_last_day_of_month: {}".format(self.t_1, self.t_2, self.six_month_back_date,
                                                           self.is_last_day_of_month))

    def select_cols(self, df, asset, **kwargs):
        df = df.select(kwargs.get("id", "id"), kwargs.get("account_id", "account_id"), col(kwargs.get("updated", "updated")).alias("updated"),
                       kwargs.get("transaction_time", "transaction_time"), lit(asset).alias("last_transaction_asset")) \
                .withColumn("last_transaction_timestamp", F.when(col("transaction_time").isNull(), col("updated")).otherwise(col("transaction_time"))) \
                .withColumnRenamed("id", "last_transaction_id")
        return df

    def read_gold_transactions(self):
        self.logger.info("reading gold transactions")
        gold_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS]))
        gold_transactions = self.select_cols(gold_transactions, "gold_transactions")
        self.logger.info("read gold transaction successfully")

        self.logger.info("reading installment_payments")
        installment_payments = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.installment_payment, self.t_1)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.PAID, Constants.CANCEL_HAS_PAID]))

        gold_loans = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_loans, self.t_1)) \
            .select("account_id", "user_id", col("id").alias("gold_loan_id"))
        installment_payments = installment_payments.join(gold_loans, on=["gold_loan_id"], how="left")
        installment_payments = self.select_cols(installment_payments, "installment_payments")
        self.logger.info("installment payment read successfully")

        self.logger.info("reading gold withdrawals")
        gold_withdrawals = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_withdrawals, self.t_1)) \
            .filter(col("status").isin([Constants.DELIVERED]))
        gold_withdrawals = self.select_cols(gold_withdrawals, "gold_withdrawals")
        self.logger.info("gold withdrawals read successfully")

        return gold_transactions.union(installment_payments).union(gold_withdrawals)

    def read_global_stock_transactions(self):
        self.logger.info("reading global stock transactions")
        global_stock_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS, Constants.PARTIALLY_FILLED]))
        global_stock_transactions = self.select_cols(global_stock_transactions, "global_stock_transactions")
        self.logger.info("read global stock transactions successfully")

        self.logger.info("reading options contract transactions")
        options_contract_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contract_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS, Constants.PARTIALLY_FILLED]))
        options_contract_transactions = self.select_cols(options_contract_transactions, "options_contract_transactions")
        self.logger.info("options contract transactions read successfully")

        return global_stock_transactions.union(options_contract_transactions)

    def read_crypto_currency_transactions(self):
        self.logger.info("reading crypto currency transactions")
        crypto_currency_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS, Constants.PARTIALLY_FILLED]))
        crypto_currency_transactions = self.select_cols(crypto_currency_transactions, "crypto_currency_transactions")
        self.logger.info("read crypto currency successfully")

        self.logger.info("reading crypto currency pocket transactions")
        crypto_currency_pocket_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_pocket_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS, Constants.PARTIALLY_FILLED]))
        crypto_currency_pocket_transactions = self.select_cols(crypto_currency_pocket_transactions, "crypto_currency_pocket_transactions")
        self.logger.info("crypto currency pocket transactions read successfully")

        self.logger.info("reading gold withdrawals")
        crypto_currency_wallet_transfers = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_wallet_transfers, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.DEPOSIT, Constants.WITHDRAWAL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS]))
        crypto_currency_wallet_transfers = self.select_cols(crypto_currency_wallet_transfers, "crypto_currency_wallet_transfers", updated="updated_at")
        self.logger.info("crypto currency wallet transfers read successfully")

        self.logger.info("reading crypto future transactions")
        crypto_future_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_future_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("exchange_status").isin([Constants.FILLED, Constants.PARTIALLY_FILLED]))
        crypto_future_transactions = self.select_cols(crypto_future_transactions, "crypto_future_transactions")
        self.logger.info("crypto future transactions read successfully")

        return crypto_currency_transactions.union(crypto_currency_pocket_transactions).union(crypto_currency_wallet_transfers) \
                    .union(crypto_future_transactions)

    def read_fund_transactions(self):
        self.logger.info("reading fund transactions")
        fund_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.fund_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.APPROVED]))
        fund_transactions = self.select_cols(fund_transactions, "fund_transactions")
        self.logger.info("read fund transactions successfully")
        return fund_transactions

    def read_forex_transactions(self):
        self.logger.info("reading forex transactions")
        forex_transactions = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_transactions, self.t_1)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.SUCCESS]))
        forex_transactions = self.select_cols(forex_transactions, "forex_transactions")
        self.logger.info("read forex transactions successfully")
        return forex_transactions

    def read_cash_transactions(self):
        self.logger.info("reading topups")
        topups = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.topups, self.t_1)) \
            .filter(col("status").isin([Constants.SUCCESS]))
        topups = self.select_cols(topups, "topups")
        self.logger.info("read topups successfully")

        self.logger.info("reading cashouts")
        cashouts = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.cashouts, self.t_1)) \
            .filter(col("status").isin([Constants.COMPLETED]))
        cashouts = self.select_cols(cashouts, "cashouts")
        self.logger.info("read cashouts successfully")

        self.logger.info("reading forex top ups")
        forex_topups = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_top_ups, self.t_1)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.COMPLETED]))
        forex_topups = self.select_cols(forex_topups, "forex_topups")
        self.logger.info("read forex top ups successfully")

        self.logger.info("reading forex cash outs")
        forex_cash_outs = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_cash_outs, self.t_1)) \
            .filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("status").isin([Constants.COMPLETED]))
        forex_cash_outs = self.select_cols(forex_cash_outs, "forex_cash_outs")
        self.logger.info("read forex cash outs successfully")

        return topups.union(cashouts).union(forex_topups).union(forex_cash_outs)

    def get_all_gold_accounts(self):
        self.logger.info("reading gold accounts")
        gold_accounts = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_accounts, self.t_1), None, False)
        gold_accounts = gold_accounts.filter(col("partner_id") == self.pluang_partner_id) \
            .filter(col("gold_balance") > 0) \
            .select("account_id", "user_id", "gold_balance", "partner_id")

        all_accounts = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.bucket_path, S3Paths.accounts, self.t_1), None, False)
        all_accounts = all_accounts.select(col("id").alias("account_id"))
        gold_accounts = gold_accounts.join(all_accounts, on=["account_id"], how="inner")
        return gold_accounts

    def get_last_txn_time(self):
        all_txn = self.read_gold_transactions()
        all_txn = all_txn.union(self.read_crypto_currency_transactions())
        all_txn = all_txn.union(self.read_fund_transactions())
        all_txn = all_txn.union(self.read_forex_transactions())
        all_txn = all_txn.union(self.read_global_stock_transactions())
        all_txn = all_txn.union(self.read_cash_transactions())
        last_txn = self.ops.de_dupe_dataframe(all_txn, ["account_id"], "last_transaction_timestamp")
        last_txn = last_txn.withColumn(
            "last_transaction_jkt_date",
            F.to_date(F.from_utc_timestamp("last_transaction_timestamp", Constants.JKT_TIMEZONE))
        )
        return last_txn

    def get_eligible_accounts(self, last_txn):
        plus_members = self.user_props.get_pluang_plus_members(self.t_1).select("account_id")
        gold_accounts = self.get_all_gold_accounts()

        eligible_gold_accounts = gold_accounts.join(
            plus_members,
            on=["account_id"],
            how="left_anti"
        ).join(
            last_txn,
            on=["account_id"],
            how="left"
        )
        eligible_gold_accounts = eligible_gold_accounts.filter(((col("last_transaction_jkt_date").isNull()) | (col("last_transaction_jkt_date") <= self.six_month_back_date)) & (col("user_id") > 0))
        return eligible_gold_accounts

    def create_kafka_events(self, eligible_accounts):
        publish_time = DateUtils.get_utc_timestamp()
        eligible_accounts = self.ops.convert_columns_snake_to_camel_case(eligible_accounts)
        eligible_accounts = eligible_accounts.withColumn("isPluangUser", lit(True))
        kafka_event = eligible_accounts.withColumn("referenceId", F.expr("uuid()"))
        kafka_event = kafka_event.withColumn("id", col("accountId"))
        cols_to_publish = kafka_event.columns
        kafka_event = kafka_event.withColumn("data", F.struct(cols_to_publish))
        kafka_event = kafka_event.withColumn("type", lit("gold_maintenance_fees"))
        kafka_event = kafka_event.withColumn("object", F.struct(["id", "type", "data"])).drop("type", "id")
        kafka_event = kafka_event.withColumn("type", lit("admin")).withColumn("id", lit(self.config["gold_maintenance_fees"]["admin_id"]))
        kafka_event = kafka_event.withColumn("actor", F.struct(["type", "id"])).drop("id", "type")
        kafka_event = kafka_event.withColumn("type", lit("account")).withColumn("id", col("accountId"))
        kafka_event = kafka_event.withColumn("target", F.struct(["type", "id"]))
        kafka_event = kafka_event.withColumn("x-request-id", col("referenceId"))
        kafka_event = kafka_event.withColumn("loggerContext", F.struct(["x-request-id"]))
        kafka_event = kafka_event.withColumn("verb", lit("create")).withColumn("eventId", col("referenceId")).withColumn("published", lit(publish_time))
        kafka_event = kafka_event.select("eventId", "published", "actor", "object", "target", "verb", "loggerContext", "accountId")
        kafka_columns = ["eventId", "published", "actor", "object", "target", "verb", "loggerContext"]
        kafka_event = kafka_event.withColumn("x-request-id",col("eventId"))
        kafka_event = kafka_event.select(col("accountId").cast(StringType()).alias("key"), F.to_json(F.struct(kafka_columns)).alias("value"), F.array(F.struct(lit("x-request-id").alias("key"), col("x-request-id").cast("binary").alias("value"))).alias("headers")).drop("x-request-id")
        # kafka_event = kafka_event.select(kafka_columns)
        return kafka_event

    def run(self):
        if self.is_last_day_of_month:
            last_txn = self.get_last_txn_time()
            self.io_utils.write_csv_file(last_txn, "{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_maintenance_fees_intermediate_folder, self.t_1))

            is_already_published = self.io_utils.read_json_data("{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_maintenance_fees_s3_folder, self.t_1), return_empty_if_file_not_present=True)
            if is_already_published is None:
                eligible_accounts = self.get_eligible_accounts(last_txn)

                kafka_events = self.create_kafka_events(eligible_accounts)
                count_kafka_events = kafka_events.count()
                part_file_batch_size = self.config["gold_maintenance_fees"]["part_file_batch_size"]
                num_partitions = max(1, count_kafka_events // part_file_batch_size)
                self.logger.info(f"Total eligible accounts count: {count_kafka_events}")
                # self.io_utils.write_data_in_kafka(kafka_events, self.config["gold_maintenance_fees_kafka_topic"])
                self.io_utils.write_json_file(kafka_events, "{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_maintenance_fees_s3_folder, self.t_1), partition=num_partitions)
        self.spark_utils.stop_spark(self.spark)
