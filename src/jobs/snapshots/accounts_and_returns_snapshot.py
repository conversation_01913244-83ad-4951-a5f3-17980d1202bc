from src.utils.spark_utils import *


class AccountsAndReturnsSnapshot:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config
        all_snapshot_groups = kwargs.get("snapshot_groups", "all")
        if all_snapshot_groups == "all":
            self.snapshot_groups = self.config["accounts_and_returns_snapshots"]["groups"].keys()
        else:
            self.snapshot_groups = kwargs.get("snapshot_groups").split(",")

        # get utility objects
        self.spark_utils = SparkUtils("accounts_and_returns_snapshot")
        self.spark = self.spark_utils.get_spark()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.logger.info("Accounts and Returns Snapshot initialised successfully with t_1: {}, t_2: {}"
                         .format(self.t_1, self.t_2))

    def read_file(self, path, file_type, schema=None):
        df = None
        if file_type == "raw_json":
            df = self.io_utils.read_json_data(path, is_raw=True, return_empty_if_file_not_present=True, schema=schema)
        elif file_type == "deleted_raw":
            df = self.io_utils.read_deleted_records(path)
        elif file_type == "csv":
            df = self.io_utils.read_csv_file(path, schema=schema, return_empty=True)
        elif file_type == "json":
            df = self.io_utils.read_json_data(path, is_raw=False, schema=schema, return_empty_if_file_not_present=True)
        elif file_type == "parquet":
            df = self.io_utils.read_parquet_data(path)
        else:
            self.logger.warning("file_type: {} is not a valid file type!".format(file_type))
        return df

    def write_file(self, df, path, file_type, num_of_partitions):
        if file_type == "csv":
            self.io_utils.write_csv_file(df, path, partition=num_of_partitions)
        elif file_type == "json":
            self.io_utils.write_json_file(df, path, partition=num_of_partitions)
        elif file_type == "parquet":
            self.io_utils.write_parquet_file(df, path, partition=num_of_partitions)
        else:
            self.logger.warning("file_type: {} is not a valid file type!".format(file_type))

    def snapshot_asset(self, asset):
        self.logger.info("Starting snapshot for: {}".format(asset))
        allow_delete = False
        snapshot_folder = asset
        raw_data_folder = asset
        snapshot_file_type = "csv"
        de_dupe_key = ["id"]
        de_dupe_on = "updated"
        raw_file_type = "raw_json"
        asset_snapshot_config = self.config["accounts_and_returns_snapshots"]["snapshot_config"].get(asset)
        num_of_partitions = self.config["accounts_and_returns_snapshots"]["partitions"].get(asset, 1)
        asset_schema = schema_dict.get(asset, None)
        if asset_snapshot_config is not None:
            allow_delete = asset_snapshot_config.get("allow_delete", allow_delete)
            snapshot_folder = asset_snapshot_config.get("snapshot_folder", snapshot_folder)
            raw_data_folder = asset_snapshot_config.get("raw_data_folder", raw_data_folder)
            snapshot_file_type = asset_snapshot_config.get("snapshot_file_type", snapshot_file_type)
            de_dupe_key = asset_snapshot_config.get("de_dupe_key", de_dupe_key)
            de_dupe_on = asset_snapshot_config.get("de_dupe_on", de_dupe_on)
            raw_file_type = asset_snapshot_config.get("raw_file_type", raw_file_type)

        prev_snapshot_file_path = "{}/{}/dt={}/".format(self.bucket_path, snapshot_folder, self.t_2)
        snapshot_file_path = "{}/{}/dt={}/".format(self.bucket_path, snapshot_folder, self.t_1)
        self.logger.info("prev snapshot file path is: {} and new snapshot file path is: {}".format(prev_snapshot_file_path, snapshot_file_path))
        prev_snapshot = self.read_file(prev_snapshot_file_path, snapshot_file_type, schema=asset_schema)

        raw_file_path = "{}/raw_data/{}/dt={}/".format(self.bucket_path, raw_data_folder, self.t_1)
        self.logger.info("raw file path: {}".format(raw_file_path))
        raw = self.read_file(raw_file_path, raw_file_type, schema=asset_schema)
        if raw is None:
            snapshot = prev_snapshot
        else:
            if set(prev_snapshot.columns) != set(raw.columns):
                self.logger.warning("There is mismatch in snapshot and raw file columns, diff columns are : {} for tha asset: {}".format(list(set(prev_snapshot.columns).symmetric_difference(set(raw.columns))), asset))
            raw = self.ops.apply_schema_from(raw, prev_snapshot)
            snapshot = prev_snapshot.union(raw)

        if not allow_delete and "id" in snapshot.columns:
            deleted_record_path = "{}/raw_data/{}/dt={}/".format(self.bucket_path, raw_data_folder, self.config["deleted_data_date_folder"])
            deleted_records = self.read_file(deleted_record_path, "deleted_raw")
            snapshot = snapshot.filter(~col("id").isin(deleted_records))

        snapshot = self.ops.de_dupe_dataframe(snapshot, de_dupe_key, de_dupe_on)
        self.write_file(snapshot, snapshot_file_path, snapshot_file_type, num_of_partitions)
        self.logger.info("Successfully snapshotted for: {}".format(asset))

    def run(self):

        self.logger.info("All snapshot groups are: {}".format(self.snapshot_groups))

        for snapshot_group in self.snapshot_groups:
            snapshot_assets = self.config["accounts_and_returns_snapshots"]["groups"][snapshot_group]
            self.logger.info("Starting snapshoting of {} group".format(snapshot_assets))
            for asset in snapshot_assets:
                self.snapshot_asset(asset)
        self.spark_utils.stop_spark(self.spark)

