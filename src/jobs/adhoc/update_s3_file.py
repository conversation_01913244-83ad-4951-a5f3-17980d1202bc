from src.utils.spark_utils import *


class UpdateS3File:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("update_s3_file")
        self.spark = self.spark_utils.get_spark()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]

        self.operation_type = kwargs.get("operation_type", "updatecol")

        self.input_file_path = kwargs.get("input_file_path", None)
        self.current_file_path = kwargs.get("current_file_path", None)
        self.output_file_path = kwargs.get("output_file_path", None)
        self.output_file_partitions = int(kwargs.get("output_file_partitions", "1"))

        self.input_file_type = kwargs.get("input_file_type", "json")
        self.current_file_type = kwargs.get("current_file_type", "parquet")
        self.output_file_type = kwargs.get("output_file_type", "parquet")

        self.de_dupe_key = kwargs.get("de_dupe_key", "id").split(",")
        self.de_dupe_on = kwargs.get("de_dupe_on", "updated")
        self.update_on = kwargs.get("update_on", "id").split(",")
        self.delete_cols = kwargs.get("delete_cols", "") .split(",")

        self.logger.info("UpdateS3File initialised successfully with t_1: {}, t_2: {}".format(self.t_1, self.t_2))

    def read_file(self, path, file_type, schema=None):
        df = None
        if file_type == "raw_json":
            df = self.io_utils.read_json_data(path, is_raw=True, return_empty_if_file_not_present=True, schema=schema)
        elif file_type == "deleted_raw":
            df = self.io_utils.read_deleted_records(path)
        elif file_type == "csv":
            df = self.io_utils.read_csv_file(path, schema=schema, return_empty=True)
        elif file_type == "json":
            df = self.io_utils.read_json_data(path, is_raw=False, schema=schema, return_empty_if_file_not_present=True)
        elif file_type == "parquet":
            df = self.io_utils.read_parquet_data(path)
        else:
            self.logger.warning("file_type: {} is not a valid file type!".format(file_type))
        return df

    def write_file(self, df, path, file_type, num_of_partitions):
        if file_type == "csv":
            self.io_utils.write_csv_file(df, path, partition=num_of_partitions)
        elif file_type == "json":
            self.io_utils.write_json_file(df, path, partition=num_of_partitions)
        elif file_type == "parquet":
            self.io_utils.write_parquet_file(df, path, partition=num_of_partitions)
        else:
            self.logger.warning("file_type: {} is not a valid file type!".format(file_type))

    def update_cols(self):
        if self.input_file_path is not None:
            try:
                df_input = self.read_file(self.input_file_path, self.input_file_type)
                cols_to_update = list(set(df_input.columns) - set(self.update_on))
                self.logger.info("Cols to update are: {}".format(cols_to_update))

                for cl in cols_to_update:
                    df_input = df_input.withColumnRenamed(cl, "{}_updated".format(cl))

                df_current = self.read_file(self.current_file_path, self.current_file_type)
                self.logger.info("Current file count is: {}".format(df_current.count()))

                df_joined = df_current.join(df_input, on=self.update_on, how="left")
                self.logger.info("Joined count is: {}".format(df_joined.count()))

                for cl in cols_to_update:
                    df_joined = df_joined.withColumn(cl, col("{}_updated".format(cl)))

                for cl in cols_to_update:
                    df_joined = df_joined.drop("{}_updated".format(cl))

                self.write_file(df_joined, self.output_file_path, self.output_file_type, self.output_file_partitions)
                self.logger.info("Cols updated successfully! Total Count in updated file is: {}".format(df_joined.count()))
            except Exception as e:
                self.logger.warning("An error occurred while updating cols: {}".format(repr(e)))
        else:
            self.logger.warning("Input: {} file path is not provided".format(self.input_file_path))

    def delete_columns(self):
        if self.current_file_path is not None:
            try:
                df_current = self.read_file(self.current_file_path, self.input_file_type)
                self.logger.info("All columns to be deleted are: {}".format(self.delete_cols))
                df_current = df_current.drop(*self.delete_cols)
                self.write_file(df_current, self.output_file_path, self.output_file_type, self.output_file_partitions)
                self.logger.info("Cols deleted successfully!")
            except Exception as e:
                self.logger.warning("An error occurred while deleting cols: {}".format(repr(e)))
        else:
            self.logger.warning("Input: {} file path is not provided".format(self.input_file_path))

    def merge_files(self):
        if self.input_file_path is not None and self.output_file_path is not None:
            try:
                df_input = self.read_file(self.input_file_path, self.input_file_type)
                self.logger.info("Input file count is: {}".format(df_input.count()))

                df_current = self.read_file(self.current_file_path, self.current_file_type)
                self.logger.info("Current file count is: {}".format(df_current.count()))

                df_input = self.ops.apply_schema_from(df_input, df_current)
                df_output = df_current.union(df_input)

                df_output = self.ops.de_dupe_dataframe(df_output, self.de_dupe_key, self.de_dupe_on)
                self.logger.info("Output file count is: {}".format(df_output.count()))

                self.write_file(df_output, self.output_file_path, self.output_file_type, self.output_file_partitions)
                self.logger.info("File merged successfully!")
            except Exception as e:
                self.logger.warning("An error occurred while merging file: {}".format(repr(e)))
        else:
            self.logger.warning("Either input: {} or output: {} files path is not provided".format(self.input_file_path, self.output_file_path))

    def convert_file_type(self):
        if self.input_file_path is not None and self.output_file_path is not None:
            try:
                df = self.read_file(self.input_file_path, self.input_file_type)
                self.write_file(df, self.output_file_path, self.output_file_type, self.output_file_partitions)
                self.logger.info("File converted successfully!")
            except Exception as e:
                self.logger.warning("An error occurred while converting file type: {}".format(repr(e)))
        else:
            self.logger.warning("Either input: {} or output: {} files path is not provided".format(self.input_file_path, self.output_file_path))

    def run(self):
        if self.operation_type.lower() == "updatecol":
            self.update_cols()
        elif self.operation_type.lower() == "mergefile":
            self.merge_files()
        elif self.operation_type.lower() == "deletecol":
            self.delete_columns()
        elif self.operation_type.lower() == "convertfiletype":
            self.convert_file_type()
        else:
            self.logger.warning("Given operation type is not valid!")
        self.spark_utils.stop_spark(self.spark)
