from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.jobs.trading_competition.trading_competition_structs import schema_for_batch, schema_for_all_transactions
import builtins
python_round = builtins.round


class BatchCreation:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("transaction_batch")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # Write Paths
        self.bucket_path = self.config.get("bucket_path")

        self.batch_path = "{}/{}".format(self.bucket_path, self.config["batches"]["batch_file_path"])
        self.transactions_overwrite_hdfs_path = "hdfs:///{}/".format(self.config["batches"]["all_transaction_file_path"])
        self.transactions_snapshot_path = "{}/{}/".format(self.bucket_path, self.config['snapshot_path'])
        self.transactions_overwrite_path = "{}/{}/".format(self.bucket_path, self.config['batches']['all_transaction_file_path'])

        # Handling Dates & Timestamps
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(
            self.config["trading_competition"]["start_time"])
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2))

        self.key_cols = ["account_id"]
        self.value_cols = ["asset_type", "user_id", "leverage", "asset_id","asset_sub_type", "transaction_id", "created", "updated", "executed_quantity", "executed_unit_price",
                             "updated_executed_quantity", "updated_executed_unit_price", "executed_total_price", "fees", "transaction_type", "status", "currency_to_idr", "transaction_time",
                             "current_unit_price", "current_currency_to_idr", "remaining_quantity",
                             "realized_pnl", "unrealized_pnl", "execution_time", "row_number", "ignore_for_gtv",
                           "trading_competition_start_time", "forex_price", "usdt_price"]

    @staticmethod
    def create_batches(all_txn):
        buy_types = ["BUY", "LONG_OPEN", "DEPOSIT", "RECEIVE", "AIRDROP"]
        sell_types = ["SELL", "LONG_CLOSE", "WITHDRAWAL", "SEND", "GOLD_WITHDRAWAL", "AIRDROP_SELL"]
        all_sorted_txn = sorted(all_txn, key=lambda txn: txn["row_number"])
        result = []
        forex_accounts = 0.0
        leverage_wallet_accounts = 0.0
        crypto_margin_wallets = 0.0
        bappebti_wallets = 0.0
        txn_position = 0
        prev_position = 0
        prev_position_time = None
        position_open_time = None
        all_sorted_txn_count = len(all_sorted_txn)
        for index, transaction in enumerate(all_sorted_txn):
            row = transaction.asDict()
            currency_to_idr = row["currency_to_idr"]
            executed_unit_price = row["updated_executed_unit_price"]
            forex_price = row["forex_price"]
            usdt_price = row["usdt_price"]
            executed_quantity = row["updated_executed_quantity"]
            transaction_time = row["transaction_time"]
            leverage = row["leverage"] if row["leverage"] > 0 else 1
            prev_position_time = transaction_time if prev_position_time is None else prev_position_time

            if row["asset_type"] == "crypto_futures":
                row["realized_pnl"] = int(-1 * row["fees"])
                row["remaining_quantity"] = executed_quantity
                running_quantity = 0.0
                for result_row in result:
                    if (result_row["asset_id"] == row["asset_id"]) and (result_row["asset_sub_type"] == row["asset_sub_type"]):
                        if result_row["transaction_type"] in buy_types:
                            running_quantity = running_quantity + result_row["updated_executed_quantity"]
                        else:
                            running_quantity = running_quantity - result_row["updated_executed_quantity"]
                if row["transaction_type"] in buy_types:
                    new_running_quantity = running_quantity + row["updated_executed_quantity"]
                else:
                    new_running_quantity = running_quantity - row["updated_executed_quantity"]
                new_running_quantity = python_round(new_running_quantity, 4)
                running_quantity = python_round(running_quantity, 4)
                if (new_running_quantity >= 0 and running_quantity < 0) or (new_running_quantity <= 0 and running_quantity > 0):
                    for result_row in result:
                        if (result_row["asset_id"] == row["asset_id"]) and (result_row["asset_sub_type"] == row["asset_sub_type"]):
                            remaining_quantity = result_row["remaining_quantity"] if result_row["transaction_type"] in buy_types else -1 * result_row["remaining_quantity"]
                            result_row["realized_pnl"] += int(remaining_quantity * (
                                    executed_unit_price * currency_to_idr -
                                    result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                            ))
                            result_row["remaining_quantity"] = 0.0
                    row["remaining_quantity"] = abs(new_running_quantity)
            # elif row["transaction_type"] == "INITIAL_WALLET_BALANCE":
            #     forex_accounts = row["forex_accounts"]
            #     leverage_wallet_accounts = row["leverage_wallet_accounts"]
            #     crypto_margin_wallets = row["crypto_margin_wallets"]
            #     bappebti_wallets = row["bappebti_wallets"]
            #
            # elif row["transaction_type"] == "WALLET_UPDATE":
            #     if row["asset_sub_type"] == "forex_accounts":
            #         forex_accounts = row["executed_quantity"]
            #     elif row["asset_sub_type"] == "leverage_wallet_accounts":
            #         leverage_wallet_accounts = row["executed_quantity"]
            #     elif row["asset_sub_type"] == "crypto_margin_wallets":
            #         crypto_margin_wallets = row["executed_quantity"]
            #     elif row["asset_sub_type"] == "bappebti_wallets":
            #         bappebti_wallets = row["executed_quantity"]

            elif row["transaction_type"] in buy_types:
                row["remaining_quantity"] = executed_quantity
                txn_position = txn_position + int((executed_quantity * executed_unit_price * currency_to_idr) / leverage)
            elif row["transaction_type"] in sell_types:
                remaining_sell_quantity = executed_quantity

                for result_row in result:
                    if (result_row["transaction_type"] in buy_types) and (result_row["asset_type"] == row["asset_type"]) and (result_row["leverage"] == row["leverage"]) and (result_row["asset_id"] == row["asset_id"]) and (result_row["remaining_quantity"] > 0) and (remaining_sell_quantity > 0):
                        quantity_sold = min(result_row["remaining_quantity"], remaining_sell_quantity)
                        remaining_sell_quantity -= quantity_sold
                        result_row["remaining_quantity"] -= quantity_sold

                        if (row["asset_sub_type"] != "crypto_currency_wallet_transfers") and (row["asset_sub_type"] != "gold_withdrawals") and (row["transaction_type"] != "AIRDROP_SELL"):
                            result_row["realized_pnl"] += int(quantity_sold * (
                                    executed_unit_price * currency_to_idr -
                                    result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                            ))
                        txn_position = txn_position - int((quantity_sold * result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]) / leverage)
                row["remaining_quantity"] = remaining_sell_quantity
            # row["bappebti_wallets"] = bappebti_wallets
            # row["crypto_margin_wallets"] = crypto_margin_wallets
            # row["leverage_wallet_accounts"] = leverage_wallet_accounts
            # row["forex_accounts"] = forex_accounts
            # wallet_position = int(forex_accounts * forex_price + leverage_wallet_accounts * forex_price + crypto_margin_wallets * usdt_price + bappebti_wallets)

            # row["position"] = wallet_position + txn_position
            row["position"] = txn_position
            if row["position"] <= 100000:
                row["position"] = 0
            row["position_open_time"] = 0
            if (row["position"] == 0) and (position_open_time is not None):
                row["position_open_time"] = int((transaction_time - position_open_time).total_seconds()) - 1
                position_open_time = None
            elif (row["position"] != 0) and (index == all_sorted_txn_count -1):
                position_open_time = transaction_time if position_open_time is None else position_open_time
                row["position_open_time"] = int((row["execution_time"] - position_open_time).total_seconds()) - 1
            elif (row["position"] != 0) and (position_open_time is None):
                position_open_time = transaction_time

            row["prev_position"] = prev_position
            row["position_time_in_sec"] = int((transaction_time - prev_position_time).total_seconds()) - 1
            row["position_time_in_sec"] = 0 if row["position_time_in_sec"] < 0 else row["position_time_in_sec"]
            prev_position_time = transaction_time
            prev_position = row["position"]
            result.append(row)

        for index, result_row in enumerate(result):
            remaining_quantity = result_row["remaining_quantity"]
            if result_row["asset_sub_type"] == "crypto_future_trades":
                remaining_quantity = remaining_quantity if result_row["transaction_type"] in buy_types else -1 * remaining_quantity
                result_row["unrealized_pnl"] = int(remaining_quantity * (
                        result_row["current_unit_price"] * result_row["current_currency_to_idr"]
                        - result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                ))
            elif (result_row["transaction_type"] in buy_types) and (result_row["remaining_quantity"] > 0):
                result_row["unrealized_pnl"] = int(remaining_quantity * (
                        result_row["current_unit_price"] * result_row["current_currency_to_idr"]
                        - result_row["updated_executed_unit_price"] * result_row["currency_to_idr"]
                ))
            result_row["position_x_time"] = int(result_row["prev_position"] * result_row["position_time_in_sec"])
            if index == all_sorted_txn_count -1:
                result_row["position_x_time"] = result_row["position_x_time"] + result_row["position"] * \
                                                (int((result_row["execution_time"] - result_row["transaction_time"]).total_seconds()) - 1)

        return result

    def get_batches(self, transactions):
        transactions = transactions.withColumn("remaining_quantity", lit(0.0)) \
            .withColumn("realized_pnl", lit(0)) \
            .withColumn("unrealized_pnl", lit(0))

        transactions = transactions.withColumn("execution_time", lit(self.utc_cutoff_ts))
        transactions = transactions.withColumn("trading_competition_start_time", lit(self.trading_competition_start_time))
        transactions = transactions.withColumn("priority", when(col("transaction_type") == "INITIAL_WALLET_BALANCE", 1).otherwise(2))
        window_spec = Window.partitionBy(self.key_cols).orderBy(col("transaction_time").asc(), col("priority").asc())
        df_with_row_number = transactions.withColumn("row_number", row_number().over(window_spec)).drop("priority")

        grouped_df = (
            df_with_row_number
            .groupBy(self.key_cols)
            .agg(F.collect_list(F.struct(*[col(c) for c in self.value_cols])).alias("transactions"))
        )

        batch_udf = F.udf(self.create_batches, schema_for_batch)

        batches = (
            grouped_df
            .withColumn("batch_result", batch_udf(col("transactions")))
            .select("account_id", "batch_result")
        )

        exploded_batch = batches.withColumn("batch_data", F.explode(col("batch_result"))).select(
            "account_id", "batch_data.*"
        )
        exploded_batch = exploded_batch.withColumn("remaining_quantity", F.round(col("remaining_quantity"), 12))
        return exploded_batch

    def cast_fields(self,all_txn):
        all_txn = all_txn.withColumn("current_unit_price", round(col("current_unit_price"), 12)) \
            .withColumn("executed_quantity", round(col("executed_quantity"), 12)) \
            .withColumn("remaining_quantity", round(col("remaining_quantity"), 12)) \
            .withColumn("executed_unit_price", round(col("executed_unit_price"), 12)) \
            .withColumn("executed_total_price", round(col("executed_total_price"), 12)) \
            .withColumn("updated_executed_quantity", round(col("updated_executed_quantity"), 12)) \
            .withColumn("updated_executed_unit_price", round(col("updated_executed_unit_price"), 12)) \
            .withColumn("current_currency_to_idr", col("current_currency_to_idr").cast(LongType())) \
            .withColumn("currency_to_idr", col("currency_to_idr").cast(LongType())) \
            .withColumn("forex_price", col("forex_price").cast(LongType())) \
            .withColumn("usdt_price", col("usdt_price").cast(LongType())) \
            .withColumn("realized_pnl", col("realized_pnl").cast(LongType())) \
            .withColumn("unrealized_pnl", col("unrealized_pnl").cast(LongType()))
            # .withColumn("bappebti_wallets", col("bappebti_wallets").cast(LongType())) \
            # .withColumn("forex_accounts", round(col("forex_accounts"), 4)) \
            # .withColumn("leverage_wallet_accounts", round(col("leverage_wallet_accounts"), 4)) \
            # .withColumn("crypto_margin_wallets", round(col("crypto_margin_wallets"), 4)) \
        return all_txn

    def add_gtv_multiplier(self, batches):
        # options contracts to get the global_stock_id
        options_contracts = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "options_contracts", self.t_1, self.h_1), None, False)
        options_contracts = options_contracts.select(col("id").alias("asset_id"), col("global_stock_id"), lit("global_stock_options").alias("asset_type"))

        # global stock prices
        global_stock_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['global_stock']['price_path'], self.t_1, self.h_1))
        global_stock_price = global_stock_price.withColumn("mid_price", round(col("mid_price"), 2))
        global_stock_price = global_stock_price.select(col("_id").alias("global_stock_id"), col("mid_price").alias("gtv_multiplier"))

        # join options contracts with global stock price
        options_contracts = options_contracts.join(global_stock_price, on=["global_stock_id"], how="left").fillna({'gtv_multiplier': 1}).drop("global_stock_id")

        # join actual batches with multiplier
        batches = batches.join(options_contracts, on=["asset_id", "asset_type"], how="left").fillna({'gtv_multiplier': 1})

        # use previously assiged price for gtv multiplier if it is already assigned
        try:
            previous_options_txn_price = self.io_utils.read_parquet_data("{}/dt={}/hour={}/".format(self.batch_path, self.t_2, self.h_2), None, False)
            previous_options_txn_price = previous_options_txn_price.filter(col("asset_sub_type") == "options_contract_transactions")
            previous_options_txn_price = previous_options_txn_price.select("asset_sub_type", "transaction_id", col("gtv_multiplier").alias("prev_gtv_multiplier"))
            batches = batches.join(previous_options_txn_price, on=["asset_sub_type", "transaction_id"], how="left")
            batches = batches.withColumn("gtv_multiplier", when(col("prev_gtv_multiplier").isNotNull(), col("prev_gtv_multiplier")).otherwise(col("gtv_multiplier"))).drop("prev_gtv_multiplier")
        except Exception as e:
            self.logger.info("Previous Batch file not found for t_2: {} and h_2: {}".format(self.t_2, self.h_2))
        return batches

    def validate_batches(self, batches):
        excluded_asset_sub_types = self.config["split_supported_asset_sub_types"]
        excluded_asset_sub_types_str = ", ".join(f"'{t}'" for t in excluded_asset_sub_types)
        mismatch_count = batches.filter(
            F.expr(f"(executed_quantity != updated_executed_quantity OR executed_unit_price != updated_executed_unit_price) "
                 f"AND asset_sub_type NOT IN ({excluded_asset_sub_types_str})")
        ).count()
        if mismatch_count > 0:
            self.logger.error("count of transactions for which executed_quantity and updated_executed_quantity or executed_unit_price and updated_executed_unit_price is not matching is: {}".format(mismatch_count))

    def execute(self):
        all_txn = self.io_utils.read_parquet_data(self.transactions_overwrite_path, schema_for_all_transactions, False)
        all_txn = all_txn.filter(col("asset_type").isin(["gold", "wallet", "crypto_currency", "global_stocks", "global_stock_options", "crypto_futures"]))
        batches = self.get_batches(all_txn)
        batches = self.cast_fields(batches)
        batches = self.add_gtv_multiplier(batches)
        batches = batches.select("asset_id","asset_type","account_id","user_id","transaction_id","asset_sub_type","created","updated","transaction_time","transaction_type","leverage","status","fees","executed_total_price","executed_quantity","executed_unit_price","currency_to_idr","current_unit_price","current_currency_to_idr","forex_price","usdt_price","remaining_quantity","realized_pnl","unrealized_pnl","execution_time","trading_competition_start_time","row_number","ignore_for_gtv","gtv_multiplier", "updated_executed_quantity", "updated_executed_unit_price")
        self.validate_batches(batches)
        self.io_utils.write_parquet_file(batches, "{}/dt={}/hour={}/".format(self.batch_path, self.t_1, self.h_1), 5)

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)

