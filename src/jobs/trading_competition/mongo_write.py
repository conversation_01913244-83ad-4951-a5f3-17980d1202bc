from src.utils.spark_utils import *
from src.utils.date_utils import *


class MongoWrite:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("Mongo Write")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # Write Paths
        self.bucket_path = self.config.get("bucket_path")
        self.tier_snapshot_path = "{}/{}".format(self.bucket_path, self.config['aum_tier_upgrade']['tier_snapshot_path'])
        self.pnl_path = "{}/{}".format(self.bucket_path, self.config['pnl_path'])
        self.gtv_path = "{}/{}".format(self.bucket_path, self.config['gtv_path'])
        self.tags_path = "{}/{}".format(self.bucket_path, self.config['tags_path'])
        
        # Handling Dates & Timestamps
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2))

    def build_user_properties(self):
        user_details = self.io_utils.read_csv_file("{}/dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1, self.h_1))
        user_details = user_details.select("account_id", "user_id", "is_upgradable", "tier", "eligible_upgrade_tier", "user_action_for_tier_upgrade", "aum", "name", "email", "trading_competition_id")

        pnl = self.io_utils.read_csv_file("{}/dt={}/hour={}/".format(self.pnl_path, self.t_1, self.h_1))
        pnl = pnl.select("account_id", "pnl", "pnl_rank")

        gtv = self.io_utils.read_csv_file("{}/dt={}/hour={}/".format(self.gtv_path, self.t_1, self.h_1))
        gtv = gtv.select("account_id", "total_gtv", "gtv_rank")

        try:
            tags = self.io_utils.read_csv_file("{}".format(self.tags_path), None, False, None)
            tags = tags.select("account_id", "tag").distinct()
            tags = tags.groupBy(["account_id"]).agg(F.collect_list("tag").alias("tags"))
        except Exception as e:
            schema = StructType([
                StructField("account_id", LongType(), True)
            ])
            tags = self.spark.createDataFrame([], schema)
            tags = tags.withColumn("tags", lit(None))

        user_details = user_details.join(pnl, on=["account_id"], how="left")
        user_details = user_details.join(gtv, on=["account_id"], how="left")
        user_details = user_details.join(tags, on=["account_id"], how="left")
        user_details = user_details.withColumn(
            "tags",
            when(col("tags").isNull(), F.array([])).otherwise(col("tags"))
        )

        current_timestamp = DateUtils.get_utc_timestamp()
        user_details = user_details.withColumn("updated_at", lit(current_timestamp))
        user_details = user_details.withColumn("last_calculated_at", lit(self.utc_cutoff_ts))
        return user_details.drop("name", "email")

    def write_trading_competition_data(self, user_details):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config['collection'] = self.config["data_store"]["trading_competition"]["collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
        mongo_write_config = {
            "uri": mongo_uri,
            "collection": self.config["data_store"]["trading_competition"]["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        self.io_utils.write_dataset_to_mongo(user_details, mongo_write_config,
                                             "trading_competition_mongo_write", "update",
                                             "{'userId':1,'tradingCompetitionId':1}", add_created_at=False)

    def execute(self):
        user_details = self.build_user_properties()
        self.write_trading_competition_data(user_details)

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)

