import redis
from src.utils.spark_utils import *



config = trading_competition_config.job_config_data
class RedisWrite:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.redis_client = get_redis_client()
        self.max_retries = 4
        self.retry_delay = 2 # seconds
        self.ttl = config["redis_ttl"]  # 7 hours
        self.spark = spark_session_create("trading_competition_redis_write")
        logging.info("trading_competition_redis_write spark session created")

    def get_forex_price(self, t_1, h_1):
        """
        Fetches the mid_price for a specific forex and partner ID.

        Returns:
            int: The mid_price value.

        Raises:
            ValueError: If the data is empty or the required key is missing.
            FileNotFoundError: If the input path is incorrect or inaccessible.
            Exception: For other unexpected issues.
        """
        try:
            # Read JSON data from S3
            input_path = "s3a://{}/{}/dt={}/hour={}/".format(config_data["bucket"], config["prices"]["forex"]["price_path"], t_1, h_1)
            forex_partner_price = self.spark.read.json(input_path)

            # Filter data
            forex_partner_price = forex_partner_price.filter(
                (col("forex_id") == 10000) & (col("partner_id") == config["partner_id"])
            )

            # Collect the filtered data
            collected_data = forex_partner_price.collect()

            # Check if data exists
            if not collected_data:
                raise ValueError(f'No data found for forex_id=10000 and partner_id={config["partner_id"]}.')

            # Extract mid_price
            mid_price = collected_data[0]["mid_price"]
            if mid_price is None:
                raise ValueError("The 'mid_price' key is missing in the data.")

            return int(mid_price)

        except AnalysisException as ae:
            raise FileNotFoundError(f"Failed to read input path {input_path}. Error: {ae}") from ae

        except ValueError as ve:
            raise ve  # Re-raise the ValueError with the same message

        except Exception as e:
            raise Exception(f"An unexpected error occurred: {e}") from e

    def get_last_update_datetime(self):
        t_1, h_1, t_2, h_2, ts_1, ts_2 = get_dates_and_timestamps(self.utc_cutoff_ts)
        return ts_1

    def update_key(self, data):
        for key, value in data.items():
            retries = 0
            success = False  # Track if the key was successfully set
            while retries < self.max_retries and not success:
                try:
                    # Log the existing value for the key if it exists
                    existing_value = self.redis_client.get(key)
                    if existing_value is not None:
                        logging.info(f"Existing value for key '{key}': {existing_value.decode('utf-8')}")
                    else:
                        logging.info(f"No existing value for key '{key}'.")

                    # Set the key with value and TTL
                    self.redis_client.set(key, value, ex=self.ttl)
                    logging.info(f"Key '{key}' set successfully with value {value} and TTL {self.ttl} seconds.")
                    success = True  # Mark as successful to exit the retry loop
                except (redis.ConnectionError, TimeoutError, redis.exceptions.RedisError) as e:
                    retries += 1
                    logging.info(f"Failed to set key '{key}'. Retrying {retries}/{self.max_retries}...")
                    if retries == self.max_retries:
                        logging.exception(f"Max retries reached for key '{key}'. Error: {e}")
                    else:
                        time.sleep(self.retry_delay)

    def delete_keys(self, keys_to_delete):
        logging.info("Delete Started Now")
        retries = 0
        # Retry logic for deleting keys
        for key in keys_to_delete:
            success = False  # Track if the key was successfully set
            logging.info(f"Deleting key: {key}")
            while retries < self.max_retries and not success:
                try:
                    if  "*" in key: # If the key contains '*', treat it as a pattern
                        logging.info(f"Deleting nested key:")
                        matched_keys = self.redis_client.keys(key)
                        logging.info(f"Matched Keys are : {matched_keys}")
                        if matched_keys:
                            # Attempt to delete the keys
                            for k in matched_keys:
                                r = self.redis_client.delete(k)
                                logging.info(f"Delete key response: {r}")
                            logging.info(f"Deleted keys matching pattern: {matched_keys}")
                        else:
                            logging.info(f"No key found for pattern: {matched_keys}")
                    else:
                        if self.redis_client.exists(key):
                            self.redis_client.delete(key)
                            logging.info(f"Deleted specific key: {key}")
                        else:
                            logging.info(f"No key found: {key}")
                    success = True  # Mark as successful to exit the retry loop
                except (redis.ConnectionError, TimeoutError, redis.exceptions.RedisError) as e:
                    retries += 1
                    logging.info(f"Failed to delete key '{key}'. Retrying {retries}/{self.max_retries}...")
                    if retries == self.max_retries:
                        logging.exception(f"Max retries reached for keys '{keys_to_delete}'. Error: {e}")
                    else:
                        logging.info("Retrying...")
                        time.sleep(self.retry_delay)


    def start_processing(self):
        t_1, h_1, t_2, h_2, ts_1, ts_2 = get_dates_and_timestamps(self.utc_cutoff_ts)
        forex_price = self.get_forex_price(t_1, h_1)
        self.update_key({config["redis_write_keys"][0]: forex_price, config["redis_write_keys"][1]: ts_1})
        self.delete_keys(config["redis_delete_keys"])



if __name__ == "__main__":
    logging.info("Main Started Now")
    start_time= datetime.now()
    parser = argparse.ArgumentParser()
    parser.add_argument("--utc_cutoff_ts", help="utc timestamp for redis write")
    args = parser.parse_args()
    if args.utc_cutoff_ts:
        utc_cutoff_ts = get_utc_timestamp_from_string(args.utc_cutoff_ts)
    obj = RedisWrite(utc_cutoff_ts)
    obj.start_processing()
    end_time = datetime.now()
    job_time_metrics(start_time, end_time, "trading_competition_redis_write")