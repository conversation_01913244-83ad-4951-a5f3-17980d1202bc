from src.utils.spark_utils import *

schema_for_trading_competition_user_events = StructType(
    [
        StructField("accountId", LongType(), True),
        StructField("userId", LongType(), True),
        StructField("name", StringType(), True),
        StructField("email", StringType(), True),
        StructField("tradingCompetitionId", StringType(), True),
        StructField("previousTier", StringType(), True),
        StructField("currentTier", StringType(), True),
        StructField("eligibleUpgradeTier", StringType(), True),
        StructField("eventTime", TimestampType(), True),
        StructField("userAction", StringType(), True)
    ]
)


schema_for_batch = ArrayType(StructType([
        StructField("user_id", LongType(), True),
        StructField("transaction_id", LongType(), True),
        StructField("asset_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_sub_type", StringType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("transaction_time", TimestampType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("leverage", LongType(), True),
        StructField("status", StringType(), True),
        StructField("fees", DoubleType(), True),
        StructField("executed_total_price", DoubleType(), True),
        StructField("executed_quantity", DoubleType(), True),
        StructField("executed_unit_price", DoubleType(), True),
        StructField("updated_executed_quantity", DoubleType(), True),
        StructField("updated_executed_unit_price", DoubleType(), True),
        StructField("currency_to_idr", DoubleType(), True),
        StructField("current_unit_price", DoubleType(), True),
        StructField("current_currency_to_idr", LongType(), True),
        StructField("forex_price", LongType(), True),
        StructField("usdt_price", LongType(), True),
        StructField("remaining_quantity", DoubleType(), True),
        StructField("realized_pnl", LongType(), True),
        StructField("unrealized_pnl", LongType(), True),
        StructField("execution_time", TimestampType(), True),
        StructField("trading_competition_start_time", TimestampType(), True),
        StructField("trading_competition_id", StringType(), True),
        StructField("row_number", IntegerType(), True),
        StructField("ignore_for_gtv", BooleanType(), True)

])
)

schema_for_all_transactions = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("transaction_id", LongType(), True),
        StructField("asset_id", LongType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("executed_quantity", DoubleType(), True),
        StructField("executed_unit_price", DoubleType(), True),
        StructField("updated_executed_quantity", DoubleType(), True),
        StructField("updated_executed_unit_price", DoubleType(), True),
        StructField("executed_total_price", DoubleType(), True),
        StructField("fees", DoubleType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("status", StringType(), True),
        StructField("currency_to_idr", DoubleType(), True),
        StructField("transaction_time", TimestampType(), True),
        StructField("leverage", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_sub_type", StringType(), True),
        StructField("current_unit_price", DoubleType(), True),
        StructField("current_currency_to_idr", LongType(), True),
        StructField("ignore_for_gtv", BooleanType(), True),
        StructField("forex_price", LongType(), True),
        StructField("usdt_price", LongType(), True)
]
)

schema_for_batch_flash_games = ArrayType(StructType([
        StructField("user_id", LongType(), True),
        StructField("transaction_id", LongType(), True),
        StructField("asset_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_sub_type", StringType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("transaction_time", TimestampType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("leverage", LongType(), True),
        StructField("updated_executed_quantity", DoubleType(), True),
        StructField("updated_executed_unit_price", DoubleType(), True),
        StructField("currency_to_idr", DoubleType(), True),
        StructField("remaining_quantity", DoubleType(), True),
        StructField("realized_pnl", LongType(), True),
        StructField("unrealized_pnl", LongType(), True),
        StructField("execution_time", TimestampType(), True),
        StructField("trading_competition_start_time", TimestampType(), True),
        StructField("trading_competition_id", StringType(), True),
        StructField("flash_game_id", StringType(), True),
        StructField("row_number", IntegerType(), True),
        StructField("is_pnl_eligible", BooleanType(), True),

])
)