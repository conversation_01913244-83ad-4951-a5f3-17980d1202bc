from src.utils.spark_utils import *


class PhGlobalStockDividend:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("ph_global_stock_dividend")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.logger.info("Ph Global Stock Dividend initialised successfully with t_1: {}, t_2: {}, cutoff_ts: {}".format(self.t_1, self.t_2, self.config["cutoff_ts"]))

    def get_global_stock_accounts(self):
        t_1_accounts_files_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.ph_global_stock_accounts_raw_data, self.t_1)
        t_2_accounts_file_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.ph_global_stock_accounts, self.t_2)

        self.logger.info("Reading raw accounts data from path: {}".format(t_1_accounts_files_path))
        raw_accounts = self.io_utils.read_json_data(t_1_accounts_files_path, is_raw=True, return_empty_if_file_not_present=True)

        self.logger.info("Reading accounts from path: {}".format(t_2_accounts_file_path))
        prev_accounts = self.io_utils.read_csv_file(t_2_accounts_file_path, None, False)

        if raw_accounts is None:
            accounts = prev_accounts
        else:
            raw_accounts = self.ops.apply_schema_from(raw_accounts, prev_accounts)
            raw_accounts = raw_accounts.filter(col("updated") <= self.config["cutoff_ts"])
            accounts = prev_accounts.union(raw_accounts)
        accounts = self.ops.de_dupe_dataframe(accounts, ["account_id", "global_stock_id"], "updated")
        self.logger.info("Successfully created accounts snapshot for dividend")
        return accounts

    def get_global_stock_returns(self):
        t_1_returns_files_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.ph_global_stock_returns_raw_data, self.t_1)
        t_2_returns_file_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.ph_global_stock_returns, self.t_2)

        self.logger.info("Reading raw returns data from path: {}".format(t_1_returns_files_path))
        raw_returns = self.io_utils.read_json_data(t_1_returns_files_path, is_raw=True, return_empty_if_file_not_present=True)

        self.logger.info("Reading returns from path: {}".format(t_2_returns_file_path))
        prev_returns = self.io_utils.read_csv_file(t_2_returns_file_path, None, False)

        if raw_returns is None:
            returns = prev_returns
        else:
            raw_returns = self.ops.apply_schema_from(raw_returns, prev_returns)
            raw_returns = raw_returns.filter(col("updated") <= self.config["cutoff_ts"])
            returns = prev_returns.union(raw_returns)
        returns = self.ops.de_dupe_dataframe(returns, ["account_id", "global_stock_id"], "updated")
        self.logger.info("Successfully created returns snapshot for dividend")
        return returns

    def calculate_global_stock_dividend(self):
        self.logger.info("Starting execution for global stocks dividend generation")
        accounts = self.get_global_stock_accounts()
        accounts = accounts.select("account_id", "user_id", "global_stock_id", "balance", "pending_reward_balance", "reward_balance", "total_reward_balance")

        returns = self.get_global_stock_returns()
        returns = returns.select("account_id", "user_id", "global_stock_id", "total_quantity", "batch_quantity")

        self.logger.info("Joining accounts and returns for final dividend record")
        all_accounts = accounts.join(returns, on=["account_id", "user_id", "global_stock_id"], how="left").fillna(0)

        all_accounts = all_accounts.filter(col("total_quantity") > 0)
        all_accounts = all_accounts.withColumn("_global_stock_id", col("global_stock_id"))
        all_accounts = all_accounts.withColumn("created", lit(DateUtils.get_utc_timestamp())) \
            .withColumn("pocket_return_details", F.array()) \
            .withColumn("adp_return_quantity", col("total_quantity"))

        dividend_path = "{}/{}/dt={}".format(self.bucket_path, S3Paths.ph_global_stock_dividend_folder, self.t_2)
        all_accounts.coalesce(1).write.mode('overwrite').partitionBy("_global_stock_id").json(dividend_path)
        self.logger.info("Successfully written dividend records in s3: {}".format(dividend_path))
        return all_accounts

    def run(self):
        self.calculate_global_stock_dividend()
