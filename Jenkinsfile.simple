pipeline {
    agent any
    
    environment {
        // Adjust these paths according to your Jenkins environment
        JAVA_HOME = '/usr/lib/jvm/java-11-openjdk-amd64'
        PATH = "${JAVA_HOME}/bin:${PATH}"
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo "🔄 Checking out code from ${env.BRANCH_NAME} branch"
                checkout scm
            }
        }
        
        stage('Pre-Deployment Tests') {
            when {
                branch 'master'  // Only run tests when deploying master branch
            }
            steps {
                script {
                    echo "🧪 Running pre-deployment tests for master branch..."
                    
                    // Make the script executable
                    sh 'chmod +x scripts/run-pre-deployment-tests.sh'
                    
                    // Run the pre-deployment tests
                    sh './scripts/run-pre-deployment-tests.sh'
                }
            }
            post {
                always {
                    // Publish test results if they exist
                    script {
                        if (fileExists('test-results/test-results.xml')) {
                            publishTestResults testResultsPattern: 'test-results/test-results.xml'
                        }
                        
                        if (fileExists('coverage/coverage.xml')) {
                            publishCoverage adapters: [
                                coberturaAdapter('coverage/coverage.xml')
                            ], sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
                        }
                        
                        // Archive test artifacts
                        archiveArtifacts artifacts: 'test-results/**, coverage/**', allowEmptyArchive: true
                    }
                }
                failure {
                    echo "❌ Pre-deployment tests failed! Deployment will be aborted."
                    
                    // Optional: Send notification
                    script {
                        try {
                            emailext (
                                subject: "❌ Pre-deployment tests failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                                body: """
                                Pre-deployment tests failed for master branch deployment.
                                
                                Job: ${env.JOB_NAME}
                                Build: ${env.BUILD_NUMBER}
                                Branch: ${env.BRANCH_NAME}
                                Commit: ${env.GIT_COMMIT}
                                
                                Please check the test results and fix the issues before deploying.
                                
                                Build URL: ${env.BUILD_URL}
                                Test Results: ${env.BUILD_URL}testReport/
                                """,
                                to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
                            )
                        } catch (Exception e) {
                            echo "Could not send email notification: ${e.getMessage()}"
                        }
                    }
                }
                success {
                    echo "✅ All pre-deployment tests passed! Proceeding with deployment."
                }
            }
        }
        
        stage('Deploy to Production') {
            when {
                allOf {
                    branch 'master'
                    expression { currentBuild.currentResult == 'SUCCESS' }
                }
            }
            steps {
                script {
                    echo "🚀 Starting deployment to production..."
                    
                    // Replace these with your actual deployment steps
                    sh '''
                        echo "=== DEPLOYMENT STEPS ==="
                        echo "Deploying PluangSparkBatchProcessingJobs to production..."
                        
                        # Example deployment steps - customize these for your environment:
                        
                        # 1. Create deployment package
                        echo "📦 Creating deployment package..."
                        tar -czf pluang-spark-jobs-${BUILD_NUMBER}.tar.gz \
                            --exclude='.git' \
                            --exclude='test*' \
                            --exclude='venv' \
                            --exclude='coverage' \
                            --exclude='*.pyc' \
                            --exclude='__pycache__' \
                            .
                        
                        # 2. Copy to deployment server (example)
                        # scp pluang-spark-jobs-${BUILD_NUMBER}.tar.gz user@prod-server:/opt/deployments/
                        
                        # 3. Deploy to Spark cluster (example)
                        # ssh user@spark-master "cd /opt/spark && ./bin/spark-submit --master yarn --deploy-mode cluster /path/to/jobs/flash_games_pnl.py"
                        
                        # 4. Update configuration files (example)
                        # kubectl apply -f k8s/production/
                        
                        # 5. Restart services (example)
                        # systemctl restart spark-batch-jobs
                        
                        echo "✅ Deployment completed successfully!"
                    '''
                }
            }
            post {
                success {
                    echo "🎉 Deployment to production completed successfully!"
                    
                    // Send success notification
                    script {
                        try {
                            emailext (
                                subject: "✅ Production deployment successful: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                                body: """
                                Production deployment completed successfully!
                                
                                Job: ${env.JOB_NAME}
                                Build: ${env.BUILD_NUMBER}
                                Branch: ${env.BRANCH_NAME}
                                Commit: ${env.GIT_COMMIT}
                                
                                All tests passed and deployment is complete.
                                
                                Build URL: ${env.BUILD_URL}
                                """,
                                to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
                            )
                        } catch (Exception e) {
                            echo "Could not send email notification: ${e.getMessage()}"
                        }
                    }
                }
                failure {
                    echo "❌ Deployment to production failed!"
                    
                    // Send failure notification
                    script {
                        try {
                            emailext (
                                subject: "❌ Production deployment failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                                body: """
                                Production deployment failed!
                                
                                Job: ${env.JOB_NAME}
                                Build: ${env.BUILD_NUMBER}
                                Branch: ${env.BRANCH_NAME}
                                Commit: ${env.GIT_COMMIT}
                                
                                Please check the deployment logs and fix the issues.
                                
                                Build URL: ${env.BUILD_URL}
                                """,
                                to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
                            )
                        } catch (Exception e) {
                            echo "Could not send email notification: ${e.getMessage()}"
                        }
                    }
                }
            }
        }
    }
    
    post {
        always {
            // Clean up
            sh '''
                # Clean up virtual environment and temporary files
                rm -rf venv/
                rm -f *.tar.gz
            '''
            
            // Clean workspace
            cleanWs(cleanWhenNotBuilt: false,
                    deleteDirs: true,
                    disableDeferredWipeout: true,
                    notFailBuild: true)
        }
        success {
            echo "🎉 Pipeline completed successfully!"
        }
        failure {
            echo "💥 Pipeline failed!"
        }
    }
}
