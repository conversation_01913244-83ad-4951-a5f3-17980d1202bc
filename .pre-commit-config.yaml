# .pre-commit-config.yaml

repos:
  - repo: local
    hooks:
      - id: pylint
        name: pylint
        entry: "pylint -j 0 --score y --fail-under=7.5"
        language: system
        types: [ python ]
        exclude: ^venv/
        args:
          [
            "-rn", # Only display messages
            "--rcfile=.pylintrc", # Link to your config file
            "--load-plugins=pylint.extensions.docparams", # Load an extension
          ]
