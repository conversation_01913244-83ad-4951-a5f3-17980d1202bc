from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException
from py4j.java_gateway import java_import

spark = SparkSession.builder \
    .appName("Read Snappy Parquet") \
    .getOrCreate()

# Replace with your actual file path
csv_file_path = "/Users/<USER>/Downloads/master_batch"
df = spark.read.option("recursiveFileLookup", "true").csv(csv_file_path, header=True, inferSchema=True,
                                     quote='"', escape='"', multiLine=True)
# df = spark.read.option("recursiveFileLookup", "true").parquet(parquet_file_path)
df = df.filter(df["account_id"].isin([********])).orderBy("transaction_time")

df.coalesce(1).write.mode('overwrite').csv('/Users/<USER>/Desktop/playground/resources/master_batch', header=True)
# # Show schema or data
df.printSchema()
print(f"df count is {df.count()}")
df.show(truncate=False)

